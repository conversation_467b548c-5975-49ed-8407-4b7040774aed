<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.et.dal.mysql.seriescontracts.SeriesContractsMapper">

    <select id="selectSqlPage" resultType="cn.iocoder.yudao.module.et.dal.dataobject.seriescontracts.SeriesContractsDO">

        SELECT jsc.*, quantity, price
        FROM jy_series_contracts jsc
        left join (select sum(quantity) quantity, sum(quantity * price) price, series_contracts_id
        from jy_contracts
        group by series_contracts_id) jc on jsc.id = jc.series_contracts_id
        <where>
            <if test="reqVO.name != null and reqVO.name != ''">
                AND name LIKE CONCAT('%', #{reqVO.name}, '%')
            </if>
            <if test="reqVO.contractsTypeId != null">
                AND contracts_type_id = #{reqVO.contractsTypeId}
            </if>
            <if test="reqVO.minQuantity != null">
                AND jc.quantity &gt;= #{reqVO.minQuantity}
            </if>
            <if test="reqVO.maxQuantity != null">
                AND jc.quantity &lt;= #{reqVO.maxQuantity}
            </if>
            <if test="reqVO.minPrice != null">
                AND jc.price &gt;= #{reqVO.minPrice}
            </if>
            <if test="reqVO.maxPrice != null">
                AND jc.price &lt;= #{reqVO.maxPrice}
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>