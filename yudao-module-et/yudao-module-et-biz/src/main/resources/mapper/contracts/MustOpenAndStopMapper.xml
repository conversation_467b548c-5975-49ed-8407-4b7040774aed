<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.et.dal.mysql.mustOpenAndStopUnit.MustOpenAndStopUnitMapper">


    <select id="getBiKaiBiTing"
            resultType="cn.iocoder.yudao.module.et.dal.dataobject.mustOpenAndStopUnit.StartupAndDownUnitDO">


        select a.power_plant_name,
               b.unit_abbreviation,
               b.run_day as ts,
               b.rated_capacity,
               b.non_heat_addition_must_open_unit,
               b.non_heat_addition_must_open_reason,
               b.non_heat_addition_must_open_unit_number,
               b.non_heat_addition_must_stop_unit_number,
               b.heat_addition_must_open_unit_number,
               b.heat_addition_must_stop_unit_number,
               b.non_heat_addition_must_stop_unit,
               b.non_heat_addition_must_stop_reason,
               b.heat_addition_must_open_unit,
               b.heat_addition_must_open_reason,
               b.heat_addition_must_stop_unit,
               b.heat_addition_must_stop_reason
        from jy_power_plant_base_information a
                 left join jy_startup_and_down_unit b on a.id = b.power_plant_id
        where b.deleted = 0
          <if test="bo.tsDate != null and bo.tsDate != ''">
              and date_format(b.run_day, '%Y-%m-%d') = #{bo.tsDate}
          </if>
          <if test="bo.name != null and bo.name != ''">
              and a.power_plant_name = #{bo.name}
          </if>

    </select>
    <select id="getPowerPlantNames"
            resultType="map">
        select distinct power_plant_name
        from jy_power_plant_base_information
    </select>
</mapper>