package cn.iocoder.yudao.module.et.dal.dataobject.rollingMatchingTransactions;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 市场交易信息实体类
 */
@TableName("jy_market_trading_info")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JyMarketTradingInfoDO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 申报日
     */
    private String bidDate;

    /**
     * 标的日
     */
    private String targetDate;

    /**
     * 交易名称
     */
    private String tradeSeq;

    /**
     * 时段
     */
    private String timeSlot;

    /**
     * 总交易量
     */
    private BigDecimal totalQuantity;

    /**
     * 最高价
     */
    private BigDecimal maxPrice;

    /**
     * 最低价
     */
    private BigDecimal minPrice;

    /**
     * 加权价格
     */
    private BigDecimal avgPrice;

    /**
     * 中位数价格
     */
    private BigDecimal medianPrice;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;

    /**
     * 调度单元编码（弃用）
     */
    private String dispatchNodeCode;

    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherTime;

    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;

    /**
     * 交易单元ID
     */
    private Long tradingUnitId;
}
