package cn.iocoder.yudao.module.et.dal.mysql.contractsmember;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.et.controller.admin.contractsmember.vo.ContractsMemberPageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsmember.ContractsMemberDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 合同方信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("shardingsphereDB")
public interface ContractsMemberMapper extends BaseMapperX<ContractsMemberDO> {

    default PageResult<ContractsMemberDO> selectPage(ContractsMemberPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContractsMemberDO>()
                .likeIfPresent(ContractsMemberDO::getSellerName, reqVO.getSellerName())
                .likeIfPresent(ContractsMemberDO::getSellerTradingUnitName, reqVO.getSellerTradingUnitName())
                .eqIfPresent(ContractsMemberDO::getSellerPrice, reqVO.getSellerPrice())
                .eqIfPresent(ContractsMemberDO::getSellerQuantity, reqVO.getSellerQuantity())
                .eqIfPresent(ContractsMemberDO::getSellerCoefficient, reqVO.getSellerCoefficient())
                .eqIfPresent(ContractsMemberDO::getSellerTradingUnitId, reqVO.getSellerTradingUnitId())
                .betweenIfPresent(ContractsMemberDO::getGridTime, reqVO.getGridTime())
                .likeIfPresent(ContractsMemberDO::getPurchaserName, reqVO.getPurchaserName())
                .likeIfPresent(ContractsMemberDO::getPurchaserTradingUnitName, reqVO.getPurchaserTradingUnitName())
                .eqIfPresent(ContractsMemberDO::getPurchaserPrice, reqVO.getPurchaserPrice())
                .eqIfPresent(ContractsMemberDO::getPurchaserQuantity, reqVO.getPurchaserQuantity())
                .eqIfPresent(ContractsMemberDO::getPurchaserCoefficient, reqVO.getPurchaserCoefficient())
                .eqIfPresent(ContractsMemberDO::getPurchaserTradingUnitId, reqVO.getPurchaserTradingUnitId())
                .eqIfPresent(ContractsMemberDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(ContractsMemberDO::getOrgCode, reqVO.getOrgCode())
                .betweenIfPresent(ContractsMemberDO::getGatherDate, reqVO.getGatherDate())
                .betweenIfPresent(ContractsMemberDO::getModificationTime, reqVO.getModificationTime())
                .eqIfPresent(ContractsMemberDO::getContractsId, reqVO.getContractsId())
                .orderByDesc(ContractsMemberDO::getId));
    }

    @Select("select * from jy_contracts_member where contracts_id = #{id} and deleted = 0")
    ContractsMemberDO selectByContractsId(String id);
}