package cn.iocoder.yudao.module.et.controller.admin.districtcode.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 行政区划分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DistrictCodePageReqVO extends PageParam {

    @Schema(description = "名字", example = "李四")
    private String name;

    @Schema(description = "等级:1-省级;2-地级市;3-区/县;4-乡/镇")
    private Integer level;

    @Schema(description = "类型:1-省;2-自治区;3-直辖市;4-特别行政区;5-地级市;6-地区;7-自治州;8-盟;9-市辖区;10-县;11- 县级市;12-自治县;13-旗;14-自治旗;15-特区;16-林区", example = "1")
    private Integer type;

    @Schema(description = "简称", example = "张三")
    private String abname;

    @Schema(description = "所属行政区划代码", example = "17855")
    private Integer pid;

    @Schema(description = "所属行政区划名字", example = "芋艿")
    private String pname;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "纬度")
    private Double lat;

    @Schema(description = "经度")
    private Double lng;

}