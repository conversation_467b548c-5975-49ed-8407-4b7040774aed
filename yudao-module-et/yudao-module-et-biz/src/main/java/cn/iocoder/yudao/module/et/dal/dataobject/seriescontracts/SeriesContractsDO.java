package cn.iocoder.yudao.module.et.dal.dataobject.seriescontracts;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 合同序列 DO
 *
 * <AUTHOR>
 */
@TableName("jy_series_contracts")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SeriesContractsDO {

    /**
     * 自增主键
     */
    @TableId
    private Long id;
    /**
     * 合同序列名称
     */
    private String name;
    /**
     * 合同类型id（主库合同类型表id）
     */
    private Long contractsTypeId;
    /**
     * 交易状态
     */
    private String status;
    /**
     * 交易发布
     */
    private String released;
    /**
     * 申报开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 申报截止时间
     */
    private LocalDateTime endTime;
    /**
     * 火电可申报(0:不可申报；1:可申报)
     */
    private Integer thermalPower;
    /**
     * 新能源申报(0:不可申报；1:可申报)
     */
    private Integer greenPower;
    /**
     * 售电公司可申报(0:不可申报；1:可申报)
     */
    private Integer powerSales;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherDate;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;

}