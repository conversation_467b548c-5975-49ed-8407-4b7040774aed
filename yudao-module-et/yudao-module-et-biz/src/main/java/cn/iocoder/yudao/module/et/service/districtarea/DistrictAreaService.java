package cn.iocoder.yudao.module.et.service.districtarea;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.et.controller.admin.districtarea.vo.DistrictAreaPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.districtarea.vo.DistrictAreaSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.districtarea.DistrictAreaDO;
import jakarta.validation.Valid;

/**
 * 省份对应算法类型 Service 接口
 *
 * <AUTHOR>
 */
public interface DistrictAreaService {

    /**
     * 创建省份对应算法类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createDistrictArea(@Valid DistrictAreaSaveReqVO createReqVO);

    /**
     * 更新省份对应算法类型
     *
     * @param updateReqVO 更新信息
     */
    void updateDistrictArea(@Valid DistrictAreaSaveReqVO updateReqVO);

    /**
     * 删除省份对应算法类型
     *
     * @param id 编号
     */
    void deleteDistrictArea(String id);

    /**
     * 获得省份对应算法类型
     *
     * @param id 编号
     * @return 省份对应算法类型
     */
    DistrictAreaDO getDistrictArea(String id);

    /**
     * 获得省份对应算法类型分页
     *
     * @param pageReqVO 分页查询
     * @return 省份对应算法类型分页
     */
    PageResult<DistrictAreaDO> getDistrictAreaPage(DistrictAreaPageReqVO pageReqVO);

    DistrictAreaDO selectByCode(String code);
}