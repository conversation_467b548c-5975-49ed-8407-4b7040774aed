package cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 交易单元 DO
 *
 * <AUTHOR>
 */
@TableName("jy_trading_unit")
@KeySequence("jy_trading_unit_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MysqlTradingUnitDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId
    private Integer id;
    /**
     * 调度单元编码
     */
    private String code;
    /**
     * 调度单元名称(简称)
     */
    private String name;
    /**
     * 调度单元全称
     */
    private String fullName;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 排序 0存量 1平价 2保障性平价
     */
    private Integer sort;
    /**
     * 场站装机容量
     */
    private Double capacity;
    /**
     * 场站设备台数
     */
    private Integer quantity;
    /**
     * 电力交易系统-省内账号
     */
    private String sysUser;
    /**
     * 电力交易系统-省内密码
     */
    private String sysPwd;
    /*
     * 电力交易系统-省间账号
     */
    private String sysUserBj;
    /**
     * 电力交易系统-省间密码
     */
    private String sysPwdBj;
    /**
     * ukey序列号
     */
    private String ukeySeq;
    /**
     * 是否禁用
     */
    private Boolean enabled;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    private Integer tenantId;

}