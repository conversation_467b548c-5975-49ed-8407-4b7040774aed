package cn.iocoder.yudao.module.et.dal.dataobject.contractsequipment;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合同机组信息 DO
 *
 * <AUTHOR>
 */
@TableName("jy_contracts_equipment")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractsEquipmentDO {

    /**
     * 自增主键
     */
    @TableId
    private Long id;
    /**
     * 合同角色
     */
    private String contractsRoleName;

    /**
     * 市场成员
     */
    private String marketParticipant;
    /**
     * 交易单元
     */
    private String tradingUnit;

    /**
     * 机组名称
     */
    private String equipmentName;
    /**
     * 机组类型
     */
    private String equipmentType;
    /**
     * 装机容量
     */
    private BigDecimal quantity;
    /**
     * 批复电价
     */
    private BigDecimal approvedPrice;
    /**
     * 机组电量
     */
    private BigDecimal equipmentGen;
    /**
     * 时段名称
     */
    private String timeSlotName;
    /**
     * 时段起止时间
     */
    private String timeSlotRange;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherDate;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
    /**
     * 开始日期
     */
    private LocalDateTime beginTime;
    /**
     * 结束日期
     */
    private LocalDateTime endTime;
    /**
     * 合同id
     */
    private String contractsId;

    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;

}