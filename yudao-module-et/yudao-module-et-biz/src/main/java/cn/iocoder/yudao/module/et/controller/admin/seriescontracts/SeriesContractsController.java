package cn.iocoder.yudao.module.et.controller.admin.seriescontracts;

import cn.hutool.core.convert.Convert;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.contracts.vo.ContractsRespVO;
import cn.iocoder.yudao.module.et.controller.admin.seriescontracts.vo.SeriesContractsPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.seriescontracts.vo.SeriesContractsRespVO;
import cn.iocoder.yudao.module.et.controller.admin.seriescontracts.vo.SeriesContractsSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contracts.ContractsDO;
import cn.iocoder.yudao.module.et.dal.dataobject.seriescontracts.SeriesContractsDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingUnitOrg.TradingUnitOrgDO;
import cn.iocoder.yudao.module.et.dal.mysql.tradingUnitOrg.TradingUnitOrgMapper;
import cn.iocoder.yudao.module.et.service.contracts.ContractsService;
import cn.iocoder.yudao.module.et.service.contractsmember.ContractsMemberService;
import cn.iocoder.yudao.module.et.service.seriescontracts.SeriesContractsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合同序列")
@RestController
@RequestMapping("/et/series-contracts")
@Validated
public class SeriesContractsController {

	@Resource
	private SeriesContractsService seriesContractsService;
	@Resource
	private ContractsService contractsService;
	@Resource
	private ContractsMemberService contractsMemberService;
	@Resource
	private TradingUnitOrgMapper tradingUnitOrgMapper;

	@PostMapping("/create")
	@Operation(summary = "创建合同序列")
	@PreAuthorize("@ss.hasPermission('jy:series-contracts:create')")
	public CommonResult<Long> createSeriesContracts(@Valid @RequestBody SeriesContractsSaveReqVO createReqVO) {
		return success(seriesContractsService.createSeriesContracts(createReqVO));
	}

	@PutMapping("/update")
	@Operation(summary = "更新合同序列")
	@PreAuthorize("@ss.hasPermission('jy:series-contracts:update')")
	public CommonResult<Boolean> updateSeriesContracts(@Valid @RequestBody SeriesContractsSaveReqVO updateReqVO) {
		seriesContractsService.updateSeriesContracts(updateReqVO);
		return success(true);
	}

	@DeleteMapping("/delete")
	@Operation(summary = "删除合同序列")
	@Parameter(name = "id", description = "编号", required = true)
	@PreAuthorize("@ss.hasPermission('jy:series-contracts:delete')")
	public CommonResult<Boolean> deleteSeriesContracts(@RequestParam("id") Long id) {
		seriesContractsService.deleteSeriesContracts(id);
		return success(true);
	}

	@GetMapping("/get")
	@Operation(summary = "获得合同序列")
	@Parameter(name = "id", description = "编号", required = true, example = "1024")
	@PreAuthorize("@ss.hasPermission('jy:series-contracts:query')")
	public CommonResult<SeriesContractsRespVO> getSeriesContracts(@RequestParam("id") Long id) {
		SeriesContractsDO seriesContracts = seriesContractsService.getSeriesContracts(id);
		return success(BeanUtils.toBean(seriesContracts, SeriesContractsRespVO.class));
	}

	@GetMapping("/getByName")
	@Operation(summary = "获得合同序列")
	@Parameter(name = "id", description = "编号", required = true, example = "1024")
	// @PreAuthorize("@ss.hasPermission('jy:series-contracts:query')")
	public CommonResult<List<SeriesContractsRespVO>> getSeriesContracts(@RequestParam("seriesName") String seriesName) {
		List<SeriesContractsDO> pageResult = seriesContractsService.selectByLikeName(seriesName);
		return success(BeanUtils.toBean(pageResult, SeriesContractsRespVO.class));
	}

    @PostMapping("/page")
    @Operation(summary = "获得合同序列分页")
    // @PreAuthorize("@ss.hasPermission('jy:series-contracts:query')")
    public CommonResult<PageResult<SeriesContractsRespVO>> getSeriesContractsPage(@Valid @RequestBody SeriesContractsPageReqVO pageReqVO) {
        PageResult<SeriesContractsDO> pageResult = seriesContractsService.getSeriesContractsPage(pageReqVO);
        PageResult<SeriesContractsRespVO> pageResult1 = BeanUtils.toBean(pageResult, SeriesContractsRespVO.class);
        pageResult1.getList().forEach(x -> {
            List<ContractsDO> contractsDOList = contractsService.selectBySeriesContractsId(x.getId());
//			contractsDOList.forEach(
//					c -> {
//						if (c == null) {
//							return;
//						}
//						BigDecimal quantity = BigDecimal.valueOf(Convert.toDouble(c.getQuantity(), 0d));
//						BigDecimal price = BigDecimal.valueOf(Convert.toDouble(c.getPrice(), 0d));
//						c.setPrice(BigDecimal.valueOf(quantity.multiply(price).doubleValue()));
//					}
//			);
            List<ContractsRespVO> contractsRespVOList = BeanUtils.toBean(contractsDOList, ContractsRespVO.class);
            Map<String, Double> childSum = new HashMap<>(2);
            childSum.put("price", contractsDOList.stream().mapToDouble(c -> Convert.toDouble(c.getPrice(), 0d)*Convert.toDouble(c.getQuantity(), 0d)).sum());
            childSum.put("quantity", contractsDOList.stream().mapToDouble(c -> Convert.toDouble(c.getQuantity(), 0d)).sum());
            x.setChildSum(childSum);
            contractsRespVOList.forEach(c -> {
                TradingUnitOrgDO tradingUnitOrgDO = tradingUnitOrgMapper.selectByCode(c.getOrgCode());
                if (tradingUnitOrgDO != null) {
                    c.setSellerName(tradingUnitOrgDO.getName());
                    c.setTradingCompany(tradingUnitOrgDO.getOfficialName());
                }
            });
            x.setChild(contractsRespVOList);
        });
        return success(pageResult1);
    }

	@GetMapping("/getAll")
	@Operation(summary = "获得合同序列分页")
	// @PreAuthorize("@ss.hasPermission('jy:series-contracts:query')")
	public CommonResult<List<SeriesContractsRespVO>> getSeriesContractsPage() {
		List<SeriesContractsDO> pageResult = seriesContractsService.selectAll();
		return success(BeanUtils.toBean(pageResult, SeriesContractsRespVO.class));
	}

	@GetMapping("/export-excel")
	@Operation(summary = "导出合同序列 Excel")
	@PreAuthorize("@ss.hasPermission('jy:series-contracts:export')")
	@ApiAccessLog(operateType = EXPORT)
	public void exportSeriesContractsExcel(@Valid SeriesContractsPageReqVO pageReqVO, HttpServletResponse response)
			throws IOException {
		pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
		List<SeriesContractsDO> list = seriesContractsService.getSeriesContractsPage(pageReqVO).getList();
		// 导出 Excel
		ExcelUtils.write(response, "合同序列.xls", "数据", SeriesContractsRespVO.class,
				BeanUtils.toBean(list, SeriesContractsRespVO.class));
	}

}