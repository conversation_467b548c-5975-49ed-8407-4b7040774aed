package cn.iocoder.yudao.module.et.controller.admin.dispatchnode.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 交易单元分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DispatchNodePageReqVO extends PageParam {

    @Schema(description = "调度单元编码")
    private String code;

    @Schema(description = "调度单元名称", example = "王五")
    private String name;

    @Schema(description = "调度单元全称")
    private String fullName;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "场站装机容量")
    private Double capacity;

    @Schema(description = "场站设备台数")
    private Integer quantity;

    @Schema(description = "二区现货系统账号")
    private String sysUser;

    @Schema(description = "二区现货系统密码")
    private String sysPwd;

    @Schema(description = "是否禁用")
    private Boolean enabled;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "部门ID", example = "26902")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

}