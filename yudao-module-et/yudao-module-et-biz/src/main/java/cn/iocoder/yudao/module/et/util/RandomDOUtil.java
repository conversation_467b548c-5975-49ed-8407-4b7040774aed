package cn.iocoder.yudao.module.et.util;

import cn.hutool.core.date.DateUtil;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.Random;

public class RandomDOUtil {


    public static void setRandomValues(Object obj) {
        if (obj == null) {
            return;
        }

        Field[] fields = obj.getClass().getDeclaredFields();
        Random random = new Random();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                if (field.getType().equals(String.class)) {
                    field.set(obj, "RandomString");
                } else if (field.getType().equals(int.class) || field.getType().equals(Integer.class)) {
                    field.set(obj, random.nextInt());
                } else if (field.getType().equals(double.class) || field.getType().equals(Double.class)) {
                    field.set(obj, random.nextDouble());
                } else if (field.getType().equals(boolean.class) || field.getType().equals(Boolean.class)) {
                    field.set(obj, random.nextBoolean());
                } else {
                    // 对于其他类型，可以添加更多的条件分支来处理
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * company_unit_
     * @param args
     */
    public static void main2(String[] args) {
        String jdbcUrl = "****************************************************************************";
        Random random = new Random();
        try (Connection conn = DriverManager.getConnection(jdbcUrl);
             Statement stmt = conn.createStatement()) {
            Long d = DateUtil.parse("2024-06-01 00:00:00").getTime();
            Long miniature15 = (long) (15 * 60 * 1000);
            //ZJN010101001_TradingUnit
            // 创建数据库和表结构
            String tranStr = "ZJN0101001,ZJN0101002,ZJN0101003," +
                    "ZJN0101," +
                    "ZJN0101004," +
                    "ZJN0101005";
            String[] tranArr = tranStr.split(",");
            for (int k = 0; k < tranArr.length; k++) {
                String comId = tranArr[k];
                // 插入数据
                for (int i = 0; i < 1; i++) {
                    Timestamp ts = new Timestamp(d + miniature15 * i);
                    String insertSql = "insert into company_unit_" + comId + " USING company_unit TAGS('" + comId + "')  values ('" + ts + "'";
                    for (int j = 0; j < 41; j++) {
                        insertSql += "," + random.nextFloat() * 10;
                    }
                    insertSql += ")";
                    stmt.addBatch(insertSql);
                }
                stmt.executeBatch();
                stmt.closeOnCompletion();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * trading_unit_
     * @param args
     */
    public static void main1(String[] args) {
        String jdbcUrl = "****************************************************************************";

        Random random = new Random();
        try (Connection conn = DriverManager.getConnection(jdbcUrl);
             Statement stmt = conn.createStatement()) {


            // 创建数据库和表结构
            String tranStr = "ZJN010100101,ZJN010100102,ZJN010100103," +
                    "ZJN010100104," +
                    "ZJN0101," +
                    "ZJN010100201," +
                    "ZJN010100202," +
                    "ZJN010100301," +
                    "ZJN010100401," +
                    "ZJN010100402," +
                    "ZJN010100403," +
                    "ZJN010100501," +
                    "ZJN010100502," +
                    "ZJN010100503";

            String[] tranArr = tranStr.split(",");
            for (int k = 0; k < tranArr.length; k++) {
                String tranId = tranArr[k];
                System.out.println(tranId);
                Long d = DateUtil.parse("2024-06-01 00:00:00").getTime();
                Long miniature15 = (long) (15 * 60 * 1000);
                //ZJN010101001_TradingUnit
                // 插入数据
                for (int i = 0; i < 1; i++) {
                    Timestamp ts = new Timestamp(d + miniature15 * i);
                    String insertSql = "insert into trading_unit_" + tranId + " USING trading_unit TAGS('" + tranId + "')  values ('" + ts + "'";
                    for (int j = 0; j < 69; j++) {
                        insertSql += "," + random.nextFloat() * 10;
                    }
                    insertSql += ")";
                    System.out.println(insertSql);
                    stmt.addBatch(insertSql);
                }
                stmt.executeBatch();
                stmt.clearBatch();
            }
            stmt.closeOnCompletion();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * substation_
     * @param args
     */
    public static void main(String[] args) {
        String jdbcUrl = "****************************************************************************";

        Random random = new Random();
        try (Connection conn = DriverManager.getConnection(jdbcUrl);
             Statement stmt = conn.createStatement()) {


            // 创建数据库和表结构
            String tranStr = "zjn0101";
//                    "ZJN010100104," +
//                    "ZJN0101," +
//                    "ZJN010100201," +
//                    "ZJN010100202," +
//                    "ZJN010100301," +
//                    "ZJN010100401," +
//                    "ZJN010100402," +
//                    "ZJN010100403," +
//                    "ZJN010100501," +
//                    "ZJN010100502," +
//                    "ZJN010100503";

            String[] tranArr = tranStr.split(",");
            for (int k = 0; k < tranArr.length; k++) {
                String tranId = tranArr[k];
                System.out.println(tranId);
                Long d = DateUtil.parse("2024-06-04 00:00:00").getTime();
                Long miniature15 = (long) (15 * 60 * 1000);
                //ZJN010101001_TradingUnit
                // 插入数据
                for (int i = 0; i < 96; i++) {
                    Timestamp ts = new Timestamp(d + miniature15 * i);
                    String insertSql = "insert into substation_" + tranId + " USING substation TAGS('" + tranId + "')  values ('" + ts + "'";
                    for (int j = 0; j < 24; j++) {
                        insertSql += "," + random.nextFloat() * 10;
                    }
                    insertSql += ")";
                    System.out.println(insertSql);
                    stmt.addBatch(insertSql);
                }
                stmt.executeBatch();
                stmt.clearBatch();
            }
            stmt.closeOnCompletion();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
