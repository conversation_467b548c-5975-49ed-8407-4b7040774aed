package cn.iocoder.yudao.module.et.dal.dataobject.priceforecastingtradingunit;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.sql.Timestamp;

/**
 * 各节点出清类信息DO
 */
@TableName("price_forecasting_trading_unit")
@KeySequence("price_forecasting_trading_unit_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PriceForecastingTradingUnitDO {
    private Timestamp ts;
    /**
     * 预测电价
     */
    private Float dayAheadPrice;
    /**
     * 预测电价
     */
    private Float realtimePrice;
    /**
     * 集团id
     */
    private Float companyId;

    /**
     * 变电站名称
     */
    private Float substationName;


}
