package cn.iocoder.yudao.module.et.controller.admin.contractsmember.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 合同方信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContractsMemberPageReqVO extends PageParam {

    @Schema(description = "售电方", example = "赵六")
    private String sellerName;

    @Schema(description = "售电单元", example = "李四")
    private String sellerTradingUnitName;

    @Schema(description = "售方电价", example = "4602")
    private BigDecimal sellerPrice;

    @Schema(description = "售方电量")
    private BigDecimal sellerQuantity;

    @Schema(description = "售方调整系数")
    private Double sellerCoefficient;

    @Schema(description = "售方市场主体id", example = "27271")
    private Long sellerTradingUnitId;

    @Schema(description = "并网时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] gridTime;

    @Schema(description = "购电方", example = "李四")
    private String purchaserName;

    @Schema(description = "购电单元", example = "赵六")
    private String purchaserTradingUnitName;

    @Schema(description = "购方电价", example = "15948")
    private BigDecimal purchaserPrice;

    @Schema(description = "购方电量")
    private BigDecimal purchaserQuantity;

    @Schema(description = "购方调整系数")
    private Double purchaserCoefficient;

    @Schema(description = "购方市场主体id", example = "17677")
    private Long purchaserTradingUnitId;

    @Schema(description = "部门ID", example = "1243")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] gatherDate;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] modificationTime;

    @Schema(description = "合同id", example = "32087")
    private String contractsId;

}