package cn.iocoder.yudao.module.et.dal.mysql.substation;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.dal.dataobject.substation.SubstationDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.et.controller.admin.substation.vo.*;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 日前实时电价统计 Mapper
 *
 * <AUTHOR>
 */
@DS("shardingsphereDB")
@Mapper
public interface SubstationMapper extends BaseMapperX<SubstationDO> {

    default PageResult<SubstationDO> selectPage(SubstationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SubstationDO>()
                .likeIfPresent(SubstationDO::getName, reqVO.getName())
                .likeIfPresent(SubstationDO::getFullname, reqVO.getFullname())
                .eqIfPresent(SubstationDO::getOrgCode, reqVO.getOrgCode())
                .likeIfPresent(SubstationDO::getTableName, reqVO.getTableName())
                .eqIfPresent(SubstationDO::getType, reqVO.getType())
                .betweenIfPresent(SubstationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SubstationDO::getId));
    }

    @Select("SELECT fullname,table_name FROM jy_substation WHERE deleted != 1")
    @TenantIgnore
    List<SubstationDO> selectAllName();

    @Select("SELECT fullname FROM jy_substation WHERE table_name = #{tableName} limit 1")
    @TenantIgnore
    String selectFullName(String tableName);



    @Select(" SELECT * FROM jy_substation WHERE deleted != 1 order by id asc")
    @TenantIgnore
    List<SubstationDO> selectAll();

    @Select(" SELECT * FROM jy_substation WHERE deleted != 1 and org_code IS NOT NULL")
    @TenantIgnore
    List<SubstationDO> selectActive();

    @Select(" SELECT * FROM jy_substation WHERE deleted != 1 and org_code = #{orgCode}")
    @TenantIgnore
    SubstationDO selectByOrgCode(String orgCode);


    @Select("SELECT * FROM jy_substation WHERE id = #{id} GROUP BY id")
    @TenantIgnore
    SubstationDO selectById (Long id);


    @Update("UPDATE jy_substation SET name = #{name},fullname = #{fullname},org_code = #{orgCode},type = #{type},table_name = #{tableName} WHERE id = #{id}")
    @TenantIgnore
    int updateSub(SubstationDO substationDO);


    // 根据 fullname 模糊查询
    @Select("SELECT * FROM jy_substation WHERE fullname LIKE CONCAT('%', #{fullname}, '%') ORDER BY ID ASC")
    List<SubstationDO> selectLikeNameByFullname( String fullname);

    // 根据 type 精确查询
    @Select("SELECT * FROM jy_substation WHERE type = #{type} ORDER BY ID ASC")
    List<SubstationDO> selectLikeNameByType( String type);

    // 根据 fullname 模糊查询和 type 精确查询
    @Select("SELECT * FROM jy_substation WHERE fullname LIKE CONCAT('%', #{fullname}, '%') AND type = #{type} ORDER BY ID ASC")
    List<SubstationDO> selectLikeNameByFullnameAndType( String fullname, String type);

    @Update("UPDATE jy_substation SET org_code = null WHERE id = #{id}")
    Integer updateNull(Long id);


}