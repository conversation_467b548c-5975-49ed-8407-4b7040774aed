[TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@e599448b, ts=2025-08-07 00:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@e8c3bd6b, ts=2025-08-07 00:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@ebee364b, ts=2025-08-07 00:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@ef18742b, ts=2025-08-07 01:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@f242ed0b, ts=2025-08-07 01:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@f56c79eb, ts=2025-08-07 01:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@f896f2cb, ts=2025-08-07 01:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@fbc130ab, ts=2025-08-07 02:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@feeba98b, ts=2025-08-07 02:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@216226b, ts=2025-08-07 02:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@53faf4b, ts=2025-08-07 02:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@869ed2b, ts=2025-08-07 03:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@b94660b, ts=2025-08-07 03:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@ebedeeb, ts=2025-08-07 03:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@11e957cb, ts=2025-08-07 03:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@151395ab, ts=2025-08-07 04:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@183d228b, ts=2025-08-07 04:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@1b679b6b, ts=2025-08-07 04:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@1e92144b, ts=2025-08-07 04:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@21bc522b, ts=2025-08-07 05:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@24e6cb0b, ts=2025-08-07 05:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@281057eb, ts=2025-08-07 05:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@9325f84f, ts=2025-08-07 05:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.026, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@1f5e71be, ts=2025-08-07 06:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.025, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@a925a9d1, ts=2025-08-07 06:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.017, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@25b3637e, ts=2025-08-07 06:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.025, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@b1eb2bed, ts=2025-08-07 06:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.024, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@2c072e3e, ts=2025-08-07 07:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.025, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@7b05a920, ts=2025-08-07 07:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.797, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@7e302200, ts=2025-08-07 07:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.797, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@f84c5f51, ts=2025-08-07 07:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.798, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@bada3e1a, ts=2025-08-07 08:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.791, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@3968e742, ts=2025-08-07 08:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.018, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@b5f6a0ef, ts=2025-08-07 08:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.026, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@544bcc7c, ts=2025-08-07 08:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.023, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@bc4b57af, ts=2025-08-07 09:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.026, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@a674853e, ts=2025-08-07 09:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.795, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@856523e2, ts=2025-08-07 09:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.799, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@23ba4f6f, ts=2025-08-07 09:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.796, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@c20f3ffc, ts=2025-08-07 10:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.793, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@b31d41be, ts=2025-08-07 10:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.795, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@da80a8da, ts=2025-08-07 10:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.791, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@427f830d, ts=2025-08-07 10:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.794, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@c0e9c8a4, ts=2025-08-07 11:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.801, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@bfc5fe3e, ts=2025-08-07 11:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.795, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@d50cee3c, ts=2025-08-07 11:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.793, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@df1c3b4f, ts=2025-08-07 11:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.026, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@f462f04d, ts=2025-08-07 12:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.024, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@3f7d5158, ts=2025-08-07 12:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.052, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@308b531a, ts=2025-08-07 12:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.054, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@45d24318, ts=2025-08-07 12:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.052, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@add1ce4b, ts=2025-08-07 13:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.055, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@4c26f9d8, ts=2025-08-07 13:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.052, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@3d340f9a, ts=2025-08-07 13:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.054, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@527aff98, ts=2025-08-07 13:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.052, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@55a53d78, ts=2025-08-07 14:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.052, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@87d0a69e, ts=2025-08-07 14:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.025, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@af340dba, ts=2025-08-07 14:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.021, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@a041237c, ts=2025-08-07 14:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.023, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@a36b615c, ts=2025-08-07 15:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.023, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@9479631e, ts=2025-08-07 15:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.025, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@bbdcca3a, ts=2025-08-07 15:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.021, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@adb451c, ts=2025-08-07 15:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.793, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@72dad04f, ts=2025-08-07 16:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.796, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@ecf621a0, ts=2025-08-07 16:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.797, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@23d119e, ts=2025-08-07 16:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.795, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@1784019c, ts=2025-08-07 16:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.793, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@891c85e, ts=2025-08-07 17:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.795, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@82ae05af, ts=2025-08-07 17:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.796, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@331ebc5a, ts=2025-08-07 17:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.791, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@edd758c2, ts=2025-08-07 17:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.799, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@31e0dc0, ts=2025-08-07 18:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.797, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@a173394d, ts=2025-08-07 18:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.794, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@6874aaed, ts=2025-08-07 18:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.013, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@6b9e37cd, ts=2025-08-07 18:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.013, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@d2f9652b, ts=2025-08-07 19:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@d623de0b, ts=2025-08-07 19:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@d94e56eb, ts=2025-08-07 19:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@dc78cfcb, ts=2025-08-07 19:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@dfa30dab, ts=2025-08-07 20:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@e2cc9a8b, ts=2025-08-07 20:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@e5f7136b, ts=2025-08-07 20:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@e9218c4b, ts=2025-08-07 20:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@ec4bca2b, ts=2025-08-07 21:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@ef76430b, ts=2025-08-07 21:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@f29fcfeb, ts=2025-08-07 21:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@f5ca48cb, ts=2025-08-07 21:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@f8f486ab, ts=2025-08-07 22:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@fc1eff8b, ts=2025-08-07 22:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@ff49786b, ts=2025-08-07 22:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@273054b, ts=2025-08-07 22:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@59d432b, ts=2025-08-07 23:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@8c7bc0b, ts=2025-08-07 23:15:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@bf234eb, ts=2025-08-07 23:30:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@f1cadcb, ts=2025-08-07 23:45:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null), TradingUnitDO(super=cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO@1246ebab, ts=2025-08-08 00:00:00.0, unitId=null, hisSettlementPricesLimit=--, realSettlementPrices=--, mediumLongTermSettlementCurves=0.0, selfPlan=--, prebalancePlan=--, reliabilityPlan=--, bilateralClearance=--, realPlan=--, realScadaPower=--, shortTermForecast=--, ultraShortTermForecast=--, crossProvincialSpotClearingOfElectricity=--, realBasisClearance=--, hisPositivePower=--, hisNegativePower=--, hisPositivePrices=--, hisNegativePrices=--, hisGenerateElectricityPlan=--, mediumLongTermPlan=--, hisProvinceClearance=--, hisMarketSettlementPrices=--, realMarketSettlementPrices=--, realPositivePower=--, realNegativePower=--, realPositivePrices=--, realNegativePrices=--, realProvinceClearance=--, crossProvincialPeakLoadBalancingPlan=--, crossProvincialPeakLoadBalancingReturn=--, realSettlementPower=--, emergencyInvokeStartUpCompensationPrices=--, thermalPowerStartUpCompensationPrices=--, deviationReviewPrices=--, recoveryOfExcessProfitsFromNewEnergyIssuancePrices=--, hisStandbyClearance=--, realStandbyClearance=--, mediumLongTermBlockagePrices=--, blockingRiskHedgingPrices=--, modifiedCompensationPrices=--, necessaryStartUpCompensationPrices=--, mediumLongTermFusion=--, hisSettlementFreePower=--, realSettlementFreePower=--, jiscoBpo=--, jiscoBilateralTransactions=--, reviewPower=--, fmergencyInvokeStartUpOnGridPower=--, adjustmentFrequencyAdditionEnergyCompensation=--, adjustmentFrequencyLessenEnergyCompensation=--, unplannedOutageAssessment=--, whetherSubstitutionIsAllowed=--, deviationRate=--, tmrPower=--, scadaPower=--, realUsePower=--, calibrationPower=--, settlementPower=--, radixPower=--, deliveryPower=--, bilateralPower=--, dayScrollPower=--, longTermContractPower=--, radixPrice=--, deliveryPrice=--, bilateralPrice=--, dayScrollPrice=--, longTermContractPrice=--, shortTermForecastGen=--, capacity=null)] 
