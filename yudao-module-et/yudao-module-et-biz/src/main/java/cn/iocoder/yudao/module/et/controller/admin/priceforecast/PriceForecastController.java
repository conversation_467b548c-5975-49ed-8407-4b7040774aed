package cn.iocoder.yudao.module.et.controller.admin.priceforecast;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.et.controller.admin.priceforecast.vo.PriceForecastRespVO;
import cn.iocoder.yudao.module.et.service.priceforecast.PriceForecastService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "市场预测 - 价格预测")
@RestController
@RequestMapping("/et/priceForecast")
@Validated
public class PriceForecastController {

    @Resource
    private PriceForecastService priceForecastService;

    @PostMapping("/getPriceForecastAnalysis")
    @Operation(summary = "市场预测-价格预测分析")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, Object>> getPriceForecastAnalysis(@RequestBody PriceForecastRespVO respVO) {
        Map<String, Object> resultMap = priceForecastService.getPriceForecastAnalysis(respVO.getDate(), respVO.getSimilarDate(), respVO.getIds());
        return success(resultMap);
    }

    /**
     * 市场预测-电价预测
     */
    @PostMapping("/getPriceForecast")
    @Operation(summary = "市场预测-电价预测")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, Object>> getPriceForecast(@RequestBody PriceForecastRespVO respVO) {
        Map<String, Object> resultMap = priceForecastService.getPriceForecast(respVO.getDate(), respVO.getIds());
        return success(resultMap);
    }

    @PostMapping("/exportPrice")
    @Operation(summary = "市场预测-电价预测曲线数据导出")
    //@PreAuthorize("@ss.hasPermission('et:tradingUnit_settlement_release_sum:query')")
    public void exportPrice(HttpServletResponse response, HttpServletRequest request, @RequestBody PriceForecastRespVO respVO) {
        priceForecastService.exportPrice(response, request, respVO.getDate(),respVO.getIds());
    }
}