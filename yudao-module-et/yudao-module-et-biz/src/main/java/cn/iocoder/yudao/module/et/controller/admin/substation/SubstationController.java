package cn.iocoder.yudao.module.et.controller.admin.substation;


import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.substation.vo.SubstationPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.substation.vo.SubstationRespVO;
import cn.iocoder.yudao.module.et.controller.admin.substation.vo.SubstationSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.substation.SubstationDO;
import cn.iocoder.yudao.module.et.service.substation.SubstationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 日前实时电价统计")
@RestController
@RequestMapping("/et/substation")
@Validated
public class SubstationController {

    @Resource
    private SubstationService substationService;

    @PostMapping("/create")
    public CommonResult<Long> createSubstation(@RequestParam("fullname") String fullname,
                                               @RequestParam("name") String name,
                                               @RequestParam("orgCode") String orgCode,
                                               @RequestParam("tableName") String tableName,
                                               @RequestParam("type") String type) {
        return success(substationService.createSubstation(fullname, name, tableName, type, orgCode));
    }

    @PutMapping("/update")
    @Operation(summary = "更新日前实时电价统计")
    public CommonResult<Boolean> updateSubstation(@RequestBody SubstationSaveReqVO updateReqVO) {
        substationService.updateSubstation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除日前实时电价统计")
    @Parameter(name = "id", description = "编号", required = true)

    public CommonResult<Boolean> deleteSubstation(@RequestParam("id") Long id) {
        substationService.deleteSubstation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得日前实时电价统计")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")

    public CommonResult<SubstationRespVO> getSubstation(@RequestParam("id") Long id) {
        SubstationDO substation = substationService.getSubstation(id);
        return success(BeanUtils.toBean(substation, SubstationRespVO.class));
    }


    @GetMapping("/getSubstations")
    @Operation(summary = "获得变电站类型列表")

    public CommonResult<List<String>> getSubstation() {
        return success(substationService.getSubstations());
    }

    @GetMapping("/getSubstationName")
    @Operation(summary = "获得变电站名称列表")
    public CommonResult<List<SubstationDO>> getSubstationName() {
        return success(substationService.getSubstationName());
    }

    @GetMapping("/getTimeTableData")
    @Operation(summary = "获得变电站名称列表")
    public CommonResult<List<Map<String, Object>>> getTimeTableData(@RequestParam("date") String date, @RequestParam("type") String type, @RequestParam("bdType") String bdType, @RequestParam("ywType") String ywType) {
        try {
            // 将传入的date字符串按逗号分割
            String[] split = date.split(",");

            // 创建一个Long数组，用于存储转换后的日期值
            Long[] date2 = new Long[split.length];

            // 遍历split数组，逐个转换成Long类型并存储到date2数组中
            for (int i = 0; i < split.length; i++) {
                date2[i] = Long.parseLong(split[i].trim()); // 使用trim去除可能的空格并转换为Long
            }
            return success(substationService.getTimeTableData(date2, type, bdType, ywType));
        } catch (Exception e) {
            System.out.println("getTimeTableData 异常：" + e.getMessage());
            return success(new ArrayList<>());
        }
    }


    @GetMapping("/getSubstationAll")
    @Operation(summary = "获得变电站名称列表")
    public CommonResult<List<SubstationDO>> selectAll() {
        return success(substationService.selectAll());
    }

    @GetMapping("/getSubstationActive")
    @Operation(summary = "获得变电站名称列表")
    public CommonResult<List<SubstationDO>> selectActive() {
        return success(substationService.selectActive());
    }


    @GetMapping("/selectLikeName")
    @Operation(summary = "模糊查询")
    public CommonResult<List<SubstationDO>> selectLikeName(@RequestParam("fullname") String fullname, @RequestParam("type") String type) {
        return success(substationService.selectLikeName(fullname, type));
    }

    @GetMapping("/selectById")
    @Operation(summary = "获得编辑数据详情")
    public CommonResult<SubstationDO> selectById(@RequestParam("id") Long id) {
        return success(substationService.validateSubstationExists(id));
    }


    @PostMapping("/updateData")
    @Operation(summary = "更新数据源")
    public CommonResult<Integer> updateData(@RequestParam("date") String date, @RequestParam("code") String code, @RequestParam("type") String type) throws ParseException {
        // 将传入的date字符串按逗号分割
        String[] split = date.split(",");

        // 创建一个Long数组，用于存储转换后的日期值
        Long[] date2 = new Long[split.length];

        // 遍历split数组，逐个转换成Long类型并存储到date2数组中
        for (int i = 0; i < split.length; i++) {
            date2[i] = Long.parseLong(split[i].trim()); // 使用trim去除可能的空格并转换为Long
        }

        return success(substationService.updateData(date2, code, type));
    }


    @GetMapping("/getSubData")
    @Operation(summary = "获得日前实时数据")
    public CommonResult<List<Map<String, Object>>> getSubData(@RequestParam("date") String date, @RequestParam("name") String name, @RequestParam("type") String type) throws ParseException {
        try {
            // 将传入的date字符串按逗号分割
            String[] split = date.split(",");

            // 创建一个Long数组，用于存储转换后的日期值
            Long[] date2 = new Long[split.length];

            // 遍历split数组，逐个转换成Long类型并存储到date2数组中
            for (int i = 0; i < split.length; i++) {
                date2[i] = Long.parseLong(split[i].trim()); // 使用trim去除可能的空格并转换为Long
            }
            return success(substationService.getSubData(date2, name, type));
        } catch (Exception e) {
            System.out.println("getTimeTableData 异常：" + e.getMessage());
            return success(new ArrayList<>());
        }
    }

    @GetMapping("/getSubEcharts")
    @Operation(summary = "获得日前实时数据曲线")
    public CommonResult<Map<String, List<Float>>> getSubEcharts(@RequestParam("date") Long[] date, @RequestParam("type") String type) {
        return success(substationService.getSubEcharts(date, type));
    }


    @GetMapping("/getRealTimeSpotMarkets")
    @Operation(summary = "获得现货市场数据统计")
    public CommonResult<List<Map<String, Object>>> getRealTimeSpotMarkets(@RequestParam("date") String date, @RequestParam("orgCodeType") String orgCodeType) throws ParseException {
        // 将传入的date字符串按逗号分割
        String[] split = date.split(",");

        // 创建一个Long数组，用于存储转换后的日期值
        Long[] date2 = new Long[split.length];

        // 遍历split数组，逐个转换成Long类型并存储到date2数组中
        for (int i = 0; i < split.length; i++) {
            date2[i] = Long.parseLong(split[i].trim()); // 使用trim去除可能的空格并转换为Long
        }

        return success(substationService.getRealTimeSpotMarkets(date2, orgCodeType));
    }

    @GetMapping("/getRealTimeSpotMarketsAll")
    @Operation(summary = "现货市场数据统计所有交易单元数据")
    public CommonResult<Map<String, List<Map<String, Object>>>> getRealTimeSpotMarketsAll(@RequestParam("date") String date) throws ParseException {
        // 将传入的date字符串按逗号分割
        String[] split = date.split(",");

        // 创建一个Long数组，用于存储转换后的日期值
        Long[] date2 = new Long[split.length];

        // 遍历split数组，逐个转换成Long类型并存储到date2数组中
        for (int i = 0; i < split.length; i++) {
            date2[i] = Long.parseLong(split[i].trim()); // 使用trim去除可能的空格并转换为Long
        }

        return success(substationService.getRealTimeSpotMarketsAll(date2));
    }


    @GetMapping("/page")
    @Operation(summary = "获得日前实时电价统计分页")

    public CommonResult<PageResult<SubstationRespVO>> getSubstationPage(@Valid SubstationPageReqVO pageReqVO) {
        PageResult<SubstationDO> pageResult = substationService.getSubstationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SubstationRespVO.class));
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出日前实时电价统计 Excel")
    @PreAuthorize("@ss.hasPermission('jy:substation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSubstationExcel(@Valid SubstationPageReqVO pageReqVO,
                                      HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SubstationDO> list = substationService.getSubstationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "日前实时电价统计.xls", "数据", SubstationRespVO.class,
                BeanUtils.toBean(list, SubstationRespVO.class));
    }

    @PostMapping("/getRemainingSpaceStatistics")
    @Operation(summary = "剩余空间统计")
    public CommonResult<Map<String, Object>> getRemainingSpaceStatistics(@RequestBody SubstationRespVO substationRespVO) {
        Map<String, Object> tradingUnitDO = substationService.getRemainingSpaceStatistics(substationRespVO.getUnitId(), substationRespVO.getYearOrMonthDate(), substationRespVO.getTimeSlotType());
        return success(tradingUnitDO);
    }

    @PostMapping("/exportEcharts")
    @Operation(summary = "日前实时电价统计曲线数据导出")
    //@PreAuthorize("@ss.hasPermission('et:tradingUnit_settlement_release_sum:query')")
    public void downloadExcelTemplate(HttpServletResponse response, HttpServletRequest request, @RequestBody SubstationRespVO substationRespVO) {
        substationService.exportEcharts(response, request, substationRespVO.getDate());
    }
}