package cn.iocoder.yudao.module.et.controller.admin.startupanddownunitdetail.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Schema(description = "管理后台 - 必开必停机组明细信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StartupAndDownUnitDetailRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2421")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "父ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3992")
    @ExcelProperty("父ID")
    private Long pid;

    @Schema(description = "必开 1 必停 0", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("必开 1 必停 0")
    private String type;

    @Schema(description = "业务时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("业务时间")
    private Date bizDate;

    @Schema(description = "机组台数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("机组台数")
    private Integer units;

    @Schema(description = "电压等级(kV)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("电压等级(kV)")
    private BigDecimal voltageLevel;

    @Schema(description = "原因", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("原因")
    private String cause;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开始时间")
    private LocalDateTime dateBeign;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("结束时间")
    private LocalDateTime dateEnd;

    @Schema(description = "部门ID", example = "13853")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    @ExcelProperty("上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    @ExcelProperty("数据爬取时间")
    private LocalDateTime gatherDate;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime modificationTime;

}