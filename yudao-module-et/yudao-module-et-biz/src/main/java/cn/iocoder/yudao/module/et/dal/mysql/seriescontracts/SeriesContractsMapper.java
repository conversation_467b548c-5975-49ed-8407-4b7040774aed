package cn.iocoder.yudao.module.et.dal.mysql.seriescontracts;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.et.controller.admin.seriescontracts.vo.SeriesContractsPageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.seriescontracts.SeriesContractsDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 合同序列 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("shardingsphereDB")
public interface SeriesContractsMapper extends BaseMapperX<SeriesContractsDO> {

    default PageResult<SeriesContractsDO> selectPage(SeriesContractsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SeriesContractsDO>()
                .likeIfPresent(SeriesContractsDO::getName, reqVO.getName())
                .eqIfPresent(SeriesContractsDO::getContractsTypeId, reqVO.getContractsTypeId())
                .orderByDesc(SeriesContractsDO::getId));
    }


	@Select("select * from jy_series_contracts where name like CONCAT('%', #{seriesName}, '%') limit 10")
	List<SeriesContractsDO> selectByLikeNameLimit(@Param("seriesName") String seriesName);

    IPage<SeriesContractsDO> selectSqlPage(IPage<?> page, @Param("reqVO") SeriesContractsPageReqVO reqVO);

}