package cn.iocoder.yudao.module.et.dal.dataobject.crossSectionLimit;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

/**
 * 断面限额变化
 *
 * <AUTHOR>
 **/
@TableName("cross_section_limit")
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class CrossSectionLimitDO {

    /**
     * 时间
     */
    private Timestamp ts;
    /**
     * 业务时间
     */
    private Timestamp bizTime;
    /**
     * 断面名称
     */
    private String sectionName;
    /**
     * 断面实际值
     */
    private float realtimeValue;
    /**
     * 正向限值
     */
    private float directLimit;
    /**
     * 反向限值
     */
    private float reverseLimit;
    /**
     * 区域
     */
    private String area;
}
