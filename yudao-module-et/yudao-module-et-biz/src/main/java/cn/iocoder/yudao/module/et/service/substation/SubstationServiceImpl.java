package cn.iocoder.yudao.module.et.service.substation;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.dict.core.DictFrameworkUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.et.controller.admin.substation.vo.SubstationPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.substation.vo.SubstationSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.companyunit.CompanyUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.DispatchNodeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.marketclearing.MarketClearingDO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import cn.iocoder.yudao.module.et.dal.dataobject.substation.SubstationDO;
import cn.iocoder.yudao.module.et.dal.dataobject.timeslots.TimeSlotsDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO;
import cn.iocoder.yudao.module.et.dal.mysql.dispatchnode.DispatchNodeMapper;
import cn.iocoder.yudao.module.et.dal.mysql.substation.SubstationMapper;
import cn.iocoder.yudao.module.et.dal.mysql.tradingUnitOrg.TradingUnitOrgMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.companyunit.CompanyUnitMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.marketclearing.MarketClearingMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.substation.RealTimeSpotMarketMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.substation.SubstationTDMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.tradingunit.TradingUnitMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.tradingunitstatm.TradingUnitStatMMapper;
import cn.iocoder.yudao.module.et.service.companyunit.CompanyUnitService;
import cn.iocoder.yudao.module.et.service.dispatchnode.DispatchNodeService;
import cn.iocoder.yudao.module.et.service.powerstation.PowerStationService;
import cn.iocoder.yudao.module.et.service.timeslots.TimeSlotsService;
import cn.iocoder.yudao.module.et.util.JyDateTimeUtil;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.infra.enums.ErrorCodeConstants.SUB_NAME_SAVE;

/**
 * 日前实时电价统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SubstationServiceImpl implements SubstationService {

    @Resource
    private SubstationMapper substationMapper;
    @Resource
    private RealTimeSpotMarketMapper realTimeSpotMarketMapper;
    @Resource
    private TradingUnitOrgMapper tradingUnitOrgMapper;
    @Resource
    private TradingUnitMapper tradingUnitMapper;


    @Resource
    private SubstationTDMapper substationTDMapper;
    @Resource
    private DispatchNodeMapper dispatchNodeMapper;
    @Resource
    private DeptApi deptApi;
    @Resource
    private TimeSlotsService timeSlotsService;
    @Resource
    private TradingUnitStatMMapper tradingUnitStatMMapper;
    @Resource
    private CompanyUnitService companyUnitService;
    @Resource
    private PowerStationService powerStationService;
    @Resource
    private MarketClearingMapper marketClearingMapper;

    @Resource
    private DispatchNodeService dispatchNodeService;

    @Resource
    private CompanyUnitMapper companyUnitMapper;

    public static List<Float> calculateAverages(List<MarketClearingDO> marketClearingDOList, String type) {
        // 1. 按15分钟时间点分组并计算平均值
        Map<LocalTime, Double> averageMap = new HashMap<>();
        if (type.equals("0")) {
            averageMap = marketClearingDOList.stream()
                    .filter(mc -> mc.getTs() != null && mc.getDayAheadNodalElectricityPrice() != null)
                    .collect(Collectors.groupingBy(
                            mc -> {
                                LocalDateTime dateTime = mc.getTs().toLocalDateTime();
                                int adjustedMinute = (dateTime.getMinute() / 15) * 15;
                                return LocalTime.of(dateTime.getHour(), adjustedMinute);
                            },
                            Collectors.averagingDouble(MarketClearingDO::getDayAheadNodalElectricityPrice)
                    ));
        } else {
            averageMap = marketClearingDOList.stream()
                    .filter(mc -> mc.getTs() != null && mc.getRealNodalElectricityPrice() != null)
                    .collect(Collectors.groupingBy(
                            mc -> {
                                LocalDateTime dateTime = mc.getTs().toLocalDateTime();
                                int adjustedMinute = (dateTime.getMinute() / 15) * 15;
                                return LocalTime.of(dateTime.getHour(), adjustedMinute);
                            },
                            Collectors.averagingDouble(MarketClearingDO::getRealNodalElectricityPrice)
                    ));
        }
        // 2. 生成所有时间点（从00:15到次日00:00，共96个时间点）
        List<LocalTime> allTimeSlots = generateAllTimeSlots();

        // 3. 按时间顺序填充平均值，缺失则用0.0f
        List<Float> result = new ArrayList<>();
        for (LocalTime slot : allTimeSlots) {
            Double avg = averageMap.get(slot);
            result.add(avg != null ? (float) avg.doubleValue() : 0.0f);
        }
        return result;
    }

    // 生成时间点列表：00:15, 00:30, ..., 23:45, 00:00
    private static List<LocalTime> generateAllTimeSlots() {
        List<LocalTime> slots = new ArrayList<>();
        // 从00:15开始，每15分钟一个时间点，共96个（覆盖24小时）
        LocalTime time = LocalTime.of(0, 15); // 起始时间00:15
        for (int i = 0; i < 96; i++) {
            slots.add(time);
            time = time.plusMinutes(15); // 自动处理进位（如23:45 +15分钟=00:00）
        }
        return slots;
    }

    @Override
    public Long createSubstation(String fullname, String name, String tableName, String type, String orgCode) {

        List<SubstationDO> substationDOS = selectAll();
        for (SubstationDO substationDO : substationDOS) {
            if (substationDO.getTableName().equals(fullname)) {
                throw exception(SUB_NAME_SAVE);
            }
        }

        SubstationSaveReqVO substationSaveReqVO = new SubstationSaveReqVO();
        substationSaveReqVO.setTableName(tableName);
        substationSaveReqVO.setName(name);
        substationSaveReqVO.setFullname(fullname);
        substationSaveReqVO.setType(type);
        substationSaveReqVO.setOrgCode(orgCode);
        // 插入
        SubstationDO substation = BeanUtils.toBean(substationSaveReqVO, SubstationDO.class);
//        if (substation.getOrgCode()==null){
//          substation.setOrgCode(" ");
//        }
        substationMapper.insert(substation);
        // 返回
        return substation.getId();
    }

    //获取变电站类型列表
    @Override
    public List<String> getSubstations() {
        List<String> substationType = DictFrameworkUtils.getDictDataLabelList("substation_type");
        List<String> sub = new ArrayList<>();
        for (String string : substationType) {
            String s = DictFrameworkUtils.parseDictDataValue("substation_type", string);
            sub.add(s);
        }
        return sub;
    }

    /**
     * 根据场站编码获取设备信息
     *
     * @param orgCode 场站编码
     * @return SubstationDO
     */
    @Override
    public SubstationDO getSubstationByOrgCode(String orgCode) {
        LambdaQueryWrapper<SubstationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(SubstationDO::getOrgCode, orgCode);
        return substationMapper.selectOne(queryWrapper);
    }

    //获取变电站名称列表
    @Override
    public List<SubstationDO> getSubstationName() {
        return substationMapper.selectAllName();
    }

    @Override
    public Integer updateData(Long[] date, String code, String type) {
        Integer result = 0;
        date[1] = date[1] + 86400000L + 1000;
        date[0] = date[0] + 15 * 60 * 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }
        String realCode = tradingUnitOrgMapper.selectCodeByOrgCode(code);
        SubstationDO substationDO = substationMapper.selectByOrgCode(code);
        String curentUserOrgCode = getCurentUserOrgCode();
        List<TradingUnitDO> tradingUnit = tradingUnitMapper.getTradingUnit(realCode, timestamps[0], timestamps[timestamps.length - 1]);
        if (tradingUnit.size() == 0) {
            return 0;
        }
        if (type.equals("0")) { //0：日前
            result = substationTDMapper.insertDayNodalElectricityPrice(curentUserOrgCode, substationDO.getTableName(), tradingUnit);
        } else {
            result = substationTDMapper.insertRealNodalElectricityPrice(curentUserOrgCode, substationDO.getTableName(), tradingUnit);
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getSubData(Long[] date, String name, String type) {
        Map<String, Object> map2 = new HashMap<>();

        String hisFieldName = "day_ahead_nodal_electricity_price";
        String nowFieldName = "real_nodal_electricity_price";
        List<Map<String, Object>> maps;
        String s = substationMapper.selectFullName(name);
        map2.put("sbName", s);

        date[1] = date[1] + 86400000L + 1000;
        date[0] = date[0] + 15 * 60 * 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }

        String curentUserOrgCode = getCurentUserOrgCode();
        List<Map<String, Object>> a = new ArrayList<>();
        Map<String, Object> test = new HashMap<>();
        if (type.equals("0")) { //0：日前
            maps = substationTDMapper.selectHis(timestamps[0], timestamps[timestamps.length - 1], name, curentUserOrgCode);
            int counter = 1;  // 用来追踪每次分组中的 "c" 后面的数字
            for (Map<String, Object> map : maps) {
                //不显示为null的数据（废弃）
                if (map.get(hisFieldName) != null) {
                    String priceLimit = DictFrameworkUtils.parseDictDataValue("electricity_price", "price_limit");
                    String value = map.getOrDefault(hisFieldName, 0.0).toString();
                    String max = priceLimit.split(",")[0];
                    String min = priceLimit.split(",")[1];
                    if (Double.parseDouble(value) > Double.parseDouble(max)) {
                        value = max;
                    } else if (Double.parseDouble(value) < Double.parseDouble(min)) {
                        value = min;
                    }
                    test.put("c" + counter, new BigDecimal(value).setScale(3, RoundingMode.HALF_UP));
                } else {
                    test.put("c" + counter, "--");
                }
                Object ts = map.get("ts");  // 获取 "ts" 字段（假设它是时间戳）
                // 如果 "ts" 是 Timestamp 类型，获取其时间戳
                Timestamp timestamp = (Timestamp) ts;
                long timestampMillis = timestamp.getTime();  // 获取时间戳的毫秒值
                Instant instant = Instant.ofEpochMilli(timestampMillis);  // 转换时间戳为 Instant
                // 设置日期格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
                        .withZone(ZoneId.systemDefault());  // 使用系统默认时区

                String formattedDate = formatter.format(instant);
                test.put("name", "日前市场出清节点电价");
                test.put("sbName", s);
                counter++;
                if (counter == 96 || counter == 97) {  // 每 96 条数据分组
                    if (counter == 96) {
                        test.put("date", formattedDate);
                    } else {
                        a.add(test);
                        test = new HashMap<>();  // 重置 test
                        counter = 1;  // 重置计数器
                    }

                }
            }
            return a;

        } else {
            maps = substationTDMapper.selectNow(timestamps[0], timestamps[timestamps.length - 1], name, curentUserOrgCode);
            int counter = 1;  // 用来追踪每次分组中的 "c" 后面的数字
            for (Map<String, Object> map : maps) {
                if (map.get(nowFieldName) != null) {
                    //电价限价
                    String priceLimit = DictFrameworkUtils.parseDictDataValue("electricity_price", "price_limit");
                    String value = map.getOrDefault(nowFieldName, 0.0).toString();
                    String max = priceLimit.split(",")[0];
                    String min = priceLimit.split(",")[1];
                    if (Double.parseDouble(value) > Double.parseDouble(max)) {
                        value = max;
                    } else if (Double.parseDouble(value) < Double.parseDouble(min)) {
                        value = min;
                    }
                    test.put("c" + counter, new BigDecimal(value).setScale(3, RoundingMode.HALF_UP));
                } else {
                    test.put("c" + counter, "--");
                }
                Object ts = map.get("ts");  // 获取 "ts" 字段（假设它是时间戳）
                // 如果 "ts" 是 Timestamp 类型，获取其时间戳
                Timestamp timestamp = (Timestamp) ts;
                long timestampMillis = timestamp.getTime();  // 获取时间戳的毫秒值
                Instant instant = Instant.ofEpochMilli(timestampMillis);  // 转换时间戳为 Instant
                // 设置日期格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
                        .withZone(ZoneId.systemDefault());  // 使用系统默认时区
                String formattedDate = formatter.format(instant);
                test.put("name", "实时市场出清节点电价");
                test.put("sbName", s);
                counter++;
                if (counter == 96 || counter == 97) {  // 每 96 条数据分组
                    if (counter == 96) {
                        test.put("date", formattedDate);
                    } else {
                        a.add(test);
                        test = new HashMap<>();  // 重置 test
                        counter = 1;  // 重置计数器
                    }
                }
            }
            return a;
        }
    }

    @Override
    public Map<String, List<Float>> getSubEcharts(Long[] date, String type) {
        Map<String, List<Float>> map = new LinkedHashMap<>();
        //定制化的曲线图 只要固定的几个变电站
//        List<String> tableName = new ArrayList<>();
//        tableName.add("xf");
//        tableName.add("ly");
//        tableName.add("cj");
//        tableName.add("lzd");
//        tableName.add("hsg");
//        tableName.add("jy");
//        tableName.add("xg");
//        tableName.add("cx");
//        tableName.add("lt");
        LambdaQueryWrapper<SubstationDO> wrapper = new LambdaQueryWrapper<>();
//        wrapper.in(SubstationDO::getTableName, tableName);
        List<SubstationDO> substationDOS = substationMapper.selectList(wrapper);
        date[0] = date[0] + 900000L;
        date[1] = date[1] + 86400000L + 900000L - 1000L;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }
        for (SubstationDO substationDO : substationDOS) {
            List<Float> list = new ArrayList<>();
            String orgCode = companyUnitService.getCurrentUserOrgCode();
            String tableName1 = "market_clearing_" + orgCode + "_" + substationDO.getTableName();
            List<MarketClearingDO> marketClearingDOList = marketClearingMapper.getPriceForecast(tableName1, timestamps[0], timestamps[timestamps.length - 1]);

            list = calculateAverages(marketClearingDOList, type);
            map.put(substationDO.getFullname(), list);
        }
        return map;
    }

    @Override
    public void updateSubstation(SubstationSaveReqVO updateReqVO) {
        // 判断字符串是否为空或者仅由空格组成
        if (updateReqVO.getTableName() == null || updateReqVO.getTableName().trim().isEmpty()) {
            updateReqVO.setTableName(null);
        }
        // 校验存在
        validateSubstationExists(updateReqVO.getId());
        // 更新
        SubstationDO updateObj = BeanUtils.toBean(updateReqVO, SubstationDO.class);

        // 手动设置 tableName 为 null
        if (updateReqVO.getOrgCode().isEmpty()) {
            substationMapper.updateNull(updateReqVO.getId());

        } else {
            substationMapper.updateSub(updateObj);
        }

    }

    @Override
    public void deleteSubstation(Long id) {
        // 校验存在
        validateSubstationExists(id);
        // 删除
        substationMapper.deleteById(id);
    }

    @Override
    public SubstationDO validateSubstationExists(Long id) {
        return substationMapper.selectById(id);
    }

    @Override
    public SubstationDO getSubstation(Long id) {
        return substationMapper.selectById(id);
    }

    @Override
    public List<SubstationDO> selectAll() {
        List<SubstationDO> substationDOS = substationMapper.selectAll();

// 使用 Stream API 去重，保留每个 id 的第一个 SubstationDO
        Map<Long, SubstationDO> uniqueSubstations = substationDOS.stream()
                .collect(Collectors.toMap(SubstationDO::getId, Function.identity(), (existing, replacement) -> existing));

// 获取去重后的 List

        return new ArrayList<>(uniqueSubstations.values());
    }

    @Override
    public List<SubstationDO> selectActive() {
        List<SubstationDO> substationDOS = substationMapper.selectActive();

// 使用 Stream API 去重，保留每个 id 的第一个 SubstationDO
        Map<Long, SubstationDO> uniqueSubstations = substationDOS.stream()
                .collect(Collectors.toMap(SubstationDO::getId, Function.identity(), (existing, replacement) -> existing));

// 获取去重后的 List

        return new ArrayList<>(uniqueSubstations.values());
    }


    @Override
    public List<SubstationDO> selectLikeName(String fullname, String type) {
        // 如果 fullname 不为空，且 type 为空，执行根据 fullname 模糊查询
        if (fullname != null && !fullname.trim().isEmpty() && (type == null || type.trim().isEmpty())) {
            return substationMapper.selectLikeNameByFullname(fullname);
        }

        // 如果 fullname 不为空，且 type 不为空，执行根据 fullname 模糊查询和 type 精确查询
        if ((fullname != null && !fullname.trim().isEmpty()) && (type != null && !type.trim().isEmpty())) {
            return substationMapper.selectLikeNameByFullnameAndType(fullname, type);
        }

        // 如果 fullname 为空，且 type 不为空，执行根据 type 精确查询
        if ((fullname == null || fullname.trim().isEmpty()) && (type != null && !type.trim().isEmpty())) {
            return substationMapper.selectLikeNameByType(type);
        }
        return Collections.emptyList();
//        return substationMapper.selectLikeName(fullname,type);
    }


    @Override
    public PageResult<SubstationDO> getSubstationPage(SubstationPageReqVO pageReqVO) {
        return substationMapper.selectPage(pageReqVO);
    }


    @Override
    public String getCurentUserOrgCode() {
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        DeptRespDTO deptRespDTO = deptApi.getDept(deptId);
        DeptRespDTO currentDept = deptRespDTO;

        // 循环查找父部门，直到找到父部门 ID 为 0 的部门
        while (currentDept != null && currentDept.getParentId() != 0) {
            currentDept = deptApi.getDept(currentDept.getParentId());
        }

        // 返回找到的部门的 orgCode
        return currentDept != null ? currentDept.getOrgCode() : null;
    }


    public List<Map<String, Object>> getSubData2(Long[] date, String name, String type) {
        Map<String, Object> map2 = new HashMap<>();

        String hisFieldName = "day_ahead_nodal_electricity_price";
        String nowFieldName = "real_nodal_electricity_price";
        List<Map<String, Object>> maps;
        String s = substationMapper.selectFullName(name);
        map2.put("sbName", s);
        date[1] = date[1] + 86400000L + 1000;
        date[0] = date[0] + 15 * 60 * 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }

        String curentUserOrgCode = getCurentUserOrgCode();
        List<Map<String, Object>> a = new ArrayList<>();
        Map<String, Object> test = new HashMap<>();
        if (type.equals("0")) { //0：日前
            // 假设查询结果是每15分钟一条的数据
            maps = substationTDMapper.selectHis(timestamps[0], timestamps[timestamps.length - 1], name, curentUserOrgCode);
//            if (maps.size() >3){
//                maps.remove(0);
//            }
            int counter = 1;  // 用来追踪每次分组中的 "t" 后面的数字
            double sum = 0;   // 用来累加每4条的值
            int groupCount = 0;  // 用来追踪当前是第几组数据
            for (Map<String, Object> map : maps) {
                // 累加当前条目的值
                sum += (map.get(hisFieldName) != null ? (Float) map.get(hisFieldName) : 0);

                // 如果是4条数据后，进行汇总处理
                if (counter % 4 == 0) {
                    // 每4条数据计算平均值
                    double average = sum / 4;
                    // 存入 "t" + groupCount
                    test.put("t" + (groupCount), String.format("%.3f", average));
                    // 重置累加器和计数器
                    sum = 0;
                    groupCount++;  // 增加组的计数器
                }
                counter++;  // 累加计数器
                // 每24条数据（即24小时），就将结果存入最终列表
                if (groupCount == 24) {
                    a.add(test);  // 将当前的数据存入列表
                    test = new HashMap<>();  // 重置 test
                    groupCount = 0;  // 重置组计数器
                }
            }

            return a;

        } else {

            // 假设查询结果是每15分钟一条的数据
            maps = substationTDMapper.selectNow(timestamps[0], timestamps[timestamps.length - 1], name, curentUserOrgCode);
//            if (maps.size() >3){
//                maps.remove(0);
//            }
            int counter = 1;  // 用来追踪每次分组中的 "t" 后面的数字
            double sum = 0;   // 用来累加每4条的值
            int groupCount = 0;  // 用来追踪当前是第几组数据
            for (Map<String, Object> map : maps) {
                // 累加当前条目的值
                sum += (map.get(nowFieldName) != null ? (Float) map.get(nowFieldName) : 0);

                // 如果是4条数据后，进行汇总处理
                if (counter % 4 == 0) {
                    // 每4条数据计算平均值
                    double average = sum / 4;

                    // 存入 "t" + groupCount
                    test.put("t" + (groupCount), String.format("%.3f", average));

                    // 重置累加器和计数器
                    sum = 0;
                    groupCount++;  // 增加组的计数器
                }

                counter++;  // 累加计数器

                // 每24条数据（即24小时），就将结果存入最终列表
                if (groupCount == 24) {
                    a.add(test);  // 将当前的数据存入列表
                    test = new HashMap<>();  // 重置 test
                    groupCount = 0;  // 重置组计数器
                }
            }

            return a;


        }

    }


    //时段电价占比分析
    @Override
    public List<Map<String, Object>> getTimeTableData(Long[] date, String type, String bdType, String ywType) {
        // 定义范围和对应的结果数据结构
        int[] ranges = {40, 110, 130, 160, 250, 300};
        List<Map<String, Object>> result = new ArrayList<>();

        List<TimeSlotsDO> timeSlotsDOS = timeSlotsService.selectByTime(type);
        List<Map<String, Object>> subData = getSubData2(date, bdType, ywType);

        // 初始化每个范围的计数
        Map<String, int[]> rangeCounts = new HashMap<>();

        // 遍历时间段
        for (TimeSlotsDO timeSlotsDO : timeSlotsDOS) {
            int hour = timeSlotsDO.getStartTime().getHour();
            String key = "t" + hour;

            // 遍历 subData 列表
            for (Map<String, Object> map : subData) {
                if (map.containsKey(key)) {
                    Object valueObj = map.get(key);

                    // 转换值为数字类型
                    double value = 0;
                    try {
                        if (valueObj instanceof String) {
                            value = Double.parseDouble((String) valueObj);
                        } else if (valueObj instanceof Number) {
                            value = ((Number) valueObj).doubleValue();
                        } else {
                            // 如果不是字符串或数字，跳过处理
                            continue;
                        }
                    } catch (NumberFormatException e) {
                        // 遇到无法解析的值时跳过
                        continue;
                    }

                    // 初始化当前时间段的范围计数数组
                    rangeCounts.putIfAbsent(key, new int[ranges.length + 1]);
                    int[] counts = rangeCounts.get(key);

                    // 根据范围统计
                    if (value < ranges[0]) {
                        counts[0]++;
                    } else if (value > ranges[ranges.length - 1]) {
                        counts[ranges.length]++;
                    } else {
                        for (int i = 1; i < ranges.length; i++) {
                            if (value <= ranges[i]) {
                                counts[i]++;
                                break;
                            }
                        }
                    }
                }
            }
        }

        // 获取升序排列的 key 列表
        List<String> sortedKeys = new ArrayList<>(rangeCounts.keySet());
        sortedKeys.sort((k1, k2) -> {
            int hour1 = Integer.parseInt(k1.substring(1)); // 提取 t 后面的数字
            int hour2 = Integer.parseInt(k2.substring(1));
            return Integer.compare(hour1, hour2);
        });

        // 构造最终结果
        for (int i = 0; i <= ranges.length; i++) {
            Map<String, Object> row = new LinkedHashMap<>(); // 保持插入顺序
            row.put("range", i == 0 ? "<" + ranges[0] : (i == ranges.length ? ">" + ranges[ranges.length - 1]
                    : ranges[i - 1] + "-" + ranges[i]));

            // 按照排序后的 key 填充每个时间段的计数
            for (String key : sortedKeys) {
                row.put(key, rangeCounts.get(key)[i]);
            }
            result.add(row);
        }

        return result;
    }

    @Override
    public Map<String, List<Map<String, Object>>> getRealTimeSpotMarketsAll(Long[] date) {
        List<DispatchNodeDO> dispatchNodeDOList = companyUnitService.getDispatchNodeList();
        Map<String, List<Map<String, Object>>> maps = new LinkedHashMap<>();
        for (DispatchNodeDO dispatchNodeDO : dispatchNodeDOList) {
            Long[] date1 = date.clone();
            maps.put(dispatchNodeDO.getName(), getRealTimeSpotMarkets(date1, dispatchNodeDO.getCode()));
        }
        return maps;
    }

    //交易单元现货市场数据统计
    @Override
    public List<Map<String, Object>> getRealTimeSpotMarkets(Long[] date, String orgCodeType) {
        Double capacity = dispatchNodeMapper.selectCapacityByCode(orgCodeType);
        // 调整时间范围
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }

        // 查询原始数据
        List<Map<String, Object>> realTimeSpotMarket = realTimeSpotMarketMapper.getRealTimeSpotMarket(timestamps[0], timestamps[timestamps.length - 1], orgCodeType);

        DispatchNodeDO dispatchNodeDO = dispatchNodeService.getDispatchNodeByCode(orgCodeType);
        List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getCongestionCost(orgCodeType, timestamps[0], timestamps[timestamps.length - 1]);

        //变电站，通过，主库场站（power_station表）org_code，查询gs分库 substation 表，查询对应变电站节点
        PowerStationDO powerStationDO = powerStationService.getPowerStationByCode(dispatchNodeDO.getOrgCode());
        SubstationDO substationDO = this.getSubstationByOrgCode(powerStationDO.getCode());
        List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getSettlementPrice(powerStationDO.getOrgCode(), timestamps[0], timestamps[timestamps.length - 1]);
        List<MarketClearingDO> marketClearingDOList = marketClearingMapper.getCongestionCost(powerStationDO.getOrgCode(), timestamps[0], timestamps[timestamps.length - 1], substationDO.getTableName());

        // 累加同一天的数据
        Map<String, Map<String, Object>> aggregatedResults = new LinkedHashMap<>();
        for (Map<String, Object> record : realTimeSpotMarket) {
            // 获取日期 (只保留到天级别)
            String dayKey = record.get("ts").toString().substring(0, 10);

            // 如果当天已存在，累加字段
            if (!aggregatedResults.containsKey(dayKey)) {
                // 创建新记录，并将 ts 字段改成日期格式
                Map<String, Object> newRecord = new LinkedHashMap<>(record);
                newRecord.put("ts", dayKey); // 将 ts 改为 yyyy-MM-dd 格式的字符串
                aggregatedResults.put(dayKey, newRecord);
            } else {
                Map<String, Object> aggregatedRecord = aggregatedResults.get(dayKey);
                // 遍历字段并累加需要的数值类型字段
                for (String key : record.keySet()) {
                    if (key.startsWith("his_") || key.startsWith("real_") || key.equals("deviation_review_prices")
                            || key.equals("adjustment_frequency_lessen_energy_compensation")) {
                        Object value = record.get(key);
                        if (value instanceof Number) {
                            double currentValue = aggregatedRecord.get(key) instanceof Number
                                    ? ((Number) aggregatedRecord.get(key)).doubleValue()
                                    : 0.0; // 如果 aggregatedRecord.get(key) 是 null，则取 0
                            double newValue = currentValue + ((Number) value).doubleValue();
                            aggregatedRecord.put(key, roundToThreeDecimalPlaces(newValue));
                        }

                    }
                }
            }
        }


        for (Map<String, Object> aggregatedRecord : aggregatedResults.values()) {
            double hisPositivePrices = ((Number) aggregatedRecord.computeIfAbsent("his_positive_prices", k -> 0)).doubleValue();
            double hisNegativePrices = ((Number) aggregatedRecord.computeIfAbsent("his_negative_prices", k -> 0)).doubleValue();
            double realPositivePrices = ((Number) aggregatedRecord.computeIfAbsent("real_positive_prices", k -> 0)).doubleValue();
            double realNegativePrices = ((Number) aggregatedRecord.computeIfAbsent("real_negative_prices", k -> 0)).doubleValue();
            double hisPositivePower = ((Number) aggregatedRecord.computeIfAbsent("his_positive_power", k -> 0)).doubleValue();
            double hisNegativePower = ((Number) aggregatedRecord.computeIfAbsent("his_negative_power", k -> 0)).doubleValue();
            double realPositivePower = ((Number) aggregatedRecord.computeIfAbsent("real_positive_power", k -> 0)).doubleValue();
            double realNegativePower = ((Number) aggregatedRecord.computeIfAbsent("real_negative_power", k -> 0)).doubleValue();
            double deviationReviewPrices = ((Number) aggregatedRecord.computeIfAbsent("deviation_review_prices", k -> 0)).doubleValue();
            double jiscoBilateralTransactions = ((Number) aggregatedRecord.computeIfAbsent("jisco_bilateral_transactions", k -> 0)).doubleValue();
            double adjustmentFrequencyLessenEnergyCompensation = ((Number) aggregatedRecord.computeIfAbsent(
                    "adjustment_frequency_lessen_energy_compensation", k -> 0)).doubleValue();

            // 计算 xhfylj
            double xhfylj = hisPositivePrices + hisNegativePrices + realPositivePrices + realNegativePrices;
            aggregatedRecord.put("xhfylj", roundToThreeDecimalPlaces(xhfylj));

            // 计算 zxhjj
            double totalPositivePower = hisPositivePower + realPositivePower;
            double totalPositivePrices = hisPositivePrices + realPositivePrices;
            double zxhjj = totalPositivePower == 0 ? 0 : totalPositivePrices / totalPositivePower;
            aggregatedRecord.put("zxhjj", roundToThreeDecimalPlaces(zxhjj));

            // 计算 fxhjj
            double totalNegativePower = hisNegativePower + realNegativePower;
            double totalNegativePrices = hisNegativePrices + realNegativePrices;
            double fxhjj = totalNegativePower == 0 ? 0 : totalNegativePrices / totalNegativePower;
            aggregatedRecord.put("fxhjj", roundToThreeDecimalPlaces(fxhjj));

            // 计算 ykze
            double ykze = xhfylj - deviationReviewPrices + adjustmentFrequencyLessenEnergyCompensation;
            aggregatedRecord.put("ykze", roundToThreeDecimalPlaces(ykze));
            aggregatedRecord.put("sgzft", 0);
            aggregatedRecord.put("sjjyft", 0);
            aggregatedRecord.put("fqjcft", 0);
            aggregatedRecord.put("xnyzfyy", 0);
            aggregatedRecord.put("jgjy", roundToThreeDecimalPlaces(jiscoBilateralTransactions));
            aggregatedRecord.put("zjrl", capacity);
            aggregatedRecord.put("remark", " ");

            //----------------------------------2025-05-30新增 增加中长期阻塞费用和对冲机制费用----------------------------------
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            long timestampMillis = LocalDate.parse(aggregatedRecord.get("ts").toString(), formatter)
                    .atStartOfDay(ZoneId.systemDefault())
                    .toInstant()
                    .toEpochMilli();
            long start = timestampMillis + 900000L;
            long end = timestampMillis + 900000L + 86400000L - 1000L;
            List<TradingUnitDO> tradingCollect = tradingUnitDOList.stream().filter(tradingUnitDO -> tradingUnitDO.getTs().getTime() >= start && tradingUnitDO.getTs().getTime() < end).toList();
            List<CompanyUnitDO> companyCollect = companyUnitDOList.stream().filter(companyUnitDO -> companyUnitDO.getTs().getTime() >= start && companyUnitDO.getTs().getTime() < end).toList();
            List<MarketClearingDO> marketClearingCollect = marketClearingDOList.stream().filter(marketClearingDO -> marketClearingDO.getTs().getTime() >= start && marketClearingDO.getTs().getTime() < end).toList();
            //中长期电量
            List<BigDecimal> longTermContractPowerList = tradingCollect.stream().map(item -> Objects.equals(item.getLongTermContractPower(), "--") ? null : new BigDecimal(item.getLongTermContractPower()).divide(new BigDecimal(4), 5, RoundingMode.HALF_UP)).toList();
            //统一结算点电价
            List<BigDecimal> unifiedSettlementPointPrice = companyCollect.stream().map(item -> Objects.equals(item.getHisUnifiedSettlementPointPrice(), "--") ? null : new BigDecimal(item.getHisUnifiedSettlementPointPrice()).setScale(2, RoundingMode.HALF_UP)).toList();
            //节点电价
            List<BigDecimal> nodalElectricityPrice = marketClearingCollect.stream().map(item -> item.getDayAheadNodalElectricityPrice() == null ? BigDecimal.ZERO : BigDecimal.valueOf(item.getDayAheadNodalElectricityPrice()).setScale(2, RoundingMode.HALF_UP)).toList();

            //中长期阻塞费用=中长期电量*(统一结算点电价-节点电价)
            double longTermBlockagePrices = 0d;
            for (int i = 0; i < longTermContractPowerList.size(); i++) {
                //判断三个List是否有值
                if (i < unifiedSettlementPointPrice.size() && i < nodalElectricityPrice.size()) {
                    BigDecimal longTermContractPower = longTermContractPowerList.get(i);
                    BigDecimal unifiedSettlementPoint = unifiedSettlementPointPrice.get(i);
                    BigDecimal nodalElectricity = nodalElectricityPrice.get(i);
                    //判断三个数据是否为null
                    if (longTermContractPower != null && unifiedSettlementPoint != null && nodalElectricity != null) {
                        try {
                            longTermBlockagePrices += longTermContractPower.multiply(unifiedSettlementPoint.subtract(nodalElectricity)).setScale(2, RoundingMode.HALF_UP).doubleValue();
                        } catch (NumberFormatException e) {
                            // 处理解析异常
                            longTermBlockagePrices += 0;
                        }
                    } else {
                        longTermBlockagePrices += 0;
                    }
                } else {
                    longTermBlockagePrices += 0;
                }
            }

            aggregatedRecord.put("longTermBlockagePrices", longTermBlockagePrices);
            //实发电量 先取settlement_power结算电量 如果为空取tmr tmr为空就取scada scada如果也为空则直接set null
            List<BigDecimal> realPower = new ArrayList<>();
            for (TradingUnitDO t : tradingCollect) {
                if (!t.getSettlementPower().equals("--")) {
                    realPower.add(new BigDecimal(t.getSettlementPower()).setScale(3, RoundingMode.HALF_UP));
                } else if (!t.getTmrPower().equals("--")) {
                    realPower.add(new BigDecimal(t.getTmrPower()).setScale(3, RoundingMode.HALF_UP));
                } else if (!t.getScadaPower().equals("--")) {
                    realPower.add(new BigDecimal(t.getScadaPower()).setScale(3, RoundingMode.HALF_UP));
                } else {
                    realPower.add(null);
                }
            }
            //对冲机制费用 excel公式为 IF((统一结算点电价-节点电价)>0,(统一结算点电价-节点电价)*MIN(中长期电价:实发电量),(统一结算点电价-节点电价)*中长期电价)
            double offsettingMechanismPrices = 0d;
            for (int i = 0; i < unifiedSettlementPointPrice.size() && i < nodalElectricityPrice.size() && i < longTermContractPowerList.size() && i < realPower.size(); i++) {
                BigDecimal unifiedSettlementPoint = unifiedSettlementPointPrice.get(i);
                BigDecimal nodalElectricity = nodalElectricityPrice.get(i);
                BigDecimal longTermContractPower = longTermContractPowerList.get(i);
                BigDecimal realPowerValue = realPower.get(i);
                if (unifiedSettlementPoint != null && nodalElectricity != null && longTermContractPower != null && realPowerValue != null) {
                    try {
                        BigDecimal priceDifference = unifiedSettlementPoint.subtract(nodalElectricity);
                        BigDecimal offsettingMechanismPrice;
                        if (priceDifference.compareTo(BigDecimal.ZERO) > 0) {
                            offsettingMechanismPrice = priceDifference.multiply(longTermContractPower.min(realPowerValue));
                        } else {
                            offsettingMechanismPrice = priceDifference.multiply(longTermContractPower);
                        }
                        offsettingMechanismPrices += offsettingMechanismPrice.setScale(2, RoundingMode.HALF_UP).doubleValue();
                    } catch (NumberFormatException e) {
                        // 处理解析异常
                        offsettingMechanismPrices += 0;
                    }
                } else {
                    offsettingMechanismPrices += 0;
                }
            }
            aggregatedRecord.put("offsettingMechanismPrices", offsettingMechanismPrices);
            aggregatedRecord.put("deductPrices", offsettingMechanismPrices + longTermBlockagePrices);
            //--------------------------结束----------------------------------------
            // 保证所有字段值保留三位小数
            for (String key : aggregatedRecord.keySet()) {
                if (aggregatedRecord.get(key) instanceof Number) {
                    double value = ((Number) aggregatedRecord.get(key)).doubleValue();
                    aggregatedRecord.put(key, roundToThreeDecimalPlaces(value));
                }
            }
        }
        // 返回累加结果
        return new ArrayList<>(aggregatedResults.values());
    }

    /**
     * 剩余空间统计
     *
     * @param companyId       集团或公司id
     * @param yearOrMonthDate 年/月日起
     * @param timeSlotType    时间段类型(峰谷平,可多选)
     * @return
     */
    @Override
    public Map<String, Object> getRemainingSpaceStatistics(String companyId, String yearOrMonthDate, String timeSlotType) {
        Long[] date;
        //如果传入年则是2025 如果传入月则是2025-01 通过-判断传入的时间是年还是月
        String type;
        if (yearOrMonthDate.split(",").length > 1) {
            //2025-03-12新增日查询
            type = "day";
            date = new Long[]{Long.parseLong(yearOrMonthDate.split(",")[0]), Long.parseLong(yearOrMonthDate.split(",")[1])};
        } else {
            type = "noDay";
            if (yearOrMonthDate.split("-").length > 1) {
                date = JyDateTimeUtil.getMonthDateByDate(yearOrMonthDate);
            } else {
                date = JyDateTimeUtil.getYearDateByDate(yearOrMonthDate);
            }
        }
        Map<String, Object> map = new HashMap<>();
        //所有数据
        List<List<Object>> allData = new ArrayList<>();
        //--------------------------拼接表头
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH");
        //根据传入的时间类型获取对应的时段
        StringBuilder titles = new StringBuilder();
        for (String timeSlot : timeSlotType.split(",")) {
            List<TimeSlotsDO> timeSlotsDOList = timeSlotsService.selectByTime(timeSlot);
            if (!titles.toString().isEmpty()) {
                titles.append(",");
            }
            for (int i = 0; i < timeSlotsDOList.size(); i++) {
                if (i == timeSlotsDOList.size() - 1) {
                    titles.append(timeSlotsDOList.get(i).getStartTime().format(formatter)).append("-").append(timeSlotsDOList.get(i).getEndTime().format(formatter)).append("点");
                } else {
                    titles.append(timeSlotsDOList.get(i).getStartTime().format(formatter)).append("-").append(timeSlotsDOList.get(i).getEndTime().format(formatter)).append("点,");
                }
            }
        }
        List<String> headers = new ArrayList<>();
        Collections.addAll(headers, titles.toString().split(","));
        //对表头排序
        headers.sort((s1, s2) -> {
            int num1 = Integer.parseInt(s1.split("-")[0]);
            int num2 = Integer.parseInt(s2.split("-")[0]);
            return Integer.compare(num1, num2);
        });
        if (date.length > 0) {
            Timestamp[] timestamps = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }

            String id = companyUnitService.getCurrentUserOrgCode();
            List<String> unitIds = new ArrayList<>();
            //如果相等则是集团
            if (id.equals(companyId)) {
                List<PowerStationDO> powerStationDOList = powerStationService.getPowerStationByOrgCode(id);
                List<String> ids = powerStationDOList.stream().map(PowerStationDO::getCode).toList();
                LambdaQueryWrapper<DispatchNodeDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(DispatchNodeDO::getOrgCode, ids);
                List<DispatchNodeDO> dispatchNodeDOS = dispatchNodeMapper.selectList(queryWrapper);
                unitIds = dispatchNodeDOS.stream().map(DispatchNodeDO::getCode).toList();
            } else if (companyId.split(",").length > 1) {
                /// 如果传入的id是多个则是集团下的公司
                for (String s : companyId.split(",")) {
                    LambdaQueryWrapper<DispatchNodeDO> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.in(DispatchNodeDO::getOrgCode, s);
                    List<DispatchNodeDO> dispatchNodeDOS = dispatchNodeMapper.selectList(queryWrapper);
                    unitIds.addAll(dispatchNodeDOS.stream().map(DispatchNodeDO::getCode).toList());
                }
            } else {
                //如果不相等则是公司
                LambdaQueryWrapper<DispatchNodeDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(DispatchNodeDO::getOrgCode, companyId);
                List<DispatchNodeDO> dispatchNodeDOS = dispatchNodeMapper.selectList(queryWrapper);
                unitIds = dispatchNodeDOS.stream().map(DispatchNodeDO::getCode).toList();
            }
            for (String unitId : unitIds) {
                //单个交易单元的数据
                List<Object> remainingSpace = new ArrayList<>();
                remainingSpace.add(dispatchNodeMapper.selectNameByCode(unitId));
                List<Map<String, Object>> tradingUnitStatMList;
                if (type.equals("day")) {
                    tradingUnitStatMList = tradingUnitStatMMapper.getRealDataByTs(unitId, timestamps[0], timestamps[timestamps.length - 1]);
                } else {
                    tradingUnitStatMList = tradingUnitStatMMapper.getDataByTs(unitId, timestamps[0], timestamps[timestamps.length - 1]);
                }
                double sum = 0;
                for (String header : headers) {
                    String pattern = "yyyy-MM-dd HH:mm:ss.S";
                    String hour = header.split("-")[0];
                    double value = tradingUnitStatMList.stream()
                            .filter(item -> {
                                // 解析时间字符串为日期对象
                                Date date1 = DateUtil.parse(item.get("ts").toString(), pattern);
                                // 减去 900000 毫秒（15 分钟）
                                Date adjustedDate = new Date(date1.getTime() - 900000L);
                                // 提取调整后日期的小时
                                int adjustedHour = DateUtil.hour(adjustedDate, true);
                                return adjustedHour == Integer.parseInt(hour);
                            })
                            .mapToDouble(item ->
                                    new BigDecimal(item.get("short_term_forecast").toString())
                                            .subtract(new BigDecimal(item.get("medium_long_term_settlement_curves").toString())).setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue())
                            .sum();
                    remainingSpace.add(value);
                    sum += value;
                }
                remainingSpace.add(sum);
                allData.add(remainingSpace);
            }
        }
        List<String> headers1 = new ArrayList<>();
        headers1.add("名称");
        headers1.addAll(headers);
        headers1.add("合计");
        map.put("header", headers1);
        map.put("remainingSpace", allData);
        return map;
    }

    @Override
    public void exportEcharts(HttpServletResponse response, HttpServletRequest request, Long[] date) {
        try {
            String fileNameDate;
            if (Objects.equals(date[0], date[1])) {
                fileNameDate = DateUtil.format(new Date(date[0]), "yyyy-MM-dd");
            } else {
                fileNameDate = DateUtil.format(new Date(date[0]), "yyyy-MM-dd") + "-" + DateUtil.format(new Date(date[1]), "yyyy-MM-dd");
            }
            ServletOutputStream out = null;
            // 1. 创建Workbook（.xlsx格式）
            XSSFWorkbook workbook = new XSSFWorkbook();
            // 2. 创建Sheet
            Sheet sheet1 = workbook.createSheet("日前市场出清节点电价");
            Sheet sheet2 = workbook.createSheet("实时市场出清节点电价");
            // 设置列宽（单位：1/256字符宽度）
            sheet1.setColumnWidth(0, 24 * 256);
            sheet1.setColumnWidth(1, 34 * 256);    // 第二列34字符
            sheet1.setColumnWidth(2, 20 * 256);
            for (int i = 3; i < 99 + 1; i++) { // 后续列
                sheet1.setColumnWidth(i, (int) (14.5 * 256));
            }

            // 同理设置 sheet2 的列宽
            sheet2.setColumnWidth(0, 23 * 256);
            sheet2.setColumnWidth(1, 34 * 256);
            sheet1.setColumnWidth(2, 20 * 256);
            for (int i = 3; i < 99 + 1; i++) {
                sheet2.setColumnWidth(i, (int) (14.5 * 256));
            }
            // 创建通用边框样式
            CellStyle borderStyle = workbook.createCellStyle();
            borderStyle.setBorderTop(BorderStyle.THIN);
            borderStyle.setBorderBottom(BorderStyle.THIN);
            borderStyle.setBorderLeft(BorderStyle.THIN);
            borderStyle.setBorderRight(BorderStyle.THIN);
            borderStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
            borderStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            borderStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            borderStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
            //日前数据
            Long[] date1 = date.clone();
            Map<String, List<Float>> hisMap = getSubEcharts(date, "0");
            Map<String, List<Float>> realMap = getSubEcharts(date1, "1");
            List<String> dateHeaders = new ArrayList<>();
            dateHeaders.add("业务名称");
            dateHeaders.add("设备名称");
            dateHeaders.add("日期");
            for (int hour = 0; hour <= 24; hour++) {
                if (hour == 0) {
                    // 0点只生成 0:15、0:30、0:45
                    for (int minute = 15; minute < 60; minute += 15) {
                        String time = String.format("%d:%02d", hour, minute);
                        dateHeaders.add(time);
                    }
                } else if (hour == 24) {
                    // 24点只生成 24:00
                    String time = String.format("%d:%02d", hour, 0);
                    dateHeaders.add(time);
                } else {
                    // 其他小时生成 0:00、0:15、0:30、0:45
                    for (int minute = 0; minute < 60; minute += 15) {
                        String time = String.format("%d:%02d", hour, minute);
                        dateHeaders.add(time);
                    }
                }
            }
            // 写入日前数据（Sheet1）
            writeDataToSheet(sheet1, hisMap, dateHeaders, "日前市场出清节点电价", borderStyle, fileNameDate);
            // 写入实时数据（Sheet2）
            writeDataToSheet(sheet2, realMap, dateHeaders, "实时市场出清节点电价", borderStyle, fileNameDate);
            String name = "市场出清节点电价 " + fileNameDate + ".xlsx";
            String fileName = URLEncoder.encode(name, StandardCharsets.UTF_8);
            response.setContentType("application/msexcel");
            //response.setContentType("multipart/form-data");
            response.addHeader("Content-Type", "application/octet-stream;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 通用数据写入方法
     *
     * @param sheet        目标Sheet
     * @param dataMap      数据集合（节点名称 -> 价格列表）
     * @param headers      时间列头
     * @param firstColName 第一列名称
     */
    private void writeDataToSheet(Sheet sheet, Map<String, List<Float>> dataMap, List<String> headers, String firstColName, CellStyle style, String fileNameDate) {
        // 创建表头行
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(25);
        style.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        createCellWithStyle(headerRow, 0, firstColName, style);
        for (int i = 0; i < headers.size(); i++) {
            createCellWithStyle(headerRow, i, headers.get(i), style);
        }

        // 写入数据行
        int rowNum = 1;
        for (Map.Entry<String, List<Float>> entry : dataMap.entrySet()) {
            Row row = sheet.createRow(rowNum++);
            row.setHeightInPoints(25);
            createCellWithStyle(row, 0, firstColName, style); // 业务名称
            createCellWithStyle(row, 1, entry.getKey(), style);// 节点名称
            createCellWithStyle(row, 2, fileNameDate, style);// 日期

            List<Float> prices = entry.getValue();
            for (int i = 0; i < prices.size(); i++) {
                createCellWithStyle(row, i + 3, prices.get(i).toString(), style);
            }
        }
    }

    private void createCellWithStyle(Row row, int column, String value, CellStyle style) {
        Cell cell = row.createCell(column);
        try {
            cell.setCellValue(Double.parseDouble(value));
        } catch (NumberFormatException e) {
            // 如果无法转换为数字，仍然按字符串处理
            cell.setCellValue(value);
        }
        cell.setCellStyle(style); // 应用统一边框样式
    }

    // 工具方法：保留三位小数
    private double roundToThreeDecimalPlaces(double value) {
        return BigDecimal.valueOf(value).setScale(3, RoundingMode.HALF_UP).doubleValue();
    }

    private BigDecimal parseNumber(String number) {
        return number.equals("--") ? BigDecimal.ZERO : new BigDecimal(number).setScale(4, RoundingMode.HALF_UP);
    }

}