package cn.iocoder.yudao.module.et.dal.dataobject.marketclearing;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.sql.Timestamp;

/**
 * 各节点出清类信息DO
 */
@TableName("market_clearing")
@KeySequence("market_clearing_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketClearingDO {
    private Timestamp ts;
    private String companyId;
    private String substationName;
    /**
     * 日前市场出清节点电价
     */
    private Float dayAheadNodalElectricityPrice;
    /**
     * 日前市场出清电能价格
     */
    private Float dayAheadElectricityPrice;
    /**
     * 日前市场出清阻塞价格
     */
    private Float dayAheadCongestionPrice;
    /**
     * 日前市场出清网损价格
     */
    private Float dayAheadNetworkLossPrice;
    /**
     * 日前市场出清总电量
     */
    private Float dayAheadTotalPowerQuantity;
    /**
     * 日前各类电源台数
     */
    private Float dayAheadQuantityOfVariousPowerSources;
    /**
     * 实时市场出清节点电价
     */
    private Float realNodalElectricityPrice;
    /**
     * 实时市场出清电能价格
     */
    private Float realElectricityPrice;
    /**
     * 实时市场出清阻塞价格
     */
    private Float realCongestionPrice;
    /**
     * 实时市场出清网损价格
     */
    private Float realNetworkLossPrice;
    /**
     * 实时市场出清总电量
     */
    private Float realTotalPowerQuantity;
    /**
     * 实时各类电源台数
     */
    private Float realQuantityOfVariousPowerSources;
    /**
     * 实时市场调频里程价格
     */
    private Float realFrequencyRegulationMileagePrice;

}
