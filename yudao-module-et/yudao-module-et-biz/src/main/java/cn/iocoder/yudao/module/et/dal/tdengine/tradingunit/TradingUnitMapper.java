package cn.iocoder.yudao.module.et.dal.tdengine.tradingunit;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.sql.Timestamp;
import java.util.List;

/**
 * 交易单元 Mapper
 *
 * <AUTHOR>
 */
@DS("tdengine")
@Mapper
public interface TradingUnitMapper extends BaseMapperX<TradingUnitDO> {
    @TenantIgnore
    @Select("select ts,unit_id,his_settlement_prices_limit,real_settlement_prices from trading_unit_${unitId} where ts >= #{monthDateStart} and ts <= #{monthDateEnd}")
    List<TradingUnitDO> getRealTimePriceComparisonByMonth(@Param("unitId") String unitId, @Param("monthDateStart") Timestamp monthDateStart, @Param("monthDateEnd") Timestamp monthDateEnd);

    @TenantIgnore
    @Select("select t1.ts, t1.unit_id, t1.his_settlement_prices_limit, t1.real_settlement_prices from trading_unit_${unitId} t1 where t1.ts >= #{startTime} and t1.ts<#{endTime}")
    List<TradingUnitDO> getRealTimePriceComparisonByDate(@Param("unitId") String unitId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

    //预测偏差分析
    @TenantIgnore
    @Select("SELECT ts,tmr_power, scada_power,deviation_rate,short_term_forecast FROM trading_unit_${unitId} t1 where t1.ts >= #{startTime} and t1.ts <= #{endTime}")
    List<TradingUnitDO> getDeviation(@Param("unitId") String unitId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

    //交易概览
    @TenantIgnore
    @Select("SELECT * FROM trading_unit_${unitId} t1 where t1.ts >= #{startTime} and t1.ts < #{endTime}")
    List<TradingUnitDO> getTransactionOverview(@Param("unitId") String unitId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);


    //发电量分析 月查询
    @TenantIgnore
    @Select("select ts,unit_id,scada_power,tmr_power,settlement_power from trading_unit_${unitId} where ts >= #{monthDateStart} and ts < #{monthDateEnd}")
    List<TradingUnitDO> getPowerGenerationByMonth(@Param("unitId") String unitId, @Param("monthDateStart") Timestamp monthDateStart, @Param("monthDateEnd") Timestamp monthDateEnd);

    //发电量分析 日查询
    @TenantIgnore
    @Select("select t1.ts, t1.unit_id, t1.scada_power, t1.tmr_power,t1.settlement_power from trading_unit_${unitId} t1 where t1.ts >= #{startTime} and t1.ts<#{endTime}")
    List<TradingUnitDO> getPowerGenerationByDate(@Param("unitId") String unitId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

    //交易复盘分析 按月查询
    @TenantIgnore
    @Select("SELECT ts,unit_id,scada_power, tmr_power,radix_power,delivery_power,bilateral_power,day_scroll_power,capacity" +
            ",long_term_contract_power,long_term_contract_price,deviation_review_prices,recovery_of_excess_profits_from_new_energy_issuance_prices" +
            ",his_positive_power,his_negative_power,real_positive_power,real_negative_power,his_market_settlement_prices,real_market_settlement_prices" +
            ",medium_long_term_blockage_prices,blocking_risk_hedging_prices,modified_compensation_prices,necessary_start_up_compensation_prices " +
            ",his_negative_prices,his_positive_prices,real_negative_prices,real_positive_prices,long_term_contract_power,long_term_contract_price " +
            ",emergency_invoke_start_up_compensation_prices ,thermal_power_start_up_compensation_prices,real_settlement_prices,his_settlement_prices_limit from trading_unit_${unitId} where ts >= #{monthDateStart} and ts < #{monthDateEnd}")
    List<TradingUnitDO> getReviewAnalysisByMonth(@Param("unitId") String unitId, @Param("monthDateStart") Timestamp monthDateStart, @Param("monthDateEnd") Timestamp monthDateEnd);

    //交易复盘分析 按日查询
    @TenantIgnore
    @Select("SELECT ts,unit_id,scada_power, scada_power,radix_power,delivery_power,bilateral_power,day_scroll_power,capacity" +
            ",long_term_contract_power,long_term_contract_price,deviation_review_prices,recovery_of_excess_profits_from_new_energy_issuance_prices" +
            ",his_positive_power,his_negative_power,real_positive_power,real_negative_power,his_market_settlement_prices,real_market_settlement_prices" +
            ",medium_long_term_blockage_prices,blocking_risk_hedging_prices,modified_compensation_prices,necessary_start_up_compensation_prices " +
            ",his_negative_prices,his_positive_prices,real_negative_prices,real_positive_prices,long_term_contract_power,long_term_contract_price " +
            ",emergency_invoke_start_up_compensation_prices ,thermal_power_start_up_compensation_prices,real_settlement_prices,his_settlement_prices_limit FROM trading_unit_${unitId} t1 where t1.ts >= #{startTime} and t1.ts < #{endTime}")
    List<TradingUnitDO> getReviewAnalysisByDay(@Param("unitId") String unitId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

    @TenantIgnore
    @Select("select ts,real_settlement_prices,his_market_settlement_prices,his_settlement_prices_limit from trading_unit_${unitId} where ts >= #{monthDateStart} and ts < #{monthDateEnd}")
    List<TradingUnitDO> getCurrentPrices(@Param("unitId") String unitId, @Param("monthDateStart") Timestamp timestamp, @Param("monthDateEnd") Timestamp timestamp1);

    @TenantIgnore
    @Select("select ts,medium_long_term_settlement_curves,self_plan,prebalance_plan,reliability_plan,bilateral_clearance,real_plan,real_scada_power,short_term_forecast,ultra_short_term_forecast,cross_provincial_spot_clearing_of_electricity,real_basis_clearance from trading_unit_${unitId} where ts >= #{monthDateStart} and ts < #{monthDateEnd}")
    List<TradingUnitDO> getClearanceMonitor(@Param("unitId") String unitId, @Param("monthDateStart") Timestamp timestamp, @Param("monthDateEnd") Timestamp timestamp1);

    @TenantIgnore
    @Select("select ts,medium_long_term_settlement_curves,short_term_forecast from trading_unit_${unitId} where ts >= #{monthDateStart} and ts < #{monthDateEnd}")
    List<TradingUnitDO> getRemainingSpace(@Param("unitId") String unitId, @Param("monthDateStart") Timestamp timestamp, @Param("monthDateEnd") Timestamp timestamp1);

    @TenantIgnore
    @Select("SELECT ts,unit_id,his_positive_power,his_negative_power,real_positive_power,real_negative_power,his_positive_prices " +
            ",his_negative_prices,real_positive_prices,real_negative_prices from trading_unit_${unitId} where ts >= #{monthDateStart} and ts < #{monthDateEnd}")
    List<TradingUnitDO> getSettlementRelease(@Param("unitId") String unitId, @Param("monthDateStart") Timestamp monthDateStart, @Param("monthDateEnd") Timestamp monthDateEnd);


    @TenantIgnore
    @Select("SELECT ts,unit_id,scada_power,tmr_power,settlement_power,real_use_power,calibration_power,settlement_power from trading_unit_${unitId} where ts >= #{monthDateStart} and ts < #{monthDateEnd}")
    List<TradingUnitDO> getSettlementElectricity(@Param("unitId") String unitId, @Param("monthDateStart") Timestamp monthDateStart, @Param("monthDateEnd") Timestamp monthDateEnd);


    @TenantIgnore
    @Select("SELECT ts,unit_id,his_positive_power,his_negative_power,real_positive_power,real_negative_power,his_positive_prices" +
            ",his_negative_prices,real_positive_prices,real_negative_prices" +
            ",his_generate_electricity_plan,medium_long_term_plan,his_province_clearance,his_market_settlement_prices" +
            ",medium_long_term_blockage_prices,blocking_risk_hedging_prices" +
            ",real_province_clearance,cross_provincial_peak_load_balancing_plan,cross_provincial_peak_load_balancing_return,real_settlement_power" +
            ",emergency_invoke_start_up_compensation_prices,thermal_power_start_up_compensation_prices,deviation_review_prices" +
            ",recovery_of_excess_profits_from_new_energy_issuance_prices" +
            ",modified_compensation_prices,necessary_start_up_compensation_prices" +
            ",real_market_settlement_prices,his_standby_clearance,real_standby_clearance" +
            " from trading_unit_${unitId} where ts >= #{monthDateStart} and ts < #{monthDateEnd}")
    List<TradingUnitDO> getSettlementReleaseSum(@Param("unitId") String unitId, @Param("monthDateStart") Timestamp monthDateStart, @Param("monthDateEnd") Timestamp monthDateEnd);

    //阻塞对冲机制费用计算
    @TenantIgnore
    @Select("SELECT ts,settlement_power,tmr_power, scada_power,long_term_contract_power FROM trading_unit_${unitId} t1 where t1.ts >= #{startTime} and t1.ts <= #{endTime}")
    List<TradingUnitDO> getCongestionCost(@Param("unitId") String unitId, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

    @TenantIgnore
    @Select("SELECT ts,his_settlement_prices_limit,real_settlement_prices FROM trading_unit_${unitId} where ts >= #{monthDateStart} and ts < #{monthDateEnd}")
    List<TradingUnitDO> getTradingUnit(@Param("unitId") String unitId,
                                       @Param("monthDateStart") Timestamp monthDateStart,
                                       @Param("monthDateEnd") Timestamp monthDateEnd);


    @TenantIgnore
    @Select("SELECT ts,${columns} FROM trading_unit_${unitId} where ts >= #{startTime} and ts < #{endTime}")
    List<TradingUnitDO> getTradingUnitByCountMonth(@Param("unitId") String unitId,
                                                   @Param("startTime") Timestamp startTime,
                                                   @Param("endTime") Timestamp endTime,
                                                   @Param("columns") String columns);

    @TenantIgnore
    @Select("SELECT ts,#{field1} FROM trading_unit_${unitId} t1 where t1.ts >= #{startTime} and t1.ts <= #{endTime}")
    List<TradingUnitDO> getDynamicField(@Param("unitId") String unitId, @Param("field1") String field1, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

    @TenantIgnore
    @Select("select * from trading_unit")
    List<TradingUnitDO> getTradingUnitListByTenantIgnore();


}