package cn.iocoder.yudao.module.et.dal.mysql.sectionlimitchange;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.et.dal.dataobject.sectionlimitchange.SectionLimitChangeDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

/**
 * SectionLimitChangeMapper
 *
 * <AUTHOR>
 **/
@Deprecated
@DS("shardingsphereDB")
@Mapper
public interface SectionLimitChangeMapper extends BaseMapperX<SectionLimitChangeDO> {
}
