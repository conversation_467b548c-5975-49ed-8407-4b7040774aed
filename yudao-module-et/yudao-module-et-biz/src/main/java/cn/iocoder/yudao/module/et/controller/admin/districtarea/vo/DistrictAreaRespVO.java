package cn.iocoder.yudao.module.et.controller.admin.districtarea.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 省份对应算法类型 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DistrictAreaRespVO {

    @Schema(description = "区域代码，甘肃-河东、甘肃-河西、内蒙古-蒙东", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("区域代码，甘肃-河东、甘肃-河西、内蒙古-蒙东")
    private String code;

    @Schema(description = "区域简称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("区域简称")
    private String name;

    @Schema(description = "区域全称", example = "赵六")
    @ExcelProperty("区域全称")
    private String fullName;

    @Schema(description = "所属行政区代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("所属行政区代码")
    private String districtCode;

    @Schema(description = "算法，normal：正位预测算法；cross：错位预测算法")
    @ExcelProperty("算法，normal：正位预测算法；cross：错位预测算法")
    private String predictionModel;

}