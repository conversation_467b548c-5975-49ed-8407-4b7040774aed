package cn.iocoder.yudao.module.et.dal.dataobject.spotTradingFiling;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.checkerframework.checker.formatter.qual.Format;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("jy_spot_trading_filing")
public class SpotTradingFilingDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 交易段编号
     */
    @TableField("segment_no")
    private String segmentNo;

    /**
     * 起始出力
     */
    @TableField("initial_output")
    private BigDecimal initialOutput;


    /**
     * 终止出力(MW)
     */
    @TableField("final_output")
    private BigDecimal finalOutput;

    /**
     * 报价(元/MWH)
     */
    @TableField("quote_price")
    private BigDecimal quotePrice;


    /**
     * 申报日期
     */
    @TableField("created_at")
    private String createdAt;

    @TableField("tenant_id")
    private Integer tenantId;

}