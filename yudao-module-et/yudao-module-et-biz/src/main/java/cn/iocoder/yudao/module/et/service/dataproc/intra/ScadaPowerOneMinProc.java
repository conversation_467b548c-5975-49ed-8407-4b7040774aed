package cn.iocoder.yudao.module.et.service.dataproc.intra;

import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.jy.soft.remote.bean.SocketPacket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * GS-B-262 其它发布信息-一分钟SCADA电力展示
 *
 * <AUTHOR>
 * @date 2024-11-30
 **/
@Slf4j
@Service
public class ScadaPowerOneMinProc implements BaseDataProc {
    @Override
    public boolean execute(SocketPacket dto) {
        return true;
    }
}
