package cn.iocoder.yudao.module.et.controller.admin.contractsequipment.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 合同机组信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContractsEquipmentRespVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "27273")
    @ExcelProperty("自增主键")
    private Long id;

    @Schema(description = "合同角色", example = "王五")
    @ExcelProperty("合同角色")
    private String contractsRoleName;

    @Schema(description = "机组名称", example = "张三")
    @ExcelProperty("机组名称")
    private String equipmentName;

    @Schema(description = "机组类型", example = "2")
    @ExcelProperty("机组类型")
    private String equipmentType;

    @Schema(description = "装机容量")
    @ExcelProperty("装机容量")
    private BigDecimal quantity;

    @Schema(description = "批复电价", example = "580")
    @ExcelProperty("批复电价")
    private BigDecimal approvedPrice;

    @Schema(description = "机组电量")
    @ExcelProperty("机组电量")
    private BigDecimal equipmentGen;

    @Schema(description = "时段名称", example = "王五")
    @ExcelProperty("时段名称")
    private String timeSlotName;

    @Schema(description = "时段起止时间")
    @ExcelProperty("时段起止时间")
    private String timeSlotRange;

    @Schema(description = "部门ID", example = "5428")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    @ExcelProperty("上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    @ExcelProperty("数据爬取时间")
    private LocalDateTime gatherDate;

    @Schema(description = "修改时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("修改时间")
    private LocalDateTime modificationTime;

    @Schema(description = "开始日期")
    @ExcelProperty("开始日期")
    private LocalDateTime beginTime;

    @Schema(description = "结束日期")
    @ExcelProperty("结束日期")
    private LocalDateTime endTime;

    @Schema(description = "合同id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1244")
    @ExcelProperty("合同id")
    private String contractsId;

}