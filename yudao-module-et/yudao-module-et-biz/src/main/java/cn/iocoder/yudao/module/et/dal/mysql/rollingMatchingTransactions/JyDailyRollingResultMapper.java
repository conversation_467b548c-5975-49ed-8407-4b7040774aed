package cn.iocoder.yudao.module.et.dal.mysql.rollingMatchingTransactions;

import cn.iocoder.yudao.module.et.dal.dataobject.rollingMatchingTransactions.JyDailyRollingResult;
import cn.iocoder.yudao.module.et.dal.dataobject.rollingMatchingTransactions.JyDailyRollingResultDetailDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * PeriodicSettlementDetailMapper
 **/
@DS("shardingsphereDB")
@Mapper
public interface JyDailyRollingResultMapper extends BaseMapper<JyDailyRollingResult> {
}
