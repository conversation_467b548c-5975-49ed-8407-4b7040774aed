package cn.iocoder.yudao.module.et.dal.mysql.timeslots;

import java.time.LocalTime;
import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.dal.dataobject.timeslots.TimeSlotsDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingmarket.TradingMarketDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.et.controller.admin.timeslots.vo.*;
import org.apache.ibatis.annotations.Select;

/**
 * 省份峰谷平时段管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TimeSlotsMapper extends BaseMapperX<TimeSlotsDO> {

    default PageResult<TimeSlotsDO> selectPage(TimeSlotsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TimeSlotsDO>()
                .betweenIfPresent(TimeSlotsDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(TimeSlotsDO::getType, reqVO.getType())
                .betweenIfPresent(TimeSlotsDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(TimeSlotsDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(TimeSlotsDO::getCode, reqVO.getCode())
                .orderByDesc(TimeSlotsDO::getId));
    }

    @Select("SELECT * FROM jy_time_slots WHERE tenant_id = #{tenantId} and code = #{code}")
    @TenantIgnore
    List<TimeSlotsDO>  selectByTenantId( Long tenantId , String code);

    @Delete("delete from jy_time_slots where start_time BETWEEN #{startTime} and #{endTime} and code = #{code} and tenant_id = #{tenantId}")
    @TenantIgnore
    int deleteByTime(LocalTime startTime, LocalTime endTime, String code, Long tenantId);

    @Delete("delete from jy_time_slots where start_time = #{startTime} and end_time =  #{endTime}")
    @TenantIgnore
    int deleteByTimes(LocalTime startTime, LocalTime endTime);

    @Delete("delete from jy_time_slots where  tenant_id = #{tenantId}")
    int deleteAll( Long tenantId);
}