package cn.iocoder.yudao.module.et.dal.mysql.reviewInfo;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.reviewinfo.vo.ReviewInfoVO;
import cn.iocoder.yudao.module.et.dal.dataobject.review.ReviewInfo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 场站信息 Mapper
 *
 * <AUTHOR>
 */
@DS("shardingsphereDB")
@Mapper
public interface ReviewInfoMapper extends BaseMapperX<ReviewInfo> {
    @TenantIgnore
    default List<ReviewInfo> list(ReviewInfoVO reviewInfoVO) {
        LambdaQueryWrapper<ReviewInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (reviewInfoVO.getDate() != null) {
            Date start = new Date(reviewInfoVO.getDate()[0]);
            Date end = new Date(reviewInfoVO.getDate()[1] + 86400000L - 1000L);
            lambdaQueryWrapper.between(ReviewInfo::getReviewDate, start, end);
        }
        if (!Objects.equals(reviewInfoVO.getOperatingDayPersons(), "")) {
            lambdaQueryWrapper.like(ReviewInfo::getOperatingDayPersons, reviewInfoVO.getOperatingDayPersons());
        }
        if (!Objects.equals(reviewInfoVO.getDutyPersons(), "")) {
            lambdaQueryWrapper.like(ReviewInfo::getDutyPersons, reviewInfoVO.getDutyPersons());
        }
        if (!Objects.equals(reviewInfoVO.getType(), "")) {
            lambdaQueryWrapper.like(ReviewInfo::getType, Integer.parseInt(reviewInfoVO.getType()));
        }
        return selectList(lambdaQueryWrapper);
    }

}