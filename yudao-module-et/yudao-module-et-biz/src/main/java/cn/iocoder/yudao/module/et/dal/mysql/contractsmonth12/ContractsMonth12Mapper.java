package cn.iocoder.yudao.module.et.dal.mysql.contractsmonth12;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.et.controller.admin.contractsmonth12.vo.ContractsMonth12PageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsmonth12.ContractsMonth12DO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 合同分月信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("shardingsphereDB")
public interface ContractsMonth12Mapper extends BaseMapperX<ContractsMonth12DO> {

    default PageResult<ContractsMonth12DO> selectPage(ContractsMonth12PageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContractsMonth12DO>()
                .eqIfPresent(ContractsMonth12DO::getQuantity, reqVO.getQuantity())
                .eqIfPresent(ContractsMonth12DO::getPrice, reqVO.getPrice())
                .eqIfPresent(ContractsMonth12DO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(ContractsMonth12DO::getOrgCode, reqVO.getOrgCode())
                .betweenIfPresent(ContractsMonth12DO::getGatherDate, reqVO.getGatherDate())
                .betweenIfPresent(ContractsMonth12DO::getModificationTime, reqVO.getModificationTime())
                .betweenIfPresent(ContractsMonth12DO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(ContractsMonth12DO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(ContractsMonth12DO::getContractsId, reqVO.getContractsId())
                .orderByDesc(ContractsMonth12DO::getId));
    }

    @Select("SELECT * FROM jy_contracts_month12 WHERE contracts_id = #{id} and deleted = 0")
    List<ContractsMonth12DO> selectListByContractsId(String id);
}