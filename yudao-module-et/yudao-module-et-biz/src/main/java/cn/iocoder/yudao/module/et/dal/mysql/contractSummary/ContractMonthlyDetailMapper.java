package cn.iocoder.yudao.module.et.dal.mysql.contractSummary;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.et.dal.dataobject.contractSummary.ContractMonthlyDetailDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

/**
 * ContractMonthlyDetailMapper
 **/
@DS("shardingsphereDB")
@Mapper
public interface ContractMonthlyDetailMapper extends BaseMapperX<ContractMonthlyDetailDO> {
}
