package cn.iocoder.yudao.module.et.dal.mysql.contractSummary;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.et.dal.dataobject.contractSummary.ContractMonthlyDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

/**
 * ContractMonthlyMapper
 **/
@DS("shardingsphereDB")
@Mapper
public interface ContractMonthlyMapper extends BaseMapperX<ContractMonthlyDO> {
}
