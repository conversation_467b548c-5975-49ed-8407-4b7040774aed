package cn.iocoder.yudao.module.et.dal.dataobject.contractsmember;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合同方信息 DO
 *
 * <AUTHOR>
 */
@TableName("jy_contracts_member")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractsMemberDO {

    /**
     * 自增主键
     */
    @TableId
    private Long id;
    /**
     * 售电方
     */
    private String sellerName;
    /**
     * 售电单元
     */
    private String sellerTradingUnitName;
    /**
     * 售方电价
     */
    private BigDecimal sellerPrice;
    /**
     * 售方电量
     */
    private BigDecimal sellerQuantity;
    /**
     * 售方调整系数
     */
    private Double sellerCoefficient;
    /**
     * 售方市场主体id
     */
    private Long sellerTradingUnitId;
    /**
     * 并网时间
     */
    private LocalDateTime gridTime;
    /**
     * 购电方
     */
    private String purchaserName;
    /**
     * 购电单元
     */
    private String purchaserTradingUnitName;
    /**
     * 购方电价
     */
    private BigDecimal purchaserPrice;
    /**
     * 购方电量
     */
    private BigDecimal purchaserQuantity;
    /**
     * 购方调整系数
     */
    private Double purchaserCoefficient;
    /**
     * 购方市场主体id
     */
    private Long purchaserTradingUnitId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherDate;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
    /**
     * 合同id
     */
    private String contractsId;

    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;

}