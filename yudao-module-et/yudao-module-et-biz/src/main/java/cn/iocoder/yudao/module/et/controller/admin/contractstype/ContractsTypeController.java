package cn.iocoder.yudao.module.et.controller.admin.contractstype;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.contractstype.vo.ContractsTypePageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractstype.vo.ContractsTypeRespVO;
import cn.iocoder.yudao.module.et.controller.admin.contractstype.vo.ContractsTypeSaveReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractstype.vo.ContractsTypeVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstype.ContractsTypeDO;
import cn.iocoder.yudao.module.et.service.contractstype.ContractsTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合同类型")
@RestController
@RequestMapping("/et/contracts-type")
@Validated
public class ContractsTypeController {

    @Resource
    private ContractsTypeService contractsTypeService;

    @PostMapping("/create")
    @Operation(summary = "创建合同类型")
    @PreAuthorize("@ss.hasPermission('jy:contracts-type:create')")
    public CommonResult<Long> createContractsType(@Valid @RequestBody ContractsTypeSaveReqVO createReqVO) {
        return success(contractsTypeService.createContractsType(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新合同类型")
    @PreAuthorize("@ss.hasPermission('jy:contracts-type:update')")
    public CommonResult<Boolean> updateContractsType(@Valid @RequestBody ContractsTypeSaveReqVO updateReqVO) {
        contractsTypeService.updateContractsType(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除合同类型")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('jy:contracts-type:delete')")
    public CommonResult<Boolean> deleteContractsType(@RequestParam("id") Long id) {
        contractsTypeService.deleteContractsType(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得合同类型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('jy:contracts-type:query')")
    public CommonResult<ContractsTypeRespVO> getContractsType(@RequestParam("id") Long id) {
        ContractsTypeDO contractsType = contractsTypeService.getContractsType(id);
        return success(BeanUtils.toBean(contractsType, ContractsTypeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得合同类型分页")
    @PreAuthorize("@ss.hasPermission('jy:contracts-type:query')")
    public CommonResult<PageResult<ContractsTypeRespVO>> getContractsTypePage(@Valid ContractsTypePageReqVO pageReqVO) {
        PageResult<ContractsTypeDO> pageResult = contractsTypeService.getContractsTypePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContractsTypeRespVO.class));
    }

    @GetMapping("/getAll")
    @Operation(summary = "获得合同类型分页")
    //@PreAuthorize("@ss.hasPermission('jy:contracts-type:query')")
    public CommonResult<List<ContractsTypeRespVO>> getContractsTypeAllList() {
        List<ContractsTypeVO> pageResult = contractsTypeService.getContractsTypeList();
        return success(BeanUtils.toBean(pageResult, ContractsTypeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出合同类型 Excel")
    @PreAuthorize("@ss.hasPermission('jy:contracts-type:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContractsTypeExcel(@Valid ContractsTypePageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ContractsTypeDO> list = contractsTypeService.getContractsTypePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "合同类型.xls", "数据", ContractsTypeRespVO.class,
                BeanUtils.toBean(list, ContractsTypeRespVO.class));
    }

}