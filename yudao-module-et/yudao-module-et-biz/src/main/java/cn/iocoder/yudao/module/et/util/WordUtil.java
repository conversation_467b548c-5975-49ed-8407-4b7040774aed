package cn.iocoder.yudao.module.et.util;

import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.checkerframework.checker.units.qual.C;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtils;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.DateAxis;
import org.jfree.chart.axis.DateTickUnit;
import org.jfree.chart.axis.DateTickUnitType;
import org.jfree.chart.plot.XYPlot;
import org.jfree.chart.title.LegendTitle;
import org.jfree.data.time.Hour;
import org.jfree.data.time.Minute;
import org.jfree.data.time.TimeSeries;
import org.jfree.data.time.TimeSeriesCollection;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WordUtil {

    // 预编译正则表达式，精准匹配 ${key} 格式的占位符
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\$\\{([^}]*)}");
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{\\s*([^}\\s]+)\\s*}");

    /**
     * 替换文档中的文本占位符
     */
   /* public static void replaceDocumentText(XWPFDocument document, Map<String, Object> dataMap) {
        // 替换段落中的占位符
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            for (XWPFRun run : paragraph.getRuns()) {
                String text = run.getText(0);
                if (text != null) {
                    String replacedText = replacePlaceholders(text, dataMap);
                    run.setText(replacedText, 0);
                }
            }
        }

        // 替换表格中的占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        for (XWPFRun run : paragraph.getRuns()) {
                            String text = run.getText(0);
                            if (text != null) {
                                String replacedText = replacePlaceholders(text, dataMap);
                                run.setText(replacedText, 0);
                            }
                        }
                    }
                }
            }
        }
    }
*/

    /**
     * 获取包含指定占位符的段落索引
     */
    public static int findPlaceholderParagraphIndex(XWPFDocument document, String placeholder) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph para = paragraphs.get(i);
            StringBuilder paraText = new StringBuilder();

            // 合并段落中所有 Run 的文本（处理跨 Run 的占位符）
            for (XWPFRun run : para.getRuns()) {
                String runText = run.getText(0);
                if (runText != null) {
                    paraText.append(runText);
                }
            }

            // 检查段落文本是否包含完整的占位符（支持前后有空格/换行）
            if (paraText.toString().trim().equals(placeholder.trim())) {
                return i; // 精确匹配（去除前后空格后完全一致）
            }
            // 或使用模糊匹配（只要段落中包含占位符字符串）：
            // if (paraText.toString().contains(placeholder)) { return i; }
        }
        return -1; // 未找到占位符段落
    }

    /**
     * 仅替换 ${key} 格式的变量，保留其他文本不变
     */
    public static String replacePlaceholders(String template, Map<String, String> dataMap) {
        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        StringBuilder result = new StringBuilder();
        int start = 0;
        if (!template.equals("${strategyTable}")) {
            while (matcher.find()) {
                result.append(template, start, matcher.start()); // 添加占位符前的文本
                String value = dataMap.getOrDefault(matcher.group(1), ""); // 安全取值（默认空字符串）
                result.append(value); // 追加替换值
                start = matcher.end(); // 更新匹配起点
            }
        }
        result.append(template, start, template.length()); // 添加剩余文本
        return result.toString();
    }

    public static File generatePriceChart(Map<String, List<Float>> chartData) throws IOException {

        // 创建时间序列数据集
        TimeSeriesCollection dataset = createTimeSeriesDataset(chartData);

        // 2. 创建时间序列图表
        JFreeChart chart = ChartFactory.createTimeSeriesChart(
                "",         // 标题
                "",         // X轴标签
                "",         // Y轴标签
                dataset,
                true,       // 显示图例
                true,       // 显示工具提示
                false       // 不显示URL
        );
        LegendTitle legend = chart.getLegend();
        if (legend!=null) {
            legend.setItemFont(new Font("宋体", Font.PLAIN, 14));
        }
        // 3. 设置全局样式
        chart.setBackgroundPaint(Color.WHITE);

        // 4. 获取绘图区域并设置背景
        XYPlot plot = chart.getXYPlot();
        plot.setBackgroundPaint(Color.WHITE);
        plot.setDomainGridlinePaint(Color.LIGHT_GRAY);
        plot.setRangeGridlinePaint(Color.LIGHT_GRAY);

        // 5. 设置 X 轴为时间轴，并调整刻度间隔
        DateAxis xAxis = (DateAxis) plot.getDomainAxis();
        xAxis.setDateFormatOverride(new SimpleDateFormat("HH:mm")); // 时间格式
        xAxis.setVerticalTickLabels(true); // 垂直标签避免重叠
        // 6. 设置时间间隔（每1小时显示一个标签）
        xAxis.setTickUnit(new DateTickUnit(DateTickUnitType.MINUTE, 30));
        xAxis.setTickMarksVisible(true);
        // 调整图表的范围，避免前后出现不必要的空白
        xAxis.setLowerMargin(0.0); // 左边界距
        xAxis.setUpperMargin(0.0); // 右边界距
        // 其他样式设置（保持原有逻辑）
        chart.setBackgroundPaint(Color.WHITE);
        plot.setBackgroundPaint(Color.WHITE);
        plot.setDomainGridlinePaint(Color.LIGHT_GRAY);
        plot.setRangeGridlinePaint(Color.LIGHT_GRAY);

        // 生成临时文件
        File tempFile = File.createTempFile("chart", ".png");
        ChartUtils.saveChartAsPNG(tempFile, chart, 1000, 400);
        return tempFile;
    }

    // 生成从 0:15 到 24:00 的标签列表
    private static List<String> generateTimeLabels() {
        List<String> labels = new ArrayList<>();
        for (int hour = 0; hour < 24; hour++) {
            labels.add(String.format("%02d:15", hour));
            labels.add(String.format("%02d:30", hour));
            labels.add(String.format("%02d:45", hour));
            labels.add(String.format("%02d:00", hour + 1)); // 例如 0:00 转为 24:00
        }
        // 替换最后一个标签为 24:00
        labels.set(labels.size() - 1, "24:00");
        return labels;
    }

    // 创建时间序列数据集
    private static TimeSeriesCollection createTimeSeriesDataset(Map<String, List<Float>> chartData) {
        TimeSeriesCollection dataset = new TimeSeriesCollection();

        List<String> timeLabels = generateTimeLabels();
        for (Map.Entry<String, List<Float>> entry : chartData.entrySet()) {
            String seriesName = entry.getKey();
            List<Float> values = entry.getValue();

            TimeSeries series = new TimeSeries(seriesName);
            for (int i = 0; i < values.size(); i++) {
                String timeLabel = timeLabels.get(i);
                String[] parts = timeLabel.split(":");
                int hour = Integer.parseInt(parts[0]);
                int minute = Integer.parseInt(parts[1]);
                Minute minuteObj = new Minute(minute, new Hour(hour, new org.jfree.data.time.Day(1, 1, 2023)));// 使用一个固定的日期
                series.add(minuteObj, values.get(i));
            }
            dataset.addSeries(series);
        }
        return dataset;
    }
}
