package cn.iocoder.yudao.module.et.controller.admin.districtcode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 行政区划新增/修改 Request VO")
@Data
public class DistrictCodeSaveReqVO {

    @Schema(description = "行政区划代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer code;

    @Schema(description = "名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "名字不能为空")
    private String name;

    @Schema(description = "等级:1-省级;2-地级市;3-区/县;4-乡/镇", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "等级:1-省级;2-地级市;3-区/县;4-乡/镇不能为空")
    private Integer level;

    @Schema(description = "类型:1-省;2-自治区;3-直辖市;4-特别行政区;5-地级市;6-地区;7-自治州;8-盟;9-市辖区;10-县;11- 县级市;12-自治县;13-旗;14-自治旗;15-特区;16-林区", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "类型:1-省;2-自治区;3-直辖市;4-特别行政区;5-地级市;6-地区;7-自治州;8-盟;9-市辖区;10-县;11- 县级市;12-自治县;13-旗;14-自治旗;15-特区;16-林区不能为空")
    private Integer type;

    @Schema(description = "简称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "简称不能为空")
    private String abname;

    @Schema(description = "所属行政区划代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "17855")
    @NotNull(message = "所属行政区划代码不能为空")
    private Integer pid;

    @Schema(description = "所属行政区划名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "所属行政区划名字不能为空")
    private String pname;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "备注不能为空")
    private String note;

    @Schema(description = "纬度")
    private Double lat;

    @Schema(description = "经度")
    private Double lng;

}