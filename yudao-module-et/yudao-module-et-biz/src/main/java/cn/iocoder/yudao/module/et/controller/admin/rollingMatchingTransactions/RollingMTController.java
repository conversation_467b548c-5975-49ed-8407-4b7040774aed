package cn.iocoder.yudao.module.et.controller.admin.rollingMatchingTransactions;


import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.et.controller.admin.seriescontracts.vo.SeriesContractsRespVO;
import cn.iocoder.yudao.module.et.dal.dataobject.seriescontracts.SeriesContractsDO;
import cn.iocoder.yudao.module.et.service.rollingMatchingTransactions.RollingMTService;
import cn.iocoder.yudao.module.et.service.rollingMatchingTransactions.vo.RollingMatchingVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "合约管理 - 滚动撮合交易")
@RestController
@RequestMapping("/et/rollingMatching/transactions")
@Validated
public class RollingMTController {

    @Autowired
    private RollingMTService rollingMTService;

    @PostMapping("/getJyMarcketNameChoose")
    @Operation(summary = "市场交易名称选择器")
    public CommonResult getJyMarcketNameChoose(@RequestBody RollingMatchingVO param) {
        return success(rollingMTService.getJyMarcketNameChoose(param));
    }




    @PostMapping("/getJyMarcketResultDateChoose")
    @Operation(summary = "交易结果标的日选择器")
    public CommonResult getJyMarcketResultDateChoose(@RequestBody RollingMatchingVO param) {
        return success(rollingMTService.getJyMarcketResultDateChoose(param));
    }


    @PostMapping("/getJyMarcketResultNameChoose")
    @Operation(summary = "交易结果名称选择器")
    public CommonResult getJyMarcketResultNameChoose(@RequestBody RollingMatchingVO param) {
        return success(rollingMTService.getJyMarcketResultNameChoose(param));
    }

    @PostMapping("/getJyMarcketResultaddTimeChoose")
    @Operation(summary = "交易结果申报日选择器")
    public CommonResult getJyMarcketResultaddTimeChoose(@RequestBody RollingMatchingVO param) {
        return success(rollingMTService.getJyMarcketResultaddTimeChoose(param));
    }

    @PostMapping("/getMarketTradingInfo")
    @Operation(summary = "市场交易信息列表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult getSeriesContracts(@RequestBody RollingMatchingVO param) {
        return success(rollingMTService.getSeriesContracts(param));
    }

    @PostMapping("/getJyDailyRollingResult")
    @Operation(summary = "交易结果")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult getJyDailyRollingResult(@RequestBody RollingMatchingVO param) {
        return success(rollingMTService.getJyDailyRollingResult(param));
    }


    @PostMapping("/getTradingInfoDetail")
    @Operation(summary = "交易明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult getTradingInfoDetail(@RequestBody RollingMatchingVO param) {
        return success(rollingMTService.getTradingInfoDetail(param));
    }
}
