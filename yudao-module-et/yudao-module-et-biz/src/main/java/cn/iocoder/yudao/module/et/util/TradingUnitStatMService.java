package cn.iocoder.yudao.module.et.util;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.DispatchNodeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunitcolumnsetting.TradingUnitColumnSettingDO;
import cn.iocoder.yudao.module.et.dal.mysql.dispatchnode.DispatchNodeMapper;
import cn.iocoder.yudao.module.et.dal.mysql.tradingunitcolumnsetting.TradingUnitColumnSettingMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.tradingunit.TradingUnitMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.tradingunitstatm.TradingUnitStatMMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Service
@Validated
@Slf4j
public class TradingUnitStatMService {
    @Resource
    private TradingUnitColumnSettingMapper tradingUnitColumnSettingMapper;
    @Resource
    private TradingUnitStatMMapper tradingUnitStatMMapper;
    @Resource
    private DispatchNodeMapper dispatchNodeMapper;
    @Resource
    private TradingUnitMapper tradingUnitMapper;


    // 将数据库字段名转换为 Java 属性名
    public static String[] convertToJavaFieldNames(String dbFieldNames) {
        String[] dbFields = dbFieldNames.split(",");
        String[] javaFields = new String[dbFields.length];
        for (int i = 0; i < dbFields.length; i++) {
            String[] parts = dbFields[i].split("_");
            StringBuilder sb = new StringBuilder(parts[0]);
            for (int j = 1; j < parts.length; j++) {
                sb.append(Character.toUpperCase(parts[j].charAt(0))).append(parts[j].substring(1));
            }
            javaFields[i] = sb.toString();
        }
        return javaFields;
    }

    // 将java字段名转换为 数据库字段名
    public static String camelToUnderline(String camelCase) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char ch = camelCase.charAt(i);
            if (Character.isUpperCase(ch)) {
                if (i > 0) {
                    result.append("_");
                }
                result.append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }
        return result.toString();
    }

    //将数据库字段转为java属性名
    public static String convertToJavaName(String name) {
        String[] parts = name.split("_");
        StringBuilder sb = new StringBuilder(parts[0]);
        for (int j = 1; j < parts.length; j++) {
            sb.append(Character.toUpperCase(parts[j].charAt(0))).append(parts[j].substring(1));
        }
        return sb.toString();
    }

    public static Map<Timestamp, Map<String, Double>> groupAndSumByFifteenMinutes(List<TradingUnitDO> tradingUnitDOList,
                                                                                  String[] javaFieldNames) {
        Map<Timestamp, Map<String, Double>> result = new HashMap<>();
        // 找到最早的时间
        Optional<TradingUnitDO> firstEntry = tradingUnitDOList.stream()
                .min(Comparator.comparing(TradingUnitDO::getTs));
        if (!firstEntry.isPresent()) {
            return result;
        }
        LocalDateTime firstDateTime = firstEntry.get().getTs().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        // 获取该月 1 号 00:15 作为基准
        LocalDateTime baseDate = firstDateTime.withDayOfMonth(1).withHour(0).withMinute(15).withSecond(0).withNano(0);
        // 生成从该月 1 号 00:15 到 2 号 00:00 所有 15 分钟间隔的时间点
        for (int i = 0; i < 96; i++) {
            LocalDateTime current = baseDate.plusMinutes(i * 15);
            Timestamp key = Timestamp.valueOf(current);
            result.put(key, new HashMap<>());
        }
        for (String javaFieldName : javaFieldNames) {
            try {
                // 获取对应的 getter 方法
                String getterName = "get" + javaFieldName.substring(0, 1).toUpperCase() + javaFieldName.substring(1);
                Method getterMethod = TradingUnitDO.class.getMethod(getterName);

                // 对每个 15 分钟间隔进行求和
                for (Map.Entry<Timestamp, Map<String, Double>> entry : result.entrySet()) {
                    Timestamp timeKey = entry.getKey();
                    LocalDateTime targetTime = timeKey.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

                    double sum = tradingUnitDOList.stream()
                            .filter(item -> {
                                LocalDateTime itemTime = item.getTs().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                                return itemTime.getHour() == targetTime.getHour()
                                        && itemTime.getMinute() == targetTime.getMinute();
                            })
                            .mapToDouble(item -> {
                                try {
                                    Object value = getterMethod.invoke(item);
                                    if (value instanceof String) {
                                        try {
                                            return Double.parseDouble((String) value);
                                        } catch (NumberFormatException e) {
                                            e.printStackTrace();
                                            return 0;
                                        }
                                    } else if (value instanceof Double) {
                                        return (double) value;
                                    }
                                    return 0;
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    return 0;
                                }
                            })
                            .sum();

                    entry.getValue().put(javaFieldName, sum);
                }
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            }
        }

        return result;
    }

    public static Map<Timestamp, Map<String, Double>> processData(List<TradingUnitDO> tradingUnitDOList, List<Map<String, Object>> tradingMonthList) {
        Map<Timestamp, Map<String, Double>> resultMap = new HashMap<>();

        for (Map<String, Object> monthMap : tradingMonthList) {
            Timestamp ts = (Timestamp) monthMap.get("ts");
            String timeKey = ts.toString().substring(11, 16);

            Map<String, Double> sumMap = new HashMap<>();
            for (String fieldName : monthMap.keySet()) {
                if (!"ts".equals(fieldName) && !"unit_id".equals(fieldName) && !"days".equals(fieldName)) {
                    sumMap.put(fieldName, Double.parseDouble(monthMap.get(fieldName).toString()));
                    for (TradingUnitDO tradingUnitDO : tradingUnitDOList) {
                        if (timeKey.equals(tradingUnitDO.getTs().toString().substring(11, 16))) {
                            try {
                                Field field = tradingUnitDO.getClass().getDeclaredField(convertToJavaName(fieldName));
                                field.setAccessible(true);
                                double value = Double.parseDouble(field.get(tradingUnitDO).toString());
                                sumMap.put(fieldName, sumMap.get(fieldName) + value);
                            } catch (NoSuchFieldException | IllegalAccessException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                } else if ("days".equals(fieldName)) {
                    sumMap.put(fieldName, Double.parseDouble(String.valueOf(monthMap.get(fieldName))) + 1);
                }
            }
            resultMap.put(ts, sumMap);
        }
        return resultMap;
    }

    public void tradingUnitStatM() {
        //查询配置的字段    没有数据就不统计
        List<TradingUnitColumnSettingDO> tradingUnitColumnSettingDOS = tradingUnitColumnSettingMapper.selectAll();
        if (!tradingUnitColumnSettingDOS.isEmpty()) {
            StringBuilder columns1 = new StringBuilder();
            //--------------------------开始判断是否需要增加统计数据列------------------------------------------
            //就一条数据。 全都配置在 column里 用,分割出所有需要统计的字段 20250304弃用
            //多条数据 每个字段一条 类型 周期 统计方式
            for (TradingUnitColumnSettingDO tradingUnitColumnSettingDO : tradingUnitColumnSettingDOS) {
                String column = tradingUnitColumnSettingDO.getStatisticType();
                //查询数据
                List<Map<String, Object>> describe = tradingUnitStatMMapper.getDescribe();
                List<String> columnList = new ArrayList<>();
                for (Map<String, Object> map : describe) {
                    if (!map.get("field").equals("ts") && !map.get("field").equals("unit_id")) {
                        columnList.add((String) map.get("field"));
                    }
                }
                //判断是否需要增加字段
                if (!columnList.contains(column)) {
                    tradingUnitStatMMapper.addColumn(column);
                    tradingUnitStatMMapper.addColumnCS(column);
                    log.info("交易单元数据月统计,已成功添加字段：{}", column);
                }
                columns1.append(column).append(",");
            }
            String columns = columns1.toString().replaceAll(",$", "");
            //--------------------------开始计算数据------------------------------------------
            //月数据为。每15分钟一个点 为该点的月合计值  月统计数据一共96条数据 2月1号00点15到2月2号00点00
            //先取出当月的统计数据 然后取出当天的数据进行相加 结果覆盖
            //当前月1号
            long beginOfMonth = JyDateTimeUtil.beginOfMonth(new Date()).getTime();
            //下个月1号
            long endOfMonth = JyDateTimeUtil.endOfMonth(new Date()).getTime();
            //昨天日0点0分
            long beginOfDay = JyDateTimeUtil.getMillisecondsSubDay() - 86400000L;
            //2025-02-01 00:15:00.0
            Timestamp startMonth = Timestamp.valueOf(DateUtil.formatDateTime(new Date(beginOfMonth + 900000L)));
            //2025-03-01 00:14:59.0
            Timestamp endMonth = Timestamp.valueOf(DateUtil.formatDateTime(new Date(endOfMonth + 900000L)));
            //昨天0点15
            Timestamp startDay = Timestamp.valueOf(DateUtil.formatDateTime(new Date(beginOfDay + 900000L)));
            //今天0点14分59秒
            Timestamp endDay = Timestamp.valueOf(DateUtil.formatDateTime(new Date(beginOfDay + 86400000L + 900000L - 1000L)));
            //所有交易单元
            List<DispatchNodeDO> dispatchNodeDOList = dispatchNodeMapper.selectAll();
            for (DispatchNodeDO d : dispatchNodeDOList) {
                //如果当月统计数据为空 则查询整月数据进行计算 2025-03-04修改 因现在可以动态配置多种统计方法 不进行累加计算了,每天都是整月统计
                //-----------------------------------整月统计---------------------------------------
                //查询交易单元整月数据 然后根据不同数据字段的统计周期进行筛选
                List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getTradingUnitByCountMonth(d.getCode(), startMonth, endMonth, columns);
                Map<Timestamp, Map<String, Double>> result = new HashMap<>();
                Map<Timestamp, Map<String, Double>> days = new HashMap<>();
                if (!tradingUnitDOList.isEmpty()) {
                    mapPutKey(tradingUnitDOList, result);
                    mapPutKey(tradingUnitDOList, days);
                    for (TradingUnitColumnSettingDO tradingUnitColumnSettingDO : tradingUnitColumnSettingDOS) {
                        //分组统计数据
                        groupFifteenMinutes(result, days, tradingUnitDOList, tradingUnitColumnSettingDO);
                    }
                }
                // 将结果存入tdengine
                for (Map.Entry<Timestamp, Map<String, Double>> entry : result.entrySet()) {
                    Timestamp ts = entry.getKey();
                    StringBuilder col = new StringBuilder();
                    StringBuilder value = new StringBuilder();
                    col.append("ts,");
                    value.append("'").append(ts.toString()).append("'").append(",");
                    //获取fieldEntry.getKey() 拼接到col里 每个字段拼接上逗号
                    for (Map.Entry<String, Double> fieldEntry : entry.getValue().entrySet()) {
                        col.append(camelToUnderline(fieldEntry.getKey())).append(",");
                        value.append(fieldEntry.getValue()).append(",");
                    }
                    tradingUnitStatMMapper.insert(d.getCode(), col.toString().replaceAll(",$", ""), value.toString().replaceAll(",$", ""));
                }
                for (Map.Entry<Timestamp, Map<String, Double>> entry : days.entrySet()) {
                    Timestamp ts = entry.getKey();
                    StringBuilder col = new StringBuilder();
                    StringBuilder value = new StringBuilder();
                    col.append("ts,");
                    value.append("'").append(ts.toString()).append("'").append(",");
                    //获取fieldEntry.getKey() 拼接到col里 每个字段拼接上逗号
                    for (Map.Entry<String, Double> fieldEntry : entry.getValue().entrySet()) {
                        col.append(camelToUnderline(fieldEntry.getKey())).append(",");
                        value.append(fieldEntry.getValue()).append(",");
                    }
                    tradingUnitStatMMapper.insertCS(d.getCode(), col.toString().replaceAll(",$", ""), value.toString().replaceAll(",$", ""));
                }
                log.info("{}{}交易单元数据月统计统计完成. 日期范围:{} - {}", d.getCode(), d.getName(), startMonth, endMonth);
            }
        } else {
            log.info("交易单元数据月统计,MySQL库中jy_trading_unit_column_setting表没有配置统计字段. 请检查配置");
        }
    }

    /**
     * 初始化key
     * 放入数据开始当月1号的Timestamp的key
     *
     * @param tradingUnitDOList 数据集合
     * @param result            放入key的map
     */
    private void mapPutKey(List<TradingUnitDO> tradingUnitDOList, Map<Timestamp, Map<String, Double>> result) {
        // 找到最早的时间
        Optional<TradingUnitDO> firstEntry = tradingUnitDOList.stream()
                .min(Comparator.comparing(TradingUnitDO::getTs));
        LocalDateTime firstDateTime = firstEntry.get().getTs().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        // 获取该月 1 号 00:15 作为基准
        LocalDateTime baseDate = firstDateTime.withDayOfMonth(1).withHour(0).withMinute(15).withSecond(0).withNano(0);
        // 生成从该月 1 号 00:15 到 2 号 00:00 所有 15 分钟间隔的时间点
        for (int i = 0; i < 96; i++) {
            LocalDateTime current = baseDate.plusMinutes(i * 15);
            Timestamp key = Timestamp.valueOf(current);
            result.put(key, new HashMap<>());
        }
    }

    private void groupFifteenMinutes(Map<Timestamp, Map<String, Double>> result, Map<Timestamp, Map<String, Double>> days, List<TradingUnitDO> tradingUnitDOList, TradingUnitColumnSettingDO tradingUnitColumnSettingDO) {
        String mode = tradingUnitColumnSettingDO.getStatisticMode();
        int period = tradingUnitColumnSettingDO.getStatisticPeriod();
        String columnName = tradingUnitColumnSettingDO.getStatisticType();
        //数据结束日期 = 今天0点15分+统计周期
        Timestamp day = Timestamp.valueOf(DateUtil.formatDateTime(new Date(JyDateTimeUtil.getMillisecondsSubDay() + 900000L + (period * 86400000L))));
        //通过过滤出数据结束日期之前的数据
        List<TradingUnitDO> collect = tradingUnitDOList.stream().filter(item -> item.getTs().compareTo(day) < 0).toList();
        //通过数据库字段转为java属性名
        String javaFieldName = convertToJavaName(columnName);
        try {
            // 获取对应的 getter 方法
            String getterName = "get" + javaFieldName.substring(0, 1).toUpperCase() + javaFieldName.substring(1);
            Method getterMethod = TradingUnitDO.class.getMethod(getterName);
            // 对每个 15 分钟间隔进行操作
            for (Map.Entry<Timestamp, Map<String, Double>> entry : result.entrySet()) {
                Timestamp timeKey = entry.getKey();
                LocalDateTime targetTime = timeKey.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                //根据mode的统计方式进行计算
                calculateByType(entry, mode, collect, targetTime, getterMethod, javaFieldName);
                days.get(timeKey).put(columnName, Double.parseDouble(String.valueOf(collect.size() / 96)));
            }
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
    }

    private void calculateByType(Map.Entry<Timestamp, Map<String, Double>> entry, String mode, List<TradingUnitDO> collect, LocalDateTime targetTime, Method getterMethod, String javaFieldName) {
        double[] values = collect.stream()
                .filter(item -> {
                    LocalDateTime itemTime = item.getTs().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                    return itemTime.getHour() == targetTime.getHour()
                            && itemTime.getMinute() == targetTime.getMinute();
                })
                .mapToDouble(item -> {
                    try {
                        Object value = getterMethod.invoke(item);
                        if (value instanceof String) {
                            try {
                                return Double.parseDouble((String) value);
                            } catch (NumberFormatException e) {
                                e.printStackTrace();
                                return 0;
                            }
                        } else if (value instanceof Double) {
                            return (double) value;
                        }
                        return 0;
                    } catch (Exception e) {
                        e.printStackTrace();
                        return 0;
                    }
                })
                .toArray();

        switch (mode) {
            case "sum" -> {
                double sum = Arrays.stream(values).sum();
                entry.getValue().put(javaFieldName, sum);
            }
            case "avg" -> {
                long count = values.length;
                if (count == 0) {
                    entry.getValue().put(javaFieldName, 0.0);
                } else {
                    double total = Arrays.stream(values).sum();
                    double avg = total / count;
                    entry.getValue().put(javaFieldName, avg);
                }
            }
            case "max" -> {
                double max = Arrays.stream(values).max().orElse(0);
                entry.getValue().put(javaFieldName, max);
            }
            case "min" -> {
                double min = Arrays.stream(values).min().orElse(0);
                entry.getValue().put(javaFieldName, min);
            }
        }
    }
}
