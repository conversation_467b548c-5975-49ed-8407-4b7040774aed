package cn.iocoder.yudao.module.et.controller.admin.contractsmember.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 合同方信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContractsMemberRespVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17438")
    @ExcelProperty("自增主键")
    private Long id;

    @Schema(description = "售电方", example = "赵六")
    @ExcelProperty("售电方")
    private String sellerName;

    @Schema(description = "售电单元", example = "李四")
    @ExcelProperty("售电单元")
    private String sellerTradingUnitName;

    @Schema(description = "售方电价", example = "4602")
    @ExcelProperty("售方电价")
    private BigDecimal sellerPrice;

    @Schema(description = "售方电量")
    @ExcelProperty("售方电量")
    private BigDecimal sellerQuantity;

    @Schema(description = "售方调整系数")
    @ExcelProperty("售方调整系数")
    private Double sellerCoefficient;

    @Schema(description = "售方市场主体id", example = "27271")
    @ExcelProperty("售方市场主体id")
    private Long sellerTradingUnitId;

    @Schema(description = "并网时间")
    @ExcelProperty("并网时间")
    private LocalDateTime gridTime;

    @Schema(description = "购电方", example = "李四")
    @ExcelProperty("购电方")
    private String purchaserName;

    @Schema(description = "购电单元", example = "赵六")
    @ExcelProperty("购电单元")
    private String purchaserTradingUnitName;

    @Schema(description = "购方电价", example = "15948")
    @ExcelProperty("购方电价")
    private BigDecimal purchaserPrice;

    @Schema(description = "购方电量")
    @ExcelProperty("购方电量")
    private BigDecimal purchaserQuantity;

    @Schema(description = "购方调整系数")
    @ExcelProperty("购方调整系数")
    private Double purchaserCoefficient;

    @Schema(description = "购方市场主体id", example = "17677")
    @ExcelProperty("购方市场主体id")
    private Long purchaserTradingUnitId;

    @Schema(description = "部门ID", example = "1243")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    @ExcelProperty("上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    @ExcelProperty("数据爬取时间")
    private LocalDateTime gatherDate;

    @Schema(description = "修改时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("修改时间")
    private LocalDateTime modificationTime;

    @Schema(description = "合同id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32087")
    @ExcelProperty("合同id")
    private String contractsId;

}