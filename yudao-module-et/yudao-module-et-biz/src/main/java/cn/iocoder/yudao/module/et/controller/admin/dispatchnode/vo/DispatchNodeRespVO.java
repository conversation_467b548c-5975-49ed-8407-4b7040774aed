package cn.iocoder.yudao.module.et.controller.admin.dispatchnode.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 交易单元 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DispatchNodeRespVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "7060")
    @ExcelProperty("自增主键")
    private Integer id;

    @Schema(description = "调度单元编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("调度单元编码")
    private String code;

    @Schema(description = "调度单元名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("调度单元名称")
    private String name;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "场站装机容量")
    @ExcelProperty("场站装机容量")
    private Double capacity;

    @Schema(description = "场站设备台数")
    @ExcelProperty("场站设备台数")
    private Integer quantity;

    @Schema(description = "二区现货系统账号")
    @ExcelProperty("二区现货系统账号")
    private String sysUser;

    @Schema(description = "二区现货系统密码")
    @ExcelProperty("二区现货系统密码")
    private String sysPwd;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否禁用")
    private Boolean enabled;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "部门ID", example = "26902")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    @ExcelProperty("上级机构唯一编码（dept扩展字段）")
    private String orgCode;

}