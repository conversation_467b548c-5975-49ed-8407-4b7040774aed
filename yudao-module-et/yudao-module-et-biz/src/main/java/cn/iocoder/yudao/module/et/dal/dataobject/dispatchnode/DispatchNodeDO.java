package cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 交易单元 DO
 *
 * <AUTHOR>
 */
@TableName("jy_dispatch_node")
@KeySequence("jy_dispatch_node_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DispatchNodeDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId
    private Integer id;
    /**
     * 调度单元编码
     */
    private String code;
    /**
     * 调度单元名称(简称)
     */
    private String name;
    /**
     * 调度单元全称
     */
    private String fullName;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 场站装机容量
     */
    private Double capacity;
    /**
     * 场站设备台数
     */
    private Integer quantity;
    /**
     * 二区现货系统账号
     */
    private String sysUser;
    /**
     * 二区现货系统密码
     */
    private String sysPwd;
    /**
     * 是否禁用
     */
    private Boolean enabled;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;

    private Integer tenantId;

}