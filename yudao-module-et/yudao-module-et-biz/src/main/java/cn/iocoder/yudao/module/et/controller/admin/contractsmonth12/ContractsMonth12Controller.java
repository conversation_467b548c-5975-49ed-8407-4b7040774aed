package cn.iocoder.yudao.module.et.controller.admin.contractsmonth12;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.contractsmonth12.vo.ContractsMonth12PageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractsmonth12.vo.ContractsMonth12RespVO;
import cn.iocoder.yudao.module.et.controller.admin.contractsmonth12.vo.ContractsMonth12SaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsmonth12.ContractsMonth12DO;
import cn.iocoder.yudao.module.et.service.contractsmonth12.ContractsMonth12Service;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合同分月信息")
@RestController
@RequestMapping("/jy/contracts-month12")
@Validated
public class ContractsMonth12Controller {

    @Resource
    private ContractsMonth12Service contractsMonth12Service;

    @PostMapping("/create")
    @Operation(summary = "创建合同分月信息")
    @PreAuthorize("@ss.hasPermission('jy:contracts-month12:create')")
    public CommonResult<Long> createContractsMonth12(@Valid @RequestBody ContractsMonth12SaveReqVO createReqVO) {
        return success(contractsMonth12Service.createContractsMonth12(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新合同分月信息")
    @PreAuthorize("@ss.hasPermission('jy:contracts-month12:update')")
    public CommonResult<Boolean> updateContractsMonth12(@Valid @RequestBody ContractsMonth12SaveReqVO updateReqVO) {
        contractsMonth12Service.updateContractsMonth12(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除合同分月信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('jy:contracts-month12:delete')")
    public CommonResult<Boolean> deleteContractsMonth12(@RequestParam("id") Long id) {
        contractsMonth12Service.deleteContractsMonth12(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得合同分月信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('jy:contracts-month12:query')")
    public CommonResult<ContractsMonth12RespVO> getContractsMonth12(@RequestParam("id") Long id) {
        ContractsMonth12DO contractsMonth12 = contractsMonth12Service.getContractsMonth12(id);
        return success(BeanUtils.toBean(contractsMonth12, ContractsMonth12RespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得合同分月信息分页")
    @PreAuthorize("@ss.hasPermission('jy:contracts-month12:query')")
    public CommonResult<PageResult<ContractsMonth12RespVO>> getContractsMonth12Page(@Valid ContractsMonth12PageReqVO pageReqVO) {
        PageResult<ContractsMonth12DO> pageResult = contractsMonth12Service.getContractsMonth12Page(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContractsMonth12RespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出合同分月信息 Excel")
    @PreAuthorize("@ss.hasPermission('jy:contracts-month12:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContractsMonth12Excel(@Valid ContractsMonth12PageReqVO pageReqVO,
                                            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ContractsMonth12DO> list = contractsMonth12Service.getContractsMonth12Page(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "合同分月信息.xls", "数据", ContractsMonth12RespVO.class,
                BeanUtils.toBean(list, ContractsMonth12RespVO.class));
    }

}