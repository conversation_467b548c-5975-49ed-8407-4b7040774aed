package cn.iocoder.yudao.module.et.dal.dataobject.districtarea;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 省份对应算法类型 DO
 *
 * <AUTHOR>
 */
@TableName("jy_district_area")
@KeySequence("jy_district_area_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistrictAreaDO {

    /**
     * 区域代码，甘肃-河东、甘肃-河西、内蒙古-蒙东
     */
    @TableId(type = IdType.INPUT)
    private String code;
    /**
     * 区域简称
     */
    private String name;
    /**
     * 区域全称
     */
    private String fullName;
    /**
     * 所属行政区代码
     */
    private String districtCode;
    /**
     * 算法，normal：正位预测算法；cross：错位预测算法
     */
    private String predictionModel;

}