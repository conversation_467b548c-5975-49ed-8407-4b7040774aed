package cn.iocoder.yudao.module.et.service.tradingunit;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.et.dal.dataobject.companyunit.CompanyUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.DispatchNodeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.marketclearing.MarketClearingDO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import cn.iocoder.yudao.module.et.dal.dataobject.substation.SubstationDO;
import cn.iocoder.yudao.module.et.dal.dataobject.timeslots.TimeSlotsDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingCalendar.JyTradingAnnouncementDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDailyDO;
import cn.iocoder.yudao.module.et.dal.mysql.tradingCalendar.TradingCalendarMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.companyunit.CompanyUnitMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.marketclearing.MarketClearingMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.tradingunit.TradingUnitDailyMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.tradingunit.TradingUnitMapper;
import cn.iocoder.yudao.module.et.service.companyunit.CompanyUnitService;
import cn.iocoder.yudao.module.et.service.dispatchnode.DispatchNodeService;
import cn.iocoder.yudao.module.et.service.marketClearing.MarketClearingService;
import cn.iocoder.yudao.module.et.service.powerstation.PowerStationService;
import cn.iocoder.yudao.module.et.service.substation.SubstationService;
import cn.iocoder.yudao.module.et.service.timeslots.TimeSlotsService;
import cn.iocoder.yudao.module.et.util.JyDateTimeUtil;
import cn.iocoder.yudao.module.et.util.MapSortUtils;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 交易单元 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TradingUnitServiceImpl implements TradingUnitService {

    private static final String[] TIME_POINTS = {
            "07:15", "08:15", "09:15", "10:15", "11:15", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00"
    };
    @Resource
    private TradingUnitMapper tradingUnitMapper;
    @Resource
    private TradingUnitDailyMapper tradingUnitDailyMapper;
    @Resource
    private TradingCalendarMapper tradingCalendarMapper;
    @Resource
    private DispatchNodeService dispatchNodeService;
    @Resource
    private DeptApi deptApi;
    @Resource
    private CompanyUnitService companyUnitService;
    @Resource
    private CompanyUnitMapper companyUnitMapper;
    @Resource
    private PowerStationService powerStationService;
    @Resource
    private SubstationService substationService;
    @Resource
    private MarketClearingMapper marketClearingMapper;
    @Resource
    private MarketClearingService marketClearingService;
    @Resource
    private TimeSlotsService timeSlotsService;
    @Resource
    private TenantService tenantService;

    /**
     * 从给定的tradingUnitDOList中提取每15分钟的数据
     */
    public static List<TradingUnitDO> extractFifteenMinuteData(List<TradingUnitDO> tradingUnitDOList) {
        List<TradingUnitDO> filteredList = new ArrayList<>();

        // 过滤出每15分钟的数据
        for (TradingUnitDO unit : tradingUnitDOList) {
            long timestampMillis = unit.getTs().getTime();
            Instant instant = Instant.ofEpochMilli(timestampMillis);
            LocalDateTime timeOfDay = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalTime time = timeOfDay.toLocalTime();

            // 计算是否为15分钟的倍数
            int minutes = time.getMinute();
            if (minutes % 15 == 0) {
                filteredList.add(unit);
            }
        }

        return filteredList;
    }

    public static Map<String, List<TradingUnitDO>> groupDataByDate(List<TradingUnitDO> extractList) {
        Map<String, List<TradingUnitDO>> groupedData = new HashMap<>();
        // 分组数据
        for (TradingUnitDO unit : extractList) {
            long timestampMillis = unit.getTs().getTime() - 900000;
            unit.setHisMarketSettlementPrices(unit.getHisSettlementPricesLimit().equals("--") ? null : new BigDecimal(unit.getHisSettlementPricesLimit()).setScale(2, RoundingMode.HALF_UP).floatValue());
            unit.setRealMarketSettlementPrices(unit.getRealSettlementPrices().equals("--") ? null : new BigDecimal(unit.getRealSettlementPrices()).setScale(2, RoundingMode.HALF_UP).floatValue());
            Instant instant = Instant.ofEpochMilli(timestampMillis);
            LocalDateTime timeOfDay = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            String dateKey = timeOfDay.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
            groupedData.computeIfAbsent(dateKey, k -> new ArrayList<>()).add(unit);
        }
        return groupedData;
    }

    public static Map<String, List<TradingUnitDO>> sortMapByDate(Map<String, List<TradingUnitDO>> originalMap) {
        // 将原始的 Map 转换为一个包含日期字符串和对应列表的列表
        List<Map.Entry<String, List<TradingUnitDO>>> entryList = new ArrayList<>(originalMap.entrySet());

        // 使用自定义比较器对列表进行排序
        Collections.sort(entryList, new Comparator<Map.Entry<String, List<TradingUnitDO>>>() {
            @Override
            public int compare(Map.Entry<String, List<TradingUnitDO>> e1, Map.Entry<String, List<TradingUnitDO>> e2) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
                LocalDate date1 = LocalDate.parse(e1.getKey(), formatter);
                LocalDate date2 = LocalDate.parse(e2.getKey(), formatter);
                return date1.compareTo(date2);
            }
        });

        // 将排序后的列表转换回 Map
        return entryList.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                (oldValue, newValue) -> oldValue, LinkedHashMap::new));
    }

    private static boolean containsChinese(String str) {
        Pattern pattern = Pattern.compile("[\\u4E00-\\u9FA5]");
        return pattern.matcher(str).find();
    }

    private BigDecimal parseNumber(String number) {
        return number.equals("--") ? BigDecimal.ZERO : new BigDecimal(number).setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 日前或实时节点价格查询
     *
     * @param unitId    场站id
     * @param monthDate 月份
     * @param date      日期
     * @param realORHis 日前还是实时
     * @return
     */
    @Override
    public Map<String, Object> getRealTimePriceComparison(String unitId, String monthDate, Long[] date, String realORHis) {
        List<TradingUnitDO> tradingUnitDOList;
        Arrays.sort(date);
        if (date.length == 0) {
            monthDate = monthDate + "-01 00:15:00";
            Timestamp startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            Timestamp endDate = new Timestamp(calendar.getTimeInMillis());
            tradingUnitDOList = tradingUnitMapper.getRealTimePriceComparisonByMonth(unitId, startDate, endDate);
        } else {
            for (int i = 0; i < date.length; i++) {
                date[i] = date[i] + 900000;
            }
            Timestamp[] timestamps = new Timestamp[date.length + 1];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            timestamps[date.length] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[date.length - 1] + 60 * 60 * 24 * 1000L - 1000)));
            tradingUnitDOList = tradingUnitMapper.getRealTimePriceComparisonByDate(unitId, timestamps[0], timestamps[timestamps.length - 1]);
        }
        //提取出每15分钟的数据
        List<TradingUnitDO> extractList = extractFifteenMinuteData(tradingUnitDOList);
        //筛选  如果按照日查询可能跳着查, 在写sql的时候使用的是最早的时间和最晚的时间范围查, 需要吧中间多查出来的数据删掉
        List<TradingUnitDO> filteredData = new ArrayList<>();
        if (date.length == 0) {
            filteredData = extractList;
        } else {
            for (Long dayTimestamp : date) {
                long dayStart = dayTimestamp;
                long dayEnd = dayStart + 24 * 60 * 60 * 1000 - 1000;
                filteredData.addAll(extractList.stream().filter(unit -> unit.getTs().getTime() >= dayStart && unit.getTs().getTime() < dayEnd).toList());
            }
        }
        //根据数据分割出每天的数据     00:15-00:00
        //先根据时间分组然后排序
        Map<String, List<TradingUnitDO>> result = sortMapByDate(groupDataByDate(filteredData));
        Map<String, Double> groupByHour;
        if (realORHis.equals("实时")) {
            groupByHour = filteredData.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getRealSettlementPrices()).doubleValue())));
        } else {
            groupByHour = filteredData.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getHisSettlementPricesLimit()).doubleValue())));
        }
        double valueAvg = 0;
        //排序
        Map<String, Object> sortedMap = new HashMap<>();
        for (Map.Entry<String, Double> entry : groupByHour.entrySet()) {
            String key = String.valueOf(Integer.parseInt(entry.getKey()) + 1);
            sortedMap.put(key, BigDecimal.valueOf(entry.getValue()).setScale(2, RoundingMode.HALF_UP));
        }
        for (Map.Entry<String, Double> entry : groupByHour.entrySet()) {
            String key = String.valueOf(Integer.parseInt(entry.getKey()) + 1);
            double value = new BigDecimal(sortedMap.get(key).toString()).divide(BigDecimal.valueOf(4).multiply(new BigDecimal(filteredData.size()).divide(new BigDecimal(96), 2, RoundingMode.HALF_UP)), 2, RoundingMode.HALF_UP).doubleValue();
            sortedMap.put(key, value);
            valueAvg += value;
        }
        sortedMap.put("均值", BigDecimal.valueOf(valueAvg).divide(BigDecimal.valueOf(24), 2, RoundingMode.HALF_UP));
        //根据租户id获取省编码GS
        //tenantService.getTenant(SecurityFrameworkUtils.getLoginUserTenantId()).getTradingMarketCode()
        //当前租户该省份的所有峰谷平时段
        List<TimeSlotsDO> timeSlotsDOList = timeSlotsService.getListAll(tenantService.getTenant(SecurityFrameworkUtils.getLoginUserTenantId()).getTradingMarketCode());
        List<TimeSlotsDO> offPeak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("off-peak")).toList();
        List<TimeSlotsDO> valley = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("valley")).toList();
        List<TimeSlotsDO> peak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("peak")).toList();
        BigDecimal peakSum = BigDecimal.ZERO;
        for (TimeSlotsDO timeSlotsDO : peak) {
            Double value = groupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")));
            if (value != null) {
                peakSum = peakSum.add(BigDecimal.valueOf(value));
            }
        }
        BigDecimal valleySum = BigDecimal.ZERO;
        for (TimeSlotsDO timeSlotsDO : valley) {
            Double value = groupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")));
            if (value != null) {
                valleySum = valleySum.add(BigDecimal.valueOf(value));
            }
        }
        BigDecimal offPeakSum = BigDecimal.ZERO;
        for (TimeSlotsDO timeSlotsDO : offPeak) {
            Double value = groupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")));
            if (value != null) {
                offPeakSum = offPeakSum.add(BigDecimal.valueOf(value));
            }
        }

        // 计算除数，并确保不为零
        long peakDivisor = peak.size() * 4L * Math.max(1, filteredData.size() / 96);
        long offPeakDivisor = offPeak.size() * 4L * Math.max(1, filteredData.size() / 96);
        long valleyDivisor = valley.size() * 4L * Math.max(1, filteredData.size() / 96);
        BigDecimal divide = new BigDecimal(0);
        try {
            divide = peakSum.divide(BigDecimal.valueOf(peakDivisor), 2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            divide = BigDecimal.valueOf(0);
        }
        BigDecimal divide1 = new BigDecimal(0);
        try {
            divide1 = offPeakSum.divide(BigDecimal.valueOf(offPeakDivisor), 2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("offPeakSum 除数不能为零");
        }
        BigDecimal divide2 = new BigDecimal(0);
        try {
            divide2 = valleySum.divide(BigDecimal.valueOf(valleyDivisor), 2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("valleySum 除数不能为零");
        }
        sortedMap.put("峰段", divide);
        sortedMap.put("平段", divide1);
        sortedMap.put("谷段", divide2);
        // 自定义比较器
        Comparator<String> customComparator = (key1, key2) -> {
            List<String> order = new ArrayList<>();
            for (int i = 1; i <= 24; i++) {
                order.add(String.valueOf(i));
            }
            order.add("均值");
            order.add("峰段");
            order.add("谷段");
            order.add("平段");

            int index1 = order.indexOf(key1);
            int index2 = order.indexOf(key2);

            return Integer.compare(index1, index2);
        };
        // 使用 TreeMap 进行排序
        Map<String, Object> sortedMap1 = new TreeMap<>(customComparator);
        sortedMap1.putAll(sortedMap);
        Map<String, Object> map = new HashMap<>();
        if (sortedMap1.size() < 24) {
            for (int i = 1; i <= 24; i++) {
                sortedMap1.putIfAbsent(String.valueOf(i), BigDecimal.ZERO);
            }
        }
        map.put("table", sortedMap1);
        map.put("echarts", result);
        map.put("slots", timeSlotsDOList);
        return map;
    }

    /**
     * 日前实时价格对比
     *
     * @param unitId
     * @param monthDate
     * @param date
     * @return
     */
    @Override
    public Map<String, Object> getRealTimePriceCompare(String unitId, String monthDate, Long[] date) {
        List<TradingUnitDO> tradingUnitDOList;
        Arrays.sort(date);
        if (date.length == 0) {
            monthDate = monthDate + "-01 00:15:00";
            Timestamp startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            Timestamp endDate = new Timestamp(calendar.getTimeInMillis());
            tradingUnitDOList = tradingUnitMapper.getRealTimePriceComparisonByMonth(unitId, startDate, endDate);
        } else {
            date[0] = date[0] + 900000;
            Timestamp[] timestamps = new Timestamp[date.length + 1];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            timestamps[date.length] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[date.length - 1] + 60 * 60 * 24 * 1000L)));
            tradingUnitDOList = tradingUnitMapper.getRealTimePriceComparisonByDate(unitId, timestamps[0], timestamps[timestamps.length - 1]);
        }
        //提取出每15分钟的数据
        List<TradingUnitDO> extractList = extractFifteenMinuteData(tradingUnitDOList);
        //筛选  如果按照日查询可能跳着查, 在写sql的时候使用的是最早的时间和最晚的时间范围查, 需要吧中间多查出来的数据删掉
        List<TradingUnitDO> filteredData = new ArrayList<>();
        if (date.length == 0) {
            filteredData = extractList;
        } else {
            for (Long dayTimestamp : date) {
                long dayStart = dayTimestamp;
                long dayEnd = dayStart + 24 * 60 * 60 * 1000 - 100;
                filteredData.addAll(extractList.stream().filter(unit -> unit.getTs().getTime() >= dayStart && unit.getTs().getTime() < dayEnd).toList());
            }
        }
        //filteredData根据月/日分组 每天一个平均值
        Map<String, List<TradingUnitDO>> result = sortMapByDate(groupDataByDate(filteredData));
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        List<String> xName = new ArrayList<>();
        List<Double> hisDay = new ArrayList<>();
        List<Double> realDay = new ArrayList<>();
        for (String key : result.keySet()) {
            //2024年10月01日 转成10/01
            LocalDate date1 = LocalDate.parse(key, inputFormatter);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("MM/dd");
            String formattedDate = date1.format(outputFormatter);
            xName.add(formattedDate);
            BigDecimal hisAvg = BigDecimal.valueOf(result.get(key).stream().mapToDouble(item -> parseNumber(item.getHisSettlementPricesLimit()).doubleValue()).average().orElse(0));
            hisAvg = hisAvg.setScale(2, RoundingMode.HALF_UP);
            BigDecimal realAvg = BigDecimal.valueOf(result.get(key).stream().mapToDouble(item -> parseNumber(item.getRealSettlementPrices()).doubleValue()).average().orElse(0));
            realAvg = realAvg.setScale(2, RoundingMode.HALF_UP);
            hisDay.add(hisAvg.doubleValue());
            realDay.add(realAvg.doubleValue());
        }
        //filteredData获取所有数据0点0的平均值0点15的平均值依次类推到23点45的平均值 一共96个平均值
        List<Double> hisAvg = new ArrayList<>();
        List<Double> realAvg = new ArrayList<>();
        for (int i = 0; i < 96; i++) {
            int count = 0;
            double hisSum = 0;
            double realSum = 0;
            for (TradingUnitDO unit : filteredData) {
                long ts = unit.getTs().getTime();
                int timeSlot = JyDateTimeUtil.getTimeSlot(ts);
                if (timeSlot == i) {
                    hisSum += parseNumber(unit.getHisSettlementPricesLimit()).doubleValue();
                    realSum += parseNumber(unit.getRealSettlementPrices()).doubleValue();
                    count++;
                }
            }
            double average = count > 0 ? hisSum / count : 0;
            double average1 = count > 0 ? realSum / count : 0;
            BigDecimal avg = new BigDecimal(average);
            avg = avg.setScale(2, RoundingMode.HALF_UP);
            BigDecimal avg1 = new BigDecimal(average1);
            avg1 = avg1.setScale(2, RoundingMode.HALF_UP);
            hisAvg.add(avg.doubleValue());
            realAvg.add(avg1.doubleValue());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("xName", xName);
        map.put("hisDay", hisDay);
        map.put("realDay", realDay);
        map.put("hisAvg", hisAvg);
        map.put("realAvg", realAvg);
        return map;
    }

    /**
     * 交易复盘-交易概览
     *
     * @param ids
     * @param date
     * @return
     */
    @Override
    public Map<String, Object> getTransactionOverview(String[] ids, Long[] date) {
        List<List<Object>> dlList = new ArrayList<>();
        List<List<Object>> dfList = new ArrayList<>();
        //中长期电量累计值
        BigDecimal zcqdl = BigDecimal.ZERO;
        //日前电量累计
        BigDecimal rqdl = BigDecimal.ZERO;
        //实时电量累计值
        BigDecimal ssdl = BigDecimal.ZERO;
        //中长期电费累计
        BigDecimal zcqdf = BigDecimal.ZERO;
        //日前电费累计
        BigDecimal rqdf = BigDecimal.ZERO;
        //实时电费累计
        BigDecimal ssdf = BigDecimal.ZERO;
        //度电收益=(中长期电费累计+日前电费累计+实时电费累计)/(中长期电量累计+日前电量累计+实时电量累计)
        BigDecimal ddsy = BigDecimal.ZERO;
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }
        List<TradingUnitDO> tradingUnitDOList = new ArrayList<>();
        for (String id : ids) {
            List<TradingUnitDO> tradingUnitDOS = tradingUnitMapper.getTransactionOverview(id, timestamps[0], timestamps[timestamps.length - 1]);
            tradingUnitDOList.addAll(tradingUnitDOS);
        }
        if (!tradingUnitDOList.isEmpty()) {
            //根据时分分组
            Map<String, List<TradingUnitDO>> groupedMap = tradingUnitDOList.stream()
                    .collect(Collectors.groupingBy(
                            tradingUnitDO -> {
                                LocalDateTime localDateTime = tradingUnitDO.getTs().toLocalDateTime().minusMinutes(15);
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                                return localDateTime.format(formatter);
                            }
                    ));
            //排序 升序
            Map<String, List<TradingUnitDO>> sortedMap = groupedMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey(Comparator.naturalOrder()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
            //在拼接柱状图数据的时候累计下合计值
            //中长期电量累计值
            List<BigDecimal> zcqdllj = new ArrayList<>();
            //日前电量累计值
            List<BigDecimal> rqdllj = new ArrayList<>();
            //实时电量累计值
            List<BigDecimal> ssdllj = new ArrayList<>();
            //中长期电费累计
            List<BigDecimal> zcqdflj = new ArrayList<>();
            //日前电费累计
            List<BigDecimal> rqdflj = new ArrayList<>();
            //实时电费累计
            List<BigDecimal> ssdflj = new ArrayList<>();
            //拼接柱状图数据
            sortedMap.forEach((key, value) -> {
                List<Object> dl = new ArrayList<>();
                List<Object> df = new ArrayList<>();
                //电量
                List<Double> zcqdlzzt = value.stream().map(item -> parseNumber(item.getLongTermContractPower()).doubleValue()).toList();
                List<Double> sbdl = value.stream().map(item -> parseNumber(item.getBilateralPower()).doubleValue()).toList();
                List<Double> wsdl = value.stream().map(item -> parseNumber(item.getDeliveryPower()).doubleValue()).toList();
                List<Double> rgddl = value.stream().map(item -> parseNumber(item.getDayScrollPower()).doubleValue()).toList();
                List<Double> rqdlzzt = value.stream().map(item -> parseNumber(item.getHisPositivePower()).add(parseNumber(item.getHisNegativePower())).doubleValue()).toList();
                List<Double> ssdlzzt = value.stream().map(item -> parseNumber(item.getRealPositivePower()).add(parseNumber(item.getRealNegativePower())).doubleValue()).toList();
                dl.add(key);
                dl.add(zcqdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                zcqdllj.add(zcqdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                dl.add(sbdl.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                dl.add(wsdl.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                dl.add(rgddl.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                dl.add(rqdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                rqdllj.add(rqdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                dl.add(ssdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                ssdllj.add(ssdlzzt.stream().map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add));
                //电费=电量*电价
                df.add(key);
                BigDecimal zcqdf1 = BigDecimal.ZERO;
                BigDecimal sbdf1 = BigDecimal.ZERO;
                BigDecimal wsdf1 = BigDecimal.ZERO;
                BigDecimal rgddf1 = BigDecimal.ZERO;
                BigDecimal rqdf1 = BigDecimal.ZERO;
                BigDecimal ssdf1 = BigDecimal.ZERO;
                for (TradingUnitDO tradingUnitDO : value) {
                    zcqdf1 = zcqdf1.add(BigDecimal.valueOf(tradingUnitDO.getLongPrices()).setScale(2, RoundingMode.HALF_UP));
                    sbdf1 = sbdf1.add(parseNumber(tradingUnitDO.getBilateralPower()).multiply(parseNumber(tradingUnitDO.getBilateralPrice())).setScale(2, RoundingMode.HALF_UP));
                    wsdf1 = wsdf1.add(parseNumber(tradingUnitDO.getDeliveryPower()).multiply(parseNumber(tradingUnitDO.getDeliveryPrice())).setScale(2, RoundingMode.HALF_UP));
                    rgddf1 = rgddf1.add(parseNumber(tradingUnitDO.getDayScrollPower()).multiply(parseNumber(tradingUnitDO.getDayScrollPrice())).setScale(2, RoundingMode.HALF_UP));
                    rqdf1 = rqdf1.add(BigDecimal.valueOf(tradingUnitDO.getHisPrices()).setScale(2, RoundingMode.HALF_UP));
                    ssdf1 = ssdf1.add(BigDecimal.valueOf(tradingUnitDO.getRealPrices()).setScale(2, RoundingMode.HALF_UP));
                    zcqdflj.add(parseNumber(tradingUnitDO.getLongTermContractPower()).multiply(parseNumber(tradingUnitDO.getLongTermContractPrice())).setScale(2, RoundingMode.HALF_UP));
                    rqdflj.add(BigDecimal.valueOf(tradingUnitDO.getHisPrices()).setScale(2, RoundingMode.HALF_UP));
                    ssdflj.add(BigDecimal.valueOf(tradingUnitDO.getRealPrices()).setScale(2, RoundingMode.HALF_UP));
                }
                df.add(zcqdf1);
                df.add(sbdf1);
                df.add(wsdf1);
                df.add(rgddf1);
                df.add(rqdf1);
                df.add(ssdf1);
                dlList.add(dl);
                dfList.add(df);
            });
            zcqdl = zcqdllj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            rqdl = rqdllj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            ssdl = ssdllj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            zcqdf = zcqdflj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            rqdf = rqdflj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            ssdf = ssdflj.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            //(中长期电量累计+日前电量累计+实时电量累计)
            //(中长期电费+实时电费+日前电费)/(中长期电量+日前电量+实时电量)
            BigDecimal value = zcqdl.add(rqdl).add(ssdl);
            if (value.compareTo(BigDecimal.ZERO) != 0) {
                ddsy = (zcqdf.add(rqdf).add(ssdf)).divide((zcqdl.add(rqdl).add(ssdl)), 2, RoundingMode.HALF_UP);
            } else {
                ddsy = BigDecimal.ZERO;
            }
        }
        return Map.of("df", dfList,
                "dl", dlList,
                "zcqdl", zcqdl,
                "rqdl", rqdl,
                "ssdl", ssdl,
                "zcqdf", zcqdf.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP),
                "rqdf", rqdf.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP),
                "ssdf", ssdf.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP),
                "ddsy", ddsy);
    }

    @Override
    public Map<String, Object> getPowerGeneration(String unitId, String monthDate, Long[] date) {
        List<TradingUnitDO> tradingUnitDOList;
        Arrays.sort(date);
        if (date.length == 0) {
            monthDate = monthDate + "-01 00:00:01";
            Timestamp startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            Timestamp endDate = new Timestamp(calendar.getTimeInMillis() + 1000L);
            tradingUnitDOList = tradingUnitMapper.getPowerGenerationByMonth(unitId, startDate, endDate);
        } else {
            Timestamp[] timestamps = new Timestamp[date.length + 1];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i] + 1000L)));
            }
            timestamps[date.length] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[date.length - 1] + 60 * 60 * 24 * 1000L + 1000L)));
            tradingUnitDOList = tradingUnitMapper.getPowerGenerationByDate(unitId, timestamps[0], timestamps[timestamps.length - 1]);
        }
        //提取出每15分钟的数据
        List<TradingUnitDO> extractList = extractFifteenMinuteData(tradingUnitDOList);
        //筛选  如果按照日查询可能跳着查, 在写sql的时候使用的是最早的时间和最晚的时间范围查, 需要吧中间多查出来的数据删掉
        List<TradingUnitDO> filteredData = new ArrayList<>();
        if (date.length == 0) {
            filteredData = extractList;
        } else {
            for (Long dayTimestamp : date) {
                long dayStart = dayTimestamp;
                long dayEnd = dayStart + 24 * 60 * 60 * 1000 - 100;
                filteredData.addAll(extractList.stream().filter(unit -> unit.getTs().getTime() >= dayStart && unit.getTs().getTime() < dayEnd).toList());
            }
        }
        filteredData.forEach(x -> x.setTs(JyDateTimeUtil.minusTimestamp15Minutes(x.getTs())));
        //按月 group分组 组合日数据曲线
        Map<String, List<TradingUnitDO>> tradingUnitDOListMonthMap = filteredData.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStrForDay(x.getTs().getTime())));
        //按时间正序排列
        tradingUnitDOListMonthMap = MapSortUtils.sortByKey(tradingUnitDOListMonthMap, false);
        List<String> dayTimeLine = new ArrayList<>();

        List<Double> daySumScadaPowerValueLine = new ArrayList<>();
        List<Double> daySumTmrPowerValueLine = new ArrayList<>();
        List<Double> daySumSettlementPowerPowerValueLine = new ArrayList<>();

        Map<String, List<String>> dayValueMapLine = new HashMap<>();
        tradingUnitDOListMonthMap.forEach((k, v) -> {
            dayTimeLine.add(k);
            // daySumScadaPowerValueLine.add(v.stream().mapToDouble(x -> "--".equals(x.getScadaPower()) ? 0 : Convert.toFloat(x.getScadaPower())).sum());
            // daySumTmrPowerValueLine.add(v.stream().mapToDouble(x -> "--".equals(x.getTmrPower()) ? 0 : Convert.toFloat(x.getTmrPower())).sum());
            daySumSettlementPowerPowerValueLine.add(v.stream().mapToDouble(x -> "--".equals(x.getSettlementPower()) ? 0 : Convert.toFloat(x.getSettlementPower())).sum());

            //List<Float> dayScadaPowerValueLine = new ArrayList<>();
            //List<Float> dayTmrPowerValueLine = new ArrayList<>();
            List<String> daySettlementPowerPowerValueLine = new ArrayList<>();
            v.forEach(x -> {
                // dayScadaPowerValueLine.add("--".equals(x.getScadaPower()) ? 0 : Convert.toFloat(x.getScadaPower()));
                // dayTmrPowerValueLine.add("--".equals(x.getTmrPower()) ? 0 : Convert.toFloat(x.getTmrPower()));
                daySettlementPowerPowerValueLine.add(x.getSettlementPower());
            });
            //dayValueMapLine.put(k + "_Scada电量", dayScadaPowerValueLine);
            //dayValueMapLine.put(k + "_Tmr电量", dayTmrPowerValueLine);
            dayValueMapLine.put(k + "_结算电量", daySettlementPowerPowerValueLine);
        });
        Map<String, Object> map = new HashMap<>();
        map.put("dayTimeLine", dayTimeLine);
        map.put("daySumScadaPowerValueLine", daySumScadaPowerValueLine);
        map.put("daySumTmrPowerValueLine", daySumTmrPowerValueLine);
        map.put("daySumSettlementPowerPowerValueLine", daySumSettlementPowerPowerValueLine);
        map.put("dayValueMapLine", dayValueMapLine);
        return map;
    }

    /**
     * 交易日历
     *
     * @param unitId
     * @param monthDate
     * @return
     */
    @Override
    public Map<String, Object> getTradingCalendar(String unitId, String monthDate) {

        List<JyTradingAnnouncementDO> jyTradingAnnouncementDOList = tradingCalendarMapper.selectListByDateAndStr("%" + monthDate + "%", "%未开始%");

        Map<String, Object> resultMap = new HashMap<>();
        Map<String, List<Map<String, Object>>> tradingListMap = new HashMap<>();
        List<Map<String, Object>> todayTradingList = new ArrayList<>();
        List<Map<String, Object>> nextDaysTradingList = new ArrayList<>();
        jyTradingAnnouncementDOList.forEach(x -> {
            String dateBegin = x.getDateBegin().toString();
            String dateEnd = x.getDateEnd().toString();

            LocalDate startDate1 = LocalDateTime.parse(dateBegin).toLocalDate();
            LocalDate endDate1 = LocalDateTime.parse(dateEnd).toLocalDate();
            // 按天遍历日期
            for (LocalDate date = startDate1; date.isBefore(endDate1.plusDays(1)); date = date.plusDays(1)) {
                List<Map<String, Object>> tradingList = tradingListMap.computeIfAbsent(date.toString(), k -> new ArrayList<>());
                tradingList = tradingListMap.get(date.toString());
                Map<String, Object> tradingMap = new HashMap<>();
                List<Integer> typeStr = new ArrayList<>();
                if (null != x.getThermalPower() && x.getThermalPower() == 1) {
                    typeStr.add(0);
                }
                if (x.getGreenPower() == 1) {
                    typeStr.add(1);
                }
                if (x.getPowerSales() == 1) {
                    typeStr.add(2);
                }
                tradingMap.put("type", typeStr.toArray());
                tradingMap.put("info", x.getName());
                tradingList.add(tradingMap);


                // 当前日期
                LocalDate today = LocalDate.now();
                if (date.equals(today)) {
                    Map<String, Object> todayMap = new HashMap<>();
                    todayMap.put("info", x.getName());
                    todayTradingList.add(todayMap);
                }

                // 计算两个日期之间的天数差异
                long daysBetween = ChronoUnit.DAYS.between(today, date);
                //过滤出未来7天的数据
                if (daysBetween <= 7 && daysBetween > 0) {
                    Map<String, Object> nextDayMap = new HashMap<>();
                    nextDayMap.put("date", date.toString());
                    nextDayMap.put("info", x.getName());
                    nextDaysTradingList.add(nextDayMap);
                }
            }
        });


        List<Map<String, Object>> resultListMap = new ArrayList<>();
        tradingListMap.forEach((k, v) -> {
            Map<String, Object> strMap = new HashMap<>();
            strMap.put("date", k);
            strMap.put("content", v.toArray());
            resultListMap.add(strMap);


        });
        resultMap.put("calendar", resultListMap);
        resultMap.put("todayTradingList", todayTradingList);
        resultMap.put("nextDaysTradingList", nextDaysTradingList);
        return resultMap;
    }

    /**
     * @param ids
     * @param monthDate
     * @param date
     * @return
     */
    @Override
    public Map<String, Object> getReviewAnalysis(String[] ids, String monthDate, Long[] date) {

        List<TradingUnitDO> tradingUnitDOList = new ArrayList<>();
        Arrays.sort(date);
        Map<String, List<MarketClearingDO>> marketClearingDOListMap = new HashMap<>();
        //所属集团code
        String orgCode = companyUnitService.getCurrentUserOrgCode();

        List<String> filteredList = Arrays.stream(ids)
                .filter(id -> id.contains(orgCode))
                .toList();
        if (date.length == 0) {
            monthDate = monthDate + "-01 00:00:01";
            Timestamp startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            Timestamp endDate = new Timestamp(calendar.getTimeInMillis() + 1000);
            if (calendar.getTime().getTime() > DateUtil.parseDate(DateUtil.today()).getTime()) {
                endDate = new Timestamp(DateUtil.parseDate(DateUtil.today()).offset(DateField.DAY_OF_MONTH, 1).getTime() - 1000);
            }
            for (String id : filteredList) {
                List<TradingUnitDO> tradingUnitDOS = tradingUnitMapper.getReviewAnalysisByMonth(id, startDate, endDate);
                tradingUnitDOList.addAll(tradingUnitDOS);
                List<MarketClearingDO> marketClearingDOList = marketClearingService.getMarketClearingDataList(id, startDate, endDate);
                marketClearingDOList.forEach(x -> x.setTs(JyDateTimeUtil.minusTimestamp15Minutes(x.getTs())));
                marketClearingDOListMap.put(id, marketClearingDOList);
            }

        } else {
            Timestamp[] timestamps = new Timestamp[date.length + 1];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i] + 1000L)));
            }
            timestamps[date.length] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[date.length - 1] + 60 * 60 * 24 * 1000L + 1000L)));
            for (String id : filteredList) {
                List<TradingUnitDO> tradingUnitDOS = tradingUnitMapper.getReviewAnalysisByDay(id, timestamps[0], timestamps[timestamps.length - 1]);
                tradingUnitDOList.addAll(tradingUnitDOS);
                List<MarketClearingDO> marketClearingDOList = marketClearingService.getMarketClearingDataList(id, timestamps[0], timestamps[timestamps.length - 1]);
                marketClearingDOList.forEach(x -> x.setTs(JyDateTimeUtil.minusTimestamp15Minutes(x.getTs())));
                marketClearingDOListMap.put(id, marketClearingDOList);
            }
        }
        //提取出每15分钟的数据
        List<TradingUnitDO> extractList = extractFifteenMinuteData(tradingUnitDOList);
        //筛选  如果按照日查询可能跳着查, 在写sql的时候使用的是最早的时间和最晚的时间范围查, 需要吧中间多查出来的数据删掉
        List<TradingUnitDO> filteredData = new ArrayList<>();
        if (date.length == 0) {
            filteredData = extractList;
        } else {
            for (Long dayTimestamp : date) {
                long dayStart = dayTimestamp + 1000;
                long dayEnd = dayStart + 24 * 60 * 60 * 1000 + 1000;
                filteredData.addAll(extractList.stream().filter(unit -> unit.getTs().getTime() >= dayStart && unit.getTs().getTime() < dayEnd).toList());
            }
        }
        filteredData.forEach(x -> x.setTs(JyDateTimeUtil.minusTimestamp15Minutes(x.getTs())));


        //按月 交易单元分组
        Map<String, List<TradingUnitDO>> tradingUnitDOListMonthMap = filteredData.stream().collect(Collectors.groupingBy(TradingUnitDO::getUnitId));
        //按时间正序排列
        tradingUnitDOListMonthMap = MapSortUtils.sortByKey(tradingUnitDOListMonthMap, false);

        final List<LinkedHashMap<String, String>> tmepMaps = new ArrayList<>();
        for (int i = 1; i < 28; i++) {
            tmepMaps.add(new LinkedHashMap<>());
        }


        tradingUnitDOListMonthMap.forEach((k, v) -> {
            List<MarketClearingDO> finalMarketClearingDOList = marketClearingDOListMap.get(k);
            if (null == finalMarketClearingDOList || finalMarketClearingDOList.isEmpty()) {
                return;
            }
            double hisPositivePowerSum = v.stream().filter(x -> (null != x.getHisPositivePower() && !"--".equals(x.getHisPositivePower()))).mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePower())).sum();
            double hisNegativePowerSum = v.stream().filter(x -> (null != x.getHisNegativePower() && !"--".equals(x.getHisNegativePower()))).mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePower())).sum();
            double realPositivePowerSum = v.stream().filter(x -> (null != x.getRealPositivePower() && !"--".equals(x.getRealPositivePower()))).mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePower())).sum();
            double realNegativePowerSum = v.stream().filter(x -> (null != x.getRealNegativePower() && !"--".equals(x.getRealNegativePower()))).mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePower())).sum();
            double hisPower = hisPositivePowerSum + hisNegativePowerSum;
            double realPower = realPositivePowerSum + realNegativePowerSum;
            //日前加权电价
            tmepMaps.get(14).put("Summary", riQianJiaQuanDianJia2(v, hisPower, finalMarketClearingDOList));
            tmepMaps.get(15).put("Summary", shiShiJiaQuanDianJia2(v, realPower, finalMarketClearingDOList));

            String xianHuoYingKui = xianHuoYingKui2(v, finalMarketClearingDOList);
            tmepMaps.get(16).put("Summary", xianHuoYingKui);
            double temp17Value = hisPower + realPower;
            if (temp17Value == 0) {
                tmepMaps.get(17).put("Summary", "--");
            } else {
                tmepMaps.get(17).put("Summary", String.format("%.2f", (Convert.toDouble(xianHuoYingKui) / temp17Value)));
            }

            if (ids.length == 1) {
                if (date.length == 0) {

                    //日期空
                    Map<String, List<TradingUnitDO>> approveMap = v.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStrForDay(x.getTs().getTime())));

                    Map<String, List<MarketClearingDO>> marketClearingDOMap = finalMarketClearingDOList.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStrForDay(x.getTs().getTime())));

                    //按时间正序排列
                    approveMap = MapSortUtils.sortByKey(approveMap, false);

                    approveMap.forEach((k1, v1) -> {
                        String timeL = DateUtil.parse(k1).getTime() + "";
                        tmepMaps.get(0).put(timeL, String.format("%.2f", v1.stream().mapToDouble(x -> getFloatValueOrZero(x.getCap())).average().orElse(0)));
                        setTempMapValue(timeL, v1, tmepMaps, marketClearingDOMap.get(k1));
                    });
                    List<String> list0 = new ArrayList<>(tmepMaps.get(0).values());
                    tmepMaps.get(0).put("Summary", String.format("%.2f", list0.stream().mapToDouble(Convert::toDouble).average().orElse(0)));

                    setSummary(tmepMaps);

                } else {

                    //日期空
                    Map<String, List<TradingUnitDO>> approveMap = v.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStrForHour(x.getTs().getTime())));
                    Map<String, List<MarketClearingDO>> marketClearingDOMap = finalMarketClearingDOList.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStrForHour(x.getTs().getTime())));

                    //按时间正序排列
                    approveMap = MapSortUtils.sortByKey(approveMap, false);

                    approveMap.forEach((k1, v1) -> {
                        String timeL = Convert.toInt(k1) + 1 + "";
                        tmepMaps.get(0).put(timeL, String.format("%.2f", v1.stream().mapToDouble(x -> getFloatValueOrZero(x.getCap())).average().orElse(0)));
                        setTempMapValue(timeL, v1, tmepMaps, marketClearingDOMap.get(k1));

                    });
                    List<String> list0 = new ArrayList<>(tmepMaps.get(0).values());
                    tmepMaps.get(0).put("Summary", String.format("%.2f", list0.stream().mapToDouble(Convert::toDouble).average().orElse(0)));

                    setSummary(tmepMaps);

                }

            } else {

                String timeL = k.toLowerCase();
                tmepMaps.get(0).put(timeL, String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getCap())).sum()));
                setTempMapValue(timeL, v, tmepMaps, finalMarketClearingDOList);
                List<String> list0 = new ArrayList<>(tmepMaps.get(0).values());
                tmepMaps.get(0).put("Summary", String.format("%.2f", list0.stream().mapToDouble(Convert::toDouble).sum()));

                setSummary(tmepMaps);

            }

        });

        Map<String, Object> map = new HashMap<>();
        map.put("tmepMaps", tmepMaps);
        return map;
    }

    public Float getFloatValueOrZero(String value) {
        return "--".equals(value) ? 0 : Convert.toFloat(value);
    }

    /**
     * 市场动态-日前价格
     *
     * @param ids
     * @param date                 时间
     * @param dataSelectTypeOption 0是日前价格 1是结算价格 2实时价格
     * @return
     */
    @Override
    public List<List<Object>> getCurrentPrices(String[] ids, Long[] date, String dataSelectTypeOption) {
        List<List<Object>> list = new ArrayList<>();
        if (date.length > 0) {
            List<DispatchNodeDO> dispatchNodeDOList = dispatchNodeService.getDispatchNodeList();
            date[1] = date[1] + 86400000L - 1000;
            Timestamp[] timestamps = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            //所属集团code
            String orgCode = companyUnitService.getCurrentUserOrgCode();
            List<String> filteredList = Arrays.stream(ids)
                    .filter(id -> id.contains(orgCode))
                    .toList();
            //当前租户该省份的所有峰谷平时段
            List<TimeSlotsDO> timeSlotsDOList = timeSlotsService.getListAll(tenantService.getTenant(SecurityFrameworkUtils.getLoginUserTenantId()).getTradingMarketCode());
            List<TimeSlotsDO> offPeak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("off-peak")).toList();
            List<TimeSlotsDO> valley = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("valley")).toList();
            List<TimeSlotsDO> peak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("peak")).toList();
            for (String unitId : filteredList) {
                List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getCurrentPrices(unitId, timestamps[0], timestamps[timestamps.length - 1]);
                List<Object> objects = new ArrayList<>();
                List<DispatchNodeDO> collect = dispatchNodeDOList.stream().filter(x -> x.getCode().equals(unitId)).toList();
                List<DeptRespDTO> deptRespDTO = deptApi.getByOrgCode(collect.get(0).getOrgCode());
                objects.add(deptRespDTO.get(0).getName());
                objects.add(collect.get(0).getName());
                BigDecimal peakSum = BigDecimal.ZERO;
                Map<String, Double> groupByHour;
                if ("0".equals(dataSelectTypeOption)) {
                    groupByHour = tradingUnitDOList.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getHisSettlementPricesLimit()).doubleValue())));
                } else if ("1".equals(dataSelectTypeOption)) {
                    groupByHour = tradingUnitDOList.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getHisMarketSettlementPrices()).doubleValue())));
                } else {
                    groupByHour = tradingUnitDOList.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getRealSettlementPrices()).doubleValue())));
                }
                for (TimeSlotsDO timeSlotsDO : peak) {
                    peakSum = peakSum.add(BigDecimal.valueOf(groupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
                }
                BigDecimal valleySum = BigDecimal.ZERO;
                for (TimeSlotsDO timeSlotsDO : valley) {
                    valleySum = valleySum.add(BigDecimal.valueOf(groupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
                }
                BigDecimal offPeakSum = BigDecimal.ZERO;
                for (TimeSlotsDO timeSlotsDO : offPeak) {
                    offPeakSum = offPeakSum.add(BigDecimal.valueOf(groupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
                }
                objects.add(peakSum.divide(BigDecimal.valueOf(peak.size() * 4L * (tradingUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
                objects.add(valleySum.divide(BigDecimal.valueOf(valley.size() * 4L * (tradingUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
                objects.add(offPeakSum.divide(BigDecimal.valueOf(offPeak.size() * 4L * (tradingUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
                if ("0".equals(dataSelectTypeOption)) {
                    objects.addAll(tradingUnitDOList.stream().map(x -> x.getHisSettlementPricesLimit()).toList());
                } else if ("1".equals(dataSelectTypeOption)) {
                    objects.addAll(tradingUnitDOList.stream().map(x -> x.getHisMarketSettlementPrices()).toList());
                } else {
                    objects.addAll(tradingUnitDOList.stream().map(x -> x.getRealSettlementPrices()).toList());
                }
                list.add(objects);
            }
        }
        return list;
    }

    /**
     * 结算发布汇总
     *
     * @param unitId
     * @param monthDate
     * @return
     */
    @Override
    public Map<String, Object> getSettlementRelease(String unitId, String monthDate) {
        monthDate = monthDate + "-01 00:00:01";
        Timestamp startDate = Timestamp.valueOf(monthDate);
        // 使用 Calendar 进行时间操作
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startDate.getTime());
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 获取下个月 1 号的时间戳
        Timestamp endDate = new Timestamp(calendar.getTimeInMillis() + 1000);
        if (calendar.getTime().getTime() > DateUtil.parseDate(DateUtil.today()).getTime()) {
            endDate = new Timestamp(DateUtil.parseDate(DateUtil.today()).offset(DateField.DAY_OF_MONTH, 1).getTime() - 1000);
        }

//his_positive_power,his_negative_power,real_positive_power,real_negative_power,his_positive_prices ,his_negative_prices,real_positive_power,real_negative_power
        List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getSettlementRelease(unitId, startDate, endDate);
        tradingUnitDOList.forEach(x -> x.setTs(JyDateTimeUtil.minusTimestamp15Minutes(x.getTs())));
        //日期空
        Map<String, List<TradingUnitDO>> tradingUnitDOListMap = tradingUnitDOList.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStrForDay(x.getTs().getTime())));
        //按时间正序排列
        tradingUnitDOListMap = MapSortUtils.sortByKey(tradingUnitDOListMap, false);
        List<String> timeLine = new ArrayList<>();
        List<String> hisPositivePrices = new ArrayList<>();
        List<String> hisNegativePrices = new ArrayList<>();
        List<String> realPositivePrices = new ArrayList<>();
        List<String> realNegativePrices = new ArrayList<>();
        List<List<Object>> tablesList = new ArrayList<>();

        List<Object> tablesHuiZong = new ArrayList<>(9);

        tablesHuiZong.add("汇总");
        for (int i = 0; i < 9; i++) {
            tablesHuiZong.add(0);
        }

        Map<String, Object> map = new HashMap<>();
        tablesList.add(tablesHuiZong);
        tradingUnitDOListMap.forEach((k, v) -> {
            timeLine.add(k);
            Double hisPositivePricesSum = v.stream().mapToDouble(item -> parseNumber(item.getHisPositivePrices()).doubleValue()).sum();
            Double hisNegativePricesSum = v.stream().mapToDouble(item -> parseNumber(item.getHisNegativePrices()).doubleValue()).sum();
            Double realPositivePricesSum = v.stream().mapToDouble(item -> parseNumber(item.getRealPositivePrices()).doubleValue()).sum();
            Double realNegativePricesSum = v.stream().mapToDouble(item -> parseNumber(item.getRealNegativePrices()).doubleValue()).sum();
            hisPositivePrices.add(String.format("%.2f", hisPositivePricesSum));
            hisNegativePrices.add(String.format("%.2f", hisNegativePricesSum));
            realPositivePrices.add(String.format("%.2f", realPositivePricesSum));
            realNegativePrices.add(String.format("%.2f", realNegativePricesSum));

            Double hisPositivePowerSum = v.stream().mapToDouble(item -> parseNumber(item.getHisPositivePower()).doubleValue()).sum();
            Double hisNegativePowerSum = v.stream().mapToDouble(item -> parseNumber(item.getHisNegativePower()).doubleValue()).sum();
            Double realPositivePowerSum = v.stream().mapToDouble(item -> parseNumber(item.getRealPositivePower()).doubleValue()).sum();
            Double realNegativePowerSum = v.stream().mapToDouble(item -> parseNumber(item.getRealNegativePower()).doubleValue()).sum();
            List<Object> tables = new ArrayList<>();
            tables.add(k);
            tables.add(String.format("%.2f", hisPositivePowerSum));
            tables.add(String.format("%.2f", hisNegativePowerSum));
            tables.add(String.format("%.2f", hisPositivePricesSum));
            tables.add(String.format("%.2f", hisNegativePricesSum));

            tablesHuiZong.set(1, Convert.toDouble(tablesHuiZong.get(1), 0d) + hisPositivePowerSum);
            tablesHuiZong.set(2, Convert.toDouble(tablesHuiZong.get(2), 0d) + hisNegativePowerSum);
            tablesHuiZong.set(3, Convert.toDouble(tablesHuiZong.get(3), 0d) + hisPositivePricesSum);
            tablesHuiZong.set(4, Convert.toDouble(tablesHuiZong.get(4), 0d) + hisNegativePricesSum);

            tables.add(String.format("%.2f", realPositivePowerSum));
            tables.add(String.format("%.2f", realNegativePowerSum));
            tables.add(String.format("%.2f", realPositivePricesSum));
            tables.add(String.format("%.2f", realNegativePricesSum));
            tablesHuiZong.set(5, Convert.toDouble(tablesHuiZong.get(5), 0d) + realPositivePowerSum);
            tablesHuiZong.set(6, Convert.toDouble(tablesHuiZong.get(6), 0d) + realNegativePowerSum);
            tablesHuiZong.set(7, Convert.toDouble(tablesHuiZong.get(7), 0d) + realPositivePricesSum);
            tablesHuiZong.set(8, Convert.toDouble(tablesHuiZong.get(8), 0d) + realNegativePricesSum);
            tablesList.add(tables);
        });

        tablesHuiZong.set(1, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(1), 0d)));
        tablesHuiZong.set(2, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(2), 0d)));
        tablesHuiZong.set(3, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(3), 0d)));
        tablesHuiZong.set(4, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(4), 0d)));

        tablesHuiZong.set(5, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(5), 0d)));
        tablesHuiZong.set(6, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(6), 0d)));
        tablesHuiZong.set(7, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(7), 0d)));
        tablesHuiZong.set(8, String.format("%.2f", Convert.toDouble(tablesHuiZong.get(8), 0d)));

        map.put("timeLine", timeLine);
        map.put("hisPositivePrices", hisPositivePrices);
        map.put("hisNegativePrices", hisNegativePrices);
        map.put("realPositivePrices", realPositivePrices);
        map.put("realNegativePrices", realNegativePrices);
        map.put("tablesList", tablesList);
        return map;
    }

    @Override
    public Map<String, Object> getSettlementElectricity(String unitId, Long[] date) {

        String dateStr = DateUtil.format(DateUtil.date(new Date(date[0]).getTime() + 1000), "yyyy-MM-dd HH:mm:ss");
        Timestamp startDate = Timestamp.valueOf(dateStr);
        // 使用 Calendar 进行时间操作
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startDate.getTime());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        // 获取下个月 1 号的时间戳
        Timestamp endDate = new Timestamp(calendar.getTimeInMillis() + 1000);

        List<TradingUnitDO> tradingUnitDOS = tradingUnitMapper.getSettlementElectricity(unitId, startDate, endDate);
        tradingUnitDOS.forEach(x -> x.setTs(JyDateTimeUtil.changeTimestamp00To24Hour(x.getTs())));
        List<List<Object>> resultTableList = new ArrayList<>();
        tradingUnitDOS.forEach(x -> {
            List<Object> tempList = new ArrayList<>();
            tempList.add(((x.getTs().toLocalDateTime().getHour() == 0 && x.getTs().toLocalDateTime().getMinute() == 0) ? "24" : x.getTs().toLocalDateTime().getHour()) + ":" + (x.getTs().toLocalDateTime().getMinute() == 0 ? "00" : x.getTs().toLocalDateTime().getMinute()));
            tempList.add(String.format("%.3f", (getFloatValueOrZero(x.getTmrPower()) == 0) ? 0 : ((getFloatValueOrZero(x.getShortTermForecastGen()) - getFloatValueOrZero(x.getTmrPower())) / getFloatValueOrZero(x.getTmrPower()))));
            tempList.add(x.getTmrPowerStr());
            tempList.add(x.getScadaPowerStr());
            tempList.add(x.getRealUsePowerStr());
            tempList.add(x.getCalibrationPowerStr());
            tempList.add(x.getSettlementPowerStr());
            resultTableList.add(tempList);
        });
        Map<String, Object> map = new HashMap<>();
        map.put("resultTableList", resultTableList);
        return map;
    }

    /**
     * 结算发布
     *
     * @param unitId
     * @param monthDate
     * @param date
     * @return
     */
    @Override
    public Map<String, Object> getSettlementReleaseSum(String unitId, String monthDate, Long[] date) {
        List<TradingUnitDO> tradingUnitDOList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        Timestamp startDate;
        Timestamp endDate;
        if (date.length == 0 || date[0] < 0) {
            monthDate = monthDate + "-01 00:00:01";
            startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            endDate = new Timestamp(calendar.getTimeInMillis() + 1000);
        } else {
            String dateStr = DateUtil.format(new Date(date[0]), "yyyy-MM-dd HH:mm:01");
            startDate = Timestamp.valueOf(dateStr);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            endDate = new Timestamp(calendar.getTimeInMillis() + 1000);
        }
        tradingUnitDOList = tradingUnitMapper.getSettlementReleaseSum(unitId, startDate, endDate);
        String companyId = companyUnitService.getCurrentUserOrgCode();
        List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getSettlementPrice(companyId, startDate, endDate);
        //tradingUnitDOList.forEach(x -> x.setTs(JyDateTimeUtil.minusTimestamp15Minutes(x.getTs())));
        Map<String, List<TradingUnitDO>> tradingUnitDOListMap = tradingUnitDOList.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStr3(x.getTs().getTime())));
        Map<String, List<CompanyUnitDO>> companyUnitDOListMap = companyUnitDOList.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStr3(x.getTs().getTime())));
        //按时间正序排列
        tradingUnitDOListMap = MapSortUtils.sortByKey(tradingUnitDOListMap, false);

        List<List<Object>> hisPricesTable = new ArrayList<>();
        List<List<Object>> planTable = new ArrayList<>();

        List<Object> hisPricesTableHuiZong = new ArrayList<>();
        hisPricesTableHuiZong.add("汇总");
        hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePower())).sum()));
        hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePower())).sum()));
        hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePrices())).sum()));
        hisPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePrices())).sum()));
        hisPricesTable.add(hisPricesTableHuiZong);
        List<Object> planTableHuiZong = new ArrayList<>();
        planTableHuiZong.add("汇总");
        planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisGenerateElectricityPlan())).sum()));
        planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermPlan())).sum()));
        planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisProvinceClearance())).sum()));
        planTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisMarketSettlementPrices())).average().orElse(0f)));
        planTable.add(planTableHuiZong);
//=========
        List<List<Object>> realPricesTable = new ArrayList<>();
        List<List<Object>> realPlanTable = new ArrayList<>();
        List<Object> realPricesTableHuiZong = new ArrayList<>();
        realPricesTableHuiZong.add("汇总");
        realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePower())).sum()));
        realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePower())).sum()));
        realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePrices())).sum()));
        realPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePrices())).sum()));
        realPricesTable.add(realPricesTableHuiZong);
        List<Object> realPlanTableHuiZong = new ArrayList<>();
        realPlanTableHuiZong.add("汇总");
        realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisGenerateElectricityPlan())).sum()));
        realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealProvinceClearance())).sum()));
        realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingPlan())).sum()));
        realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingReturn())).sum()));
        realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealSettlementPower())).sum()));
        realPlanTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealMarketSettlementPrices())).average().orElse(0)));
        realPlanTable.add(realPlanTableHuiZong);

        //=========
        List<List<Object>> marketOperationTable = new ArrayList<>();
        List<Object> marketOperationTableHuiZong = new ArrayList<>();
        marketOperationTableHuiZong.add("汇总");
        marketOperationTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getEmergencyInvokeStartUpCompensationPrices())).sum()));
        marketOperationTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getThermalPowerStartUpCompensationPrices())).sum()));
        marketOperationTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getDeviationReviewPrices())).sum()));
        marketOperationTable.add(marketOperationTableHuiZong);


        //=========
        List<List<Object>> issuancePricesTable = new ArrayList<>();
        List<Object> issuancePricesTableHuiZong = new ArrayList<>();
        issuancePricesTableHuiZong.add("汇总");
        issuancePricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices())).sum()));
        issuancePricesTable.add(issuancePricesTableHuiZong);
        //=========
        List<List<Object>> blockagePricesTable = new ArrayList<>();
        List<Object> blockagePricesTableHuiZong = new ArrayList<>();
        blockagePricesTableHuiZong.add("汇总");
        blockagePricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermBlockagePrices())).sum()));
        blockagePricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getBlockingRiskHedgingPrices())).sum()));
        blockagePricesTableHuiZong.add(String.format("%.2f", companyUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisUnifiedSettlementPointPrice())).average().orElse(0)));
        blockagePricesTable.add(blockagePricesTableHuiZong);


        //=========
        List<List<Object>> bcPricesTable = new ArrayList<>();
        List<Object> bbcPricesTableHuiZong = new ArrayList<>();
        bbcPricesTableHuiZong.add("汇总");
        bbcPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getModifiedCompensationPrices())).sum()));
        bbcPricesTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getNecessaryStartUpCompensationPrices())).sum()));
        bcPricesTable.add(bbcPricesTableHuiZong);

        //=========
        List<List<Object>> huiZongTable = new ArrayList<>();
        List<Object> huiZongTableHuiZong = new ArrayList<>();
        huiZongTableHuiZong.add("汇总");
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePower())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePower())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePrices())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePrices())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisGenerateElectricityPlan())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermPlan())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisProvinceClearance())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisMarketSettlementPrices())).average().orElse(0)));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePower())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePower())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePrices())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePrices())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealProvinceClearance())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingPlan())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingReturn())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealSettlementPower())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealMarketSettlementPrices())).average().orElse(0)));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getEmergencyInvokeStartUpCompensationPrices())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getThermalPowerStartUpCompensationPrices())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getDeviationReviewPrices())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisStandbyClearance())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealStandbyClearance())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermBlockagePrices())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getBlockingRiskHedgingPrices())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getModifiedCompensationPrices())).sum()));
        huiZongTableHuiZong.add(String.format("%.2f", tradingUnitDOList.stream().mapToDouble(x -> getFloatValueOrZero(x.getNecessaryStartUpCompensationPrices())).sum()));
        huiZongTable.add(huiZongTableHuiZong);

        tradingUnitDOListMap.forEach((k, v) -> {
            if (k.equals("00:00")) {
                return;
            }
            List<Object> hisTempList = new ArrayList<>();
            hisTempList.add(k);
            hisTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePower())).sum()));
            hisTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePower())).sum()));
            hisTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePrices())).sum()));
            hisTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePrices())).sum()));
            hisPricesTable.add(hisTempList);

            List<Object> hisPlanTempList = new ArrayList<>();
            hisPlanTempList.add(k);
            hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisGenerateElectricityPlan())).sum()));
            hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermPlan())).sum()));
            hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisProvinceClearance())).sum()));
            hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisMarketSettlementPrices())).average().orElse(0)));
            planTable.add(hisPlanTempList);


            List<Object> realTempList = new ArrayList<>();
            realTempList.add(k);
            realTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePower())).sum()));
            realTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePower())).sum()));
            realTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePrices())).sum()));
            realTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePrices())).sum()));
            realPricesTable.add(realTempList);

            List<Object> realPlanTempList = new ArrayList<>();
            realPlanTempList.add(k);
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisGenerateElectricityPlan())).sum()));
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealProvinceClearance())).sum()));
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingPlan())).sum()));
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingReturn())).average().orElse(0)));
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealSettlementPower())).sum()));
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealMarketSettlementPrices())).average().orElse(0)));
            realPlanTable.add(realPlanTempList);


            List<Object> marketOperationTableTempList = new ArrayList<>();
            marketOperationTableTempList.add(k);
            marketOperationTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getEmergencyInvokeStartUpCompensationPrices())).sum()));
            marketOperationTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getThermalPowerStartUpCompensationPrices())).sum()));
            marketOperationTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getDeviationReviewPrices())).sum()));
            marketOperationTable.add(marketOperationTableTempList);


            //=========
            List<Object> issuancePricesTableTempList = new ArrayList<>();
            issuancePricesTableTempList.add(k);
            issuancePricesTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices())).sum()));
            issuancePricesTable.add(issuancePricesTableTempList);


            //=========
            List<Object> blockagePricesTableTempList = new ArrayList<>();
            blockagePricesTableTempList.add(k);
            blockagePricesTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermBlockagePrices())).sum()));
            blockagePricesTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getBlockingRiskHedgingPrices())).sum()));
            blockagePricesTableTempList.add(String.format("%.2f", companyUnitDOListMap.get(k).stream().mapToDouble(x -> getFloatValueOrZero(x.getHisUnifiedSettlementPointPrice())).sum()));
            blockagePricesTable.add(blockagePricesTableTempList);


            //=========
            List<Object> bbcPricesTableTableTempList = new ArrayList<>();
            bbcPricesTableTableTempList.add(k);
            bbcPricesTableTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getModifiedCompensationPrices())).sum()));
            bbcPricesTableTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getNecessaryStartUpCompensationPrices())).sum()));
            bcPricesTable.add(bbcPricesTableTableTempList);


            //=================
            List<Object> huiZongTableHuiZongTempList = new ArrayList<>();
            huiZongTableHuiZongTempList.add(k);
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePower())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePower())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisGenerateElectricityPlan())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermPlan())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisProvinceClearance())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisMarketSettlementPrices())).average().orElse(0)));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePower())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePower())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealProvinceClearance())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingPlan())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingReturn())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealSettlementPower())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealMarketSettlementPrices())).average().orElse(0)));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getEmergencyInvokeStartUpCompensationPrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getThermalPowerStartUpCompensationPrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getDeviationReviewPrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisStandbyClearance())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealStandbyClearance())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermBlockagePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getBlockingRiskHedgingPrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getModifiedCompensationPrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getNecessaryStartUpCompensationPrices())).sum()));
            huiZongTable.add(huiZongTableHuiZongTempList);
        });


        List<TradingUnitDO> v = tradingUnitDOListMap.get("00:00");
        if (null != v) {
            String k = "24:00";
            List<Object> hisTempList = new ArrayList<>();
            hisTempList.add(k);
            hisTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePower())).sum()));
            hisTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePower())).sum()));
            hisTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePrices())).sum()));
            hisTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePrices())).sum()));
            hisPricesTable.add(hisTempList);

            List<Object> hisPlanTempList = new ArrayList<>();
            hisPlanTempList.add(k);
            hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisGenerateElectricityPlan())).sum()));
            hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermPlan())).sum()));
            hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisProvinceClearance())).sum()));
            hisPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisMarketSettlementPrices())).average().orElse(0)));
            planTable.add(hisPlanTempList);


            List<Object> realTempList = new ArrayList<>();
            realTempList.add(k);
            realTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePower())).sum()));
            realTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePower())).sum()));
            realTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePrices())).sum()));
            realTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePrices())).sum()));
            realPricesTable.add(realTempList);

            List<Object> realPlanTempList = new ArrayList<>();
            realPlanTempList.add(k);
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisGenerateElectricityPlan())).sum()));
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealProvinceClearance())).average().orElse(0)));
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingPlan())).sum()));
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingReturn())).average().orElse(0)));
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealSettlementPower())).sum()));
            realPlanTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealMarketSettlementPrices())).average().orElse(0)));
            realPlanTable.add(realPlanTempList);


            List<Object> marketOperationTableTempList = new ArrayList<>();
            marketOperationTableTempList.add(k);
            marketOperationTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getEmergencyInvokeStartUpCompensationPrices())).sum()));
            marketOperationTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getThermalPowerStartUpCompensationPrices())).sum()));
            marketOperationTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getDeviationReviewPrices())).sum()));
            marketOperationTable.add(marketOperationTableTempList);


            //=========
            List<Object> issuancePricesTableTempList = new ArrayList<>();
            issuancePricesTableTempList.add(k);
            issuancePricesTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices())).sum()));
            issuancePricesTable.add(issuancePricesTableTempList);


            //=========
            List<Object> blockagePricesTableTempList = new ArrayList<>();
            blockagePricesTableTempList.add(k);
            blockagePricesTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermBlockagePrices())).sum()));
            blockagePricesTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getBlockingRiskHedgingPrices())).sum()));
            blockagePricesTableTempList.add(String.format("%.2f", companyUnitDOListMap.get("00:00").stream().mapToDouble(x -> getFloatValueOrZero(x.getHisUnifiedSettlementPointPrice())).sum()));
            blockagePricesTable.add(blockagePricesTableTempList);


            //=========
            List<Object> bbcPricesTableTableTempList = new ArrayList<>();
            bbcPricesTableTableTempList.add(k);
            bbcPricesTableTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getModifiedCompensationPrices())).sum()));
            bbcPricesTableTableTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getNecessaryStartUpCompensationPrices())).sum()));
            bcPricesTable.add(bbcPricesTableTableTempList);


            //=================
            List<Object> huiZongTableHuiZongTempList = new ArrayList<>();
            huiZongTableHuiZongTempList.add(k);
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePower())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePower())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisGenerateElectricityPlan())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermPlan())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisProvinceClearance())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisMarketSettlementPrices())).average().orElse(0)));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePower())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePower())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealProvinceClearance())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingPlan())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getCrossProvincialPeakLoadBalancingReturn())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealSettlementPower())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealMarketSettlementPrices())).average().orElse(0)));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getEmergencyInvokeStartUpCompensationPrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getThermalPowerStartUpCompensationPrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getDeviationReviewPrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getHisStandbyClearance())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getRealStandbyClearance())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermBlockagePrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getBlockingRiskHedgingPrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getModifiedCompensationPrices())).sum()));
            huiZongTableHuiZongTempList.add(String.format("%.2f", v.stream().mapToDouble(x -> getFloatValueOrZero(x.getNecessaryStartUpCompensationPrices())).sum()));
            huiZongTable.add(huiZongTableHuiZongTempList);
        }

        map.put("hisPricesTable", hisPricesTable);
        map.put("planTable", planTable);
        map.put("realPricesTable", realPricesTable);
        map.put("realPlanTable", realPlanTable);
        map.put("marketOperationTable", marketOperationTable);
        map.put("issuancePricesTable", issuancePricesTable);
        map.put("blockagePricesTable", blockagePricesTable);
        map.put("bcPricesTable", bcPricesTable);
        map.put("huiZongTable", huiZongTable);
        return map;
    }

    /**
     * 市场动态 市场出清监视
     *
     * @param unitId
     * @param date
     * @return
     */
    @Override
    public Map<String, Object> getClearanceMonitor(String unitId, Long[] date) {
        Map<String, Object> map = new HashMap<>();
        if (date.length > 0) {
            date[1] = date[1] + 86400000L - 1000;
            Timestamp[] timestamps = new Timestamp[date.length];
            //日统计查询。    0点开始不是0点15 得减去15分钟
            Timestamp[] timestamps1 = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            for (int i = 0; i < date.length; i++) {
                timestamps1[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i] - 900000)));
            }
            List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getClearanceMonitor(unitId, timestamps[0], timestamps[timestamps.length - 1]);
            List<TradingUnitDailyDO> tradingUnitDailyDOList = tradingUnitDailyMapper.getClearanceMonitor(unitId, timestamps1[0], timestamps1[timestamps1.length - 1]);
            //中长期结算曲线
            List<String> mediumLongTermSettlementCurves = tradingUnitDOList.stream().map(TradingUnitDO::getMediumLongTermSettlementCurves).toList();
            map.put("mediumLongTermSettlementCurves", mediumLongTermSettlementCurves);
            //中长期结算曲线表格数据
            List<String> mediumLongTermSettlementCurvesDaily = tradingUnitDailyDOList.stream().map(TradingUnitDailyDO::getMediumLongTermSettlementCurves).toList();
            map.put("mediumLongTermSettlementCurvesDaily", mediumLongTermSettlementCurvesDaily);
            //自计划
            List<String> selfPlan = tradingUnitDOList.stream().map(TradingUnitDO::getSelfPlan).toList();
            map.put("selfPlan", selfPlan);
            //自计划
            List<String> selfPlanDaily = tradingUnitDailyDOList.stream().map(TradingUnitDailyDO::getSelfPlan).toList();
            map.put("selfPlanDaily", selfPlanDaily);
            //预平衡计划
            List<String> prebalancePlan = tradingUnitDOList.stream().map(TradingUnitDO::getPrebalancePlan).toList();
            map.put("prebalancePlan", prebalancePlan);
            //预平衡计划表格
            List<String> prebalancePlanDaily = tradingUnitDailyDOList.stream().map(TradingUnitDailyDO::getPrebalancePlan).toList();
            map.put("prebalancePlanDaily", prebalancePlanDaily);
            //可靠性计划
            List<String> reliabilityPlan = tradingUnitDOList.stream().map(TradingUnitDO::getReliabilityPlan).toList();
            map.put("reliabilityPlan", reliabilityPlan);
            //可靠性计划表格数据
            List<String> reliabilityPlanDaily = tradingUnitDailyDOList.stream().map(TradingUnitDailyDO::getReliabilityPlan).toList();
            map.put("reliabilityPlanDaily", reliabilityPlanDaily);
            //双边出清
            List<String> bilateralClearance = tradingUnitDOList.stream().map(TradingUnitDO::getBilateralClearance).toList();
            map.put("bilateralClearance", bilateralClearance);
            //双边出清表格数据
            List<String> bilateralClearanceDaily = tradingUnitDailyDOList.stream().map(TradingUnitDailyDO::getBilateralClearance).toList();
            map.put("bilateralClearanceDaily", bilateralClearanceDaily);
            //实时计划
            List<String> realPlan = tradingUnitDOList.stream().map(TradingUnitDO::getRealPlan).toList();
            map.put("realPlan", realPlan);
            //实时计划表格数据
            List<String> realPlanDaily = tradingUnitDailyDOList.stream().map(TradingUnitDailyDO::getRealPlan).toList();
            map.put("realPlanDaily", realPlanDaily);
            //实时上网scada电力
            List<String> realScadaPower = tradingUnitDOList.stream().map(TradingUnitDO::getRealScadaPower).toList();
            map.put("realScadaPower", realScadaPower);
            //实时上网scada电力表格数据
            List<String> realScadaPowerDaily = tradingUnitDailyDOList.stream().map(TradingUnitDailyDO::getRealScadaPower).toList();
            map.put("realScadaPowerDaily", realScadaPowerDaily);
            //短期预测
//            List<String> shortTermForecast = tradingUnitDOList.stream().map(item -> Objects.equals(item.getShortTermForecast(), "--") ? "--" : new BigDecimal(item.getShortTermForecast()).divide(BigDecimal.valueOf(4), 4, RoundingMode.HALF_UP).toString()).toList();
            List<String> shortTermForecast = tradingUnitDOList.stream().map(TradingUnitDO::getShortTermForecast).toList();
            map.put("shortTermForecast", shortTermForecast);
            //短期预测表格数据
            List<String> shortTermForecastDaily = tradingUnitDailyDOList.stream().map(TradingUnitDailyDO::getShortTermForecast).toList();
            map.put("shortTermForecastDaily", shortTermForecastDaily);
            //超短期预测
            List<String> ultraShortTermForecast = tradingUnitDOList.stream().map(TradingUnitDO::getUltraShortTermForecast).toList();
            map.put("ultraShortTermForecast", ultraShortTermForecast);
            //超短期预测表格数据
            List<String> ultraShortTermForecastDaily = tradingUnitDailyDOList.stream().map(TradingUnitDailyDO::getUltraShortTermForecast).toList();
            map.put("ultraShortTermForecastDaily", ultraShortTermForecastDaily);
            //跨省区现货出清电力
            List<String> crossProvincialSpotClearingOfElectricity = tradingUnitDOList.stream().map(TradingUnitDO::getCrossProvincialSpotClearingOfElectricity).toList();
            map.put("crossProvincialSpotClearingOfElectricity", crossProvincialSpotClearingOfElectricity);
            //跨省区现货出清电力表格数据
            List<String> crossProvincialSpotClearingOfElectricityDaily = tradingUnitDailyDOList.stream().map(TradingUnitDailyDO::getCrossProvincialSpotClearingOfElectricity).toList();
            map.put("crossProvincialSpotClearingOfElectricityDaily", crossProvincialSpotClearingOfElectricityDaily);
            //实时出清依据
            List<String> realBasisClearance = tradingUnitDOList.stream().map(TradingUnitDO::getRealBasisClearance).toList();
            map.put("realBasisClearance", realBasisClearance);
            //实时出清依据表格数据
            List<String> realBasisClearanceDaily = tradingUnitDailyDOList.stream().map(TradingUnitDailyDO::getRealBasisClearance).toList();
            map.put("realBasisClearanceDaily", realBasisClearanceDaily);
        }
        return map;
    }

    /**
     * 剩余空间
     *
     * @param unitId id
     * @param date   时间
     * @return
     */
    @Override
    public Map<String, List<Double>> getRemainingSpace(String unitId, Long[] date) {
        Map<String, List<Double>> map = new HashMap<>();
        //剩余空间
        List<Double> values = new ArrayList<>();
        //短期预测
        List<Double> shortTermForecast = new ArrayList<>();
        //中长期结算曲线
        List<Double> mediumLongTermSettlementCurves = new ArrayList<>();
        if (date.length > 0) {
            date[1] = date[1] + 86400000L - 1000;
            Timestamp[] timestamps = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getClearanceMonitor(unitId, timestamps[0], timestamps[timestamps.length - 1]);
            //剩余空间 = 短期预测 - 中长期结算曲线
            values = tradingUnitDOList.stream().map(tradingUnitDO -> parseNumber(tradingUnitDO.getShortTermForecast()).subtract(parseNumber(tradingUnitDO.getMediumLongTermSettlementCurves())))
                    .map(item -> item.setScale(2, RoundingMode.HALF_UP).doubleValue()).toList();
            shortTermForecast = tradingUnitDOList.stream().map(tradingUnitDO -> parseNumber(tradingUnitDO.getShortTermForecast()).doubleValue()).toList();
            mediumLongTermSettlementCurves = tradingUnitDOList.stream().map(tradingUnitDO -> parseNumber(tradingUnitDO.getMediumLongTermSettlementCurves()).doubleValue()).toList();
        }
        map.put("values", values);
        map.put("shortTermForecast", shortTermForecast);
        map.put("mediumLongTermSettlementCurves", mediumLongTermSettlementCurves);
        return map;
    }

    @Override
    public void exportRemainingSpace(HttpServletResponse response, HttpServletRequest request, Long[] date) {
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("excel/D+3统计表.xlsx");
        ServletOutputStream out = null;
        XSSFWorkbook xwb = null;
        List<DispatchNodeDO> dispatchNodeDOS = companyUnitService.getDispatchNodeList();
        String companyId = companyUnitService.getCurrentUserOrgCode();
        if (!dispatchNodeDOS.isEmpty()) {
            try {
                xwb = new XSSFWorkbook(inputStream);
                XSSFSheet sheet = xwb.getSheetAt(0);

                date[1] = date[1] + 86400000L * 7 - 1000;
                Timestamp[] timestamps = new Timestamp[date.length];
                for (int i = 0; i < date.length; i++) {
                    timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
                }
                Map<String, List<TradingUnitDO>> map = new LinkedHashMap<>();
                dispatchNodeDOS.sort(Comparator.comparing(DispatchNodeDO::getSort));
                for (DispatchNodeDO dispatchNodeDO : dispatchNodeDOS) {
                    List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getClearanceMonitor(dispatchNodeDO.getCode(), timestamps[0], Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[0] + 86400000L))));
                    map.put(dispatchNodeDO.getName(), tradingUnitDOList);
                }
                List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getDynamicField5(companyId, "load_forecast", timestamps[0], timestamps[timestamps.length - 1]);
                Map<String, List<CompanyUnitDO>> companyUnitDOMap = companyUnitDOList.stream().collect(Collectors.groupingBy(x -> JyDateTimeUtil.getFormatDateStrForDay(x.getTs().getTime())));
                //D是16号的话 D+3就是16号 D+4就是17号!!!
                String d3 = JyDateTimeUtil.getFormatDateStrForDay(date[0]);
                String d4 = JyDateTimeUtil.getFormatDateStrForDay(date[0] + 86400000L);
                String d5 = JyDateTimeUtil.getFormatDateStrForDay(date[0] + 86400000L * 2);
                String d6 = JyDateTimeUtil.getFormatDateStrForDay(date[0] + 86400000L * 3);
                List<CompanyUnitDO> companyUnitD3 = companyUnitDOMap.get(d3);
                List<CompanyUnitDO> companyUnitD4 = companyUnitDOMap.get(d4);
                List<CompanyUnitDO> companyUnitD5 = companyUnitDOMap.get(d5);
                List<CompanyUnitDO> companyUnitD6 = companyUnitDOMap.get(d6);
                String powerD3;
                if (companyUnitD3 == null || companyUnitD3.isEmpty()) {
                    powerD3 = "0"; // 处理空集合或null的情况
                } else {
                    // 获取最大值后四舍五入转为整数
                    long roundedValue = Math.round(
                            companyUnitD3.stream()
                                    .mapToDouble(x -> getFloatValueOrZero(x.getLoadForecast()))
                                    .max()
                                    .orElse(0.0) // 确保流为空时返回0
                    );
                    powerD3 = String.valueOf(roundedValue); // 直接转为字符串整数
                }
                String powerD4;
                if (companyUnitD4 == null || companyUnitD4.isEmpty()) {
                    powerD4 = "0"; // 处理空集合或null的情况
                } else {
                    // 获取最大值后四舍五入转为整数
                    long roundedValue = Math.round(
                            companyUnitD4.stream()
                                    .mapToDouble(x -> getFloatValueOrZero(x.getLoadForecast()))
                                    .max()
                                    .orElse(0.0) // 确保流为空时返回0
                    );
                    powerD4 = String.valueOf(roundedValue); // 直接转为字符串整数
                }
                String powerD5;
                if (companyUnitD5 == null || companyUnitD5.isEmpty()) {
                    powerD5 = "0"; // 处理空集合或null的情况
                } else {
                    // 获取最大值后四舍五入转为整数
                    long roundedValue = Math.round(
                            companyUnitD5.stream()
                                    .mapToDouble(x -> getFloatValueOrZero(x.getLoadForecast()))
                                    .max()
                                    .orElse(0.0) // 确保流为空时返回0
                    );
                    powerD5 = String.valueOf(roundedValue); // 直接转为字符串整数
                }
                String powerD6;
                if (companyUnitD6 == null || companyUnitD6.isEmpty()) {
                    powerD6 = "0"; // 处理空集合或null的情况
                } else {
                    // 获取最大值后四舍五入转为整数
                    long roundedValue = Math.round(
                            companyUnitD6.stream()
                                    .mapToDouble(x -> getFloatValueOrZero(x.getLoadForecast()))
                                    .max()
                                    .orElse(0.0) // 确保流为空时返回0
                    );
                    powerD6 = String.valueOf(roundedValue); // 直接转为字符串整数
                }
                sheet.setForceFormulaRecalculation(true);
                //插入第一行的 最高负荷预测值和第二行的表头
                set1Row1Cell(sheet, powerD3, powerD4, powerD5, powerD6, xwb, date);
                //开始拼装表格数据
                List<List<String>> dataToInsert = new ArrayList<>();
                map.forEach((key, value) -> {
                    List<String> rowData = new ArrayList<>();
                    rowData.add(key);
                    processData(value, rowData);
                    dataToInsert.add(rowData);
                });
                int insertRowIndex = 3; // 要在第四行（索引为3）上方插入数据
                // 创建水平居中和垂直居中的样式
                CellStyle centerStyle = xwb.createCellStyle();
                centerStyle.setAlignment(HorizontalAlignment.CENTER);
                centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                for (int i = 0; i < dataToInsert.size(); i++) {
                    List<String> rowData = dataToInsert.get(i);
                    Row row = sheet.createRow(insertRowIndex++);
                    row.setHeightInPoints(25);
                    for (int j = 0; j < rowData.size(); j++) {
                        Cell cell = row.createCell(j);
                        if (j > 0 && j < rowData.size() - 1) { // 排除第一个和最后一个元素
                            try {
                                //所有数值只显示整数（小数进位方式：武威公司（武威123，武威4，武威5，武威特变）小数部分丢弃，其余交易单元四舍五入
                                if (rowData.get(0).contains("武威")) {
                                    cell.setCellValue(Math.floor(Double.parseDouble(rowData.get(j))));
                                } else {
                                    long roundedValue = Math.round(Double.parseDouble(rowData.get(j)));
                                    cell.setCellValue(roundedValue);
                                }
                            } catch (NumberFormatException e) {
                                // 如果无法转换为数字，仍然按字符串处理
                                cell.setCellValue(rowData.get(j));
                            }
                        } else {
                            cell.setCellValue(rowData.get(j));
                        }
                        // 应用居中样式
                        CellStyle newStyle = xwb.createCellStyle();
                        newStyle.cloneStyleFrom(cell.getCellStyle());
                        newStyle.setAlignment(centerStyle.getAlignment());
                        newStyle.setVerticalAlignment(centerStyle.getVerticalAlignment());
                        cell.setCellStyle(newStyle);
                    }
                    // 每第二条数据填充灰色背景色
                    if (i % 2 == 1) {
                        CellStyle style = xwb.createCellStyle();
                        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                        for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
                            Cell cell = row.getCell(j);
                            if (cell != null) {
                                CellStyle newStyle = xwb.createCellStyle();
                                newStyle.cloneStyleFrom(cell.getCellStyle());
                                newStyle.setFillForegroundColor(style.getFillForegroundColor());
                                newStyle.setFillPattern(style.getFillPattern());
                                cell.setCellStyle(newStyle);
                            }
                        }
                    }
                }
                // 创建带边框的样式
                CellStyle borderStyle = xwb.createCellStyle();
                borderStyle.setBorderTop(BorderStyle.THIN);
                borderStyle.setBorderBottom(BorderStyle.THIN);
                borderStyle.setBorderLeft(BorderStyle.THIN);
                borderStyle.setBorderRight(BorderStyle.THIN);
                borderStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
                borderStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
                borderStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
                borderStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

                // 从第一行开始应用边框样式到所有单元格，同时保留原有样式
                for (int i = 0; i < sheet.getLastRowNum() + 1; i++) {
                    Row row = sheet.getRow(i);
                    if (row != null) {
                        for (int j = 0; j < row.getLastCellNum(); j++) {
                            Cell cell = row.getCell(j);
                            if (cell != null) {
                                CellStyle newStyle = xwb.createCellStyle();
                                newStyle.cloneStyleFrom(cell.getCellStyle());
                                newStyle.setBorderTop(borderStyle.getBorderTop());
                                newStyle.setBorderBottom(borderStyle.getBorderBottom());
                                newStyle.setBorderLeft(borderStyle.getBorderLeft());
                                newStyle.setBorderRight(borderStyle.getBorderRight());
                                newStyle.setTopBorderColor(borderStyle.getTopBorderColor());
                                newStyle.setBottomBorderColor(borderStyle.getBottomBorderColor());
                                newStyle.setLeftBorderColor(borderStyle.getLeftBorderColor());
                                newStyle.setRightBorderColor(borderStyle.getRightBorderColor());
                                cell.setCellStyle(newStyle);
                            }
                        }
                    }
                }
                // 合并单元格（从A到H，占四行）
                int lastRowNum = sheet.getLastRowNum();
                int startRow = lastRowNum + 1;
                int endRow = startRow + 3;
                CellRangeAddress cellRangeAddress = new CellRangeAddress(startRow, endRow, 0, 13);
                sheet.addMergedRegion(cellRangeAddress);

                // 创建合并单元格中的内容
                Row mergeRow = sheet.createRow(startRow);
                Cell mergeCell = mergeRow.createCell(0);
                mergeCell.setCellValue("");
                // 为合并单元格设置边框
                for (int i = startRow; i <= endRow; i++) {
                    Row row1 = sheet.getRow(i);
                    if (row1 == null) {
                        row1 = sheet.createRow(i);
                    }
                    for (int j = 0; j <= 13; j++) {
                        Cell cell1 = row1.getCell(j);
                        if (cell1 == null) {
                            cell1 = row1.createCell(j);
                        }
                        CellStyle newStyle = xwb.createCellStyle();
                        newStyle.cloneStyleFrom(cell1.getCellStyle());
                        newStyle.setBorderTop(borderStyle.getBorderTop());
                        newStyle.setBorderBottom(borderStyle.getBorderBottom());
                        newStyle.setBorderLeft(borderStyle.getBorderLeft());
                        newStyle.setBorderRight(borderStyle.getBorderRight());
                        newStyle.setTopBorderColor(borderStyle.getTopBorderColor());
                        newStyle.setBottomBorderColor(borderStyle.getBottomBorderColor());
                        newStyle.setLeftBorderColor(borderStyle.getLeftBorderColor());
                        newStyle.setRightBorderColor(borderStyle.getRightBorderColor());
                        cell1.setCellStyle(newStyle);
                    }
                }
                // 将第一列列宽设置为13
                sheet.setColumnWidth(0, 13 * 256);
                String name = "D+3统计表.xlsx";
                String fileName = URLEncoder.encode(name, StandardCharsets.UTF_8);
                response.setContentType("application/msexcel");
                //response.setContentType("multipart/form-data");
                response.addHeader("Content-Type", "application/octet-stream;charset=utf-8");
                response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
                out = response.getOutputStream();
                xwb.write(out);
                out.flush();
            } catch (Exception ex) {
                // 记录异常信息
                ex.printStackTrace();
                IoUtil.close(out);
            } finally {
                IoUtil.close(inputStream);
                if (xwb != null) {
                    try {
                        xwb.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (out != null) {
                    try {
                        out.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    private void processData(List<TradingUnitDO> value, List<String> rowData) {
        for (String timePoint : TIME_POINTS) {
            if (value.stream().filter(item -> JyDateTimeUtil.getFormatDateStr3(item.getTs().getTime()).equals(timePoint)).toList().isEmpty()) {
                rowData.add("0.00");
            } else {
                BigDecimal space = parseNumber(value.stream().filter(item -> JyDateTimeUtil.getFormatDateStr3(item.getTs().getTime()).equals(timePoint)).toList().get(0).getShortTermForecast())
                        .subtract(parseNumber(value.stream().filter(item -> JyDateTimeUtil.getFormatDateStr3(item.getTs().getTime()).equals(timePoint)).toList().get(0).getMediumLongTermSettlementCurves())).setScale(2, RoundingMode.HALF_UP);
                rowData.add(space.toString());
            }
        }
        //备注单元格
        rowData.add(null);
        //7-8点 7点15数据

        //8-9点 8点15数据

        //9-10点 9点15数据

        //10-11点 10点15数据

        //11-12点 11点15数据

        //12-13点 13点00数据

        //13-14点 53点00数据

        //14-15点 15点00数据

        //15-16点 16点00数据

        //16-17点 17点00数据

        //17-18点 18点00数据

        //18-19点 19点00数据

    }

    private void set1Row1Cell(XSSFSheet sheet, String powerD3, String powerD4, String powerD5, String powerD6, XSSFWorkbook xwb, Long[] date) {
        // 确保第一行存在
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            firstRow = sheet.createRow(0);
        }
        firstRow.setHeightInPoints(25);
        // 获取 A1 单元格
        Cell cell = firstRow.getCell(0);
        if (cell == null) {
            cell = firstRow.createCell(0);
        }
        String[] texts = {"D+3日最高负荷预测", "D+4日最高负荷预测", "D+5日最高负荷预测", "D+6日最高负荷预测"};
        String[] values = {powerD3, powerD4, powerD5, powerD6};

        StringBuilder combinedText = new StringBuilder();
        for (int i = 0; i < texts.length; i++) {
            combinedText.append(texts[i]).append("  ").append(values[i]).append("  ");
        }
        String fullText = combinedText.toString();

        // 创建富文本字符串
        RichTextString richText = xwb.getCreationHelper().createRichTextString(fullText);

        // 创建普通字体
        Font normalFont = xwb.createFont();
        normalFont.setFontName("宋体");
        normalFont.setFontHeightInPoints((short) 12);

        // 创建带下划线的字体
        Font underlineFont = xwb.createFont();
        underlineFont.setFontName("宋体");
        underlineFont.setFontHeightInPoints((short) 12);
        underlineFont.setUnderline(Font.U_SINGLE);

        int currentIndex = 0;
        for (int i = 0; i < texts.length; i++) {
            // 应用普通字体到文本部分
            int textLength = texts[i].length();
            richText.applyFont(currentIndex, currentIndex + textLength, normalFont);
            currentIndex += textLength;

            // 应用下划线字体到空格和数字部分
            int spaceBeforeLength = 2; // "  " 的长度
            richText.applyFont(currentIndex, currentIndex + spaceBeforeLength, underlineFont);
            currentIndex += spaceBeforeLength;

            String valueStr = String.valueOf(values[i]);
            int valueLength = valueStr.length();
            richText.applyFont(currentIndex, currentIndex + valueLength, underlineFont);
            currentIndex += valueLength;

            int spaceAfterLength = 2; // "  " 的长度
            richText.applyFont(currentIndex, currentIndex + spaceAfterLength, underlineFont);
            currentIndex += spaceAfterLength;
        }

        // 将富文本字符串设置到单元格中
        cell.setCellValue(richText);
        CellStyle style1 = xwb.createCellStyle();
        style1.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        style1.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        cell.setCellStyle(style1);


        // 第二行第二列插入数据并设置样式
        Row secondRow = sheet.createRow(1);
        secondRow.setHeightInPoints(25);
        Cell secondCell = secondRow.createCell(0);

        // 创建带边框的样式（在创建单元格时直接应用）
        CellStyle borderStyle = xwb.createCellStyle();
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setBorderRight(BorderStyle.THIN);
        borderStyle.setAlignment(HorizontalAlignment.CENTER);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 应用边框样式到第二行所有单元格
        for (int j = 0; j <= 13; j++) { // 假设需要覆盖到第14列（索引0~13）
            Cell cells = secondRow.getCell(j);
            if (cells == null) {
                cells = secondRow.createCell(j); // 确保所有单元格存在
            }
            cells.setCellStyle(borderStyle); // 直接设置样式
        }
        if (secondCell != null) {
            // 将时间戳转换为 Date 对象
            Date date1 = new Date(date[0]);

            // 使用 Hutool 的 DateUtil 格式化日期
            String formattedDate = DateUtil.format(date1, "yyyy年MM月dd日");
            String data = formattedDate + "日滚动交易策略";
            RichTextString richText2 = xwb.getCreationHelper().createRichTextString(data);

            // 创建加粗字体
            Font boldFont = xwb.createFont();
            boldFont.setFontName("宋体");
            boldFont.setFontHeightInPoints((short) 16);
            boldFont.setBold(true);

            // 创建加粗且带下划线的字体
            Font boldUnderlineFont = xwb.createFont();
            boldUnderlineFont.setFontName("宋体");
            boldUnderlineFont.setFontHeightInPoints((short) 16);
            boldUnderlineFont.setBold(true);
            boldUnderlineFont.setUnderline(Font.U_SINGLE);
            // 对日期部分应用加粗且带下划线的字体
            richText2.applyFont(0, formattedDate.length(), boldUnderlineFont);
            // 对剩余部分应用加粗字体
            richText2.applyFont(formattedDate.length(), data.length(), boldFont);
            secondCell.setCellValue(richText2);
            // 设置第二行第一列单元格的样式（水平居中和垂直居中）
            CellStyle style2 = xwb.createCellStyle();
            style2.setAlignment(HorizontalAlignment.CENTER); // 水平居中
            style2.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
            secondCell.setCellStyle(style2);
            xwb.getCreationHelper().createFormulaEvaluator().evaluateAll();
        }
    }


    private void setTempMapValue(String timeL, List<TradingUnitDO> v1, List<LinkedHashMap<String, String>> tmepMaps, List<MarketClearingDO> marketClearingDOList) {
        tmepMaps.get(2).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getTmrPower() && !"--".equals(x.getTmrPower()))).mapToDouble(x -> getFloatValueOrZero(x.getTmrPower())).sum()));
        tmepMaps.get(3).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getShortTermForecastGen() && !"--".equals(x.getShortTermForecastGen()))).mapToDouble(x -> getFloatValueOrZero(x.getShortTermForecastGen())).sum()));

        tmepMaps.get(5).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getRadixPower() && !"--".equals(x.getRadixPower()))).mapToDouble(x -> getFloatValueOrZero(x.getRadixPower())).sum()));
        tmepMaps.get(6).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getDeliveryPower() && !"--".equals(x.getDeliveryPower()))).mapToDouble(x -> getFloatValueOrZero(x.getDeliveryPower())).sum()));
        tmepMaps.get(7).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getBilateralPower() && !"--".equals(x.getBilateralPower()))).mapToDouble(x -> getFloatValueOrZero(x.getBilateralPower())).sum()));
        tmepMaps.get(8).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getDayScrollPower() && !"--".equals(x.getDayScrollPower()))).mapToDouble(x -> getFloatValueOrZero(x.getDayScrollPower())).sum()));
        tmepMaps.get(9).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getLongTermContractPrice() && !"--".equals(x.getLongTermContractPrice()))).mapToDouble(x -> getFloatValueOrZero(x.getLongTermContractPrice())).sum()));
        tmepMaps.get(10).put(timeL, "0");
        tmepMaps.get(11).put(timeL, "0");
        double hisPositivePowerSum = v1.stream().filter(x -> (null != x.getHisPositivePower() && !"--".equals(x.getHisPositivePower()))).mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePower())).sum();
        double hisNegativePowerSum = v1.stream().filter(x -> (null != x.getHisNegativePower() && !"--".equals(x.getHisNegativePower()))).mapToDouble(x -> getFloatValueOrZero(x.getHisNegativePower())).sum();
        double realPositivePowerSum = v1.stream().filter(x -> (null != x.getRealPositivePower() && !"--".equals(x.getRealPositivePower()))).mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePower())).sum();
        double realNegativePowerSum = v1.stream().filter(x -> (null != x.getRealNegativePower() && !"--".equals(x.getRealNegativePower()))).mapToDouble(x -> getFloatValueOrZero(x.getRealNegativePower())).sum();
        double hisPower = hisPositivePowerSum + hisNegativePowerSum;
        double realPower = realPositivePowerSum + realNegativePowerSum;
        tmepMaps.get(12).put(timeL, String.format("%.2f", hisPower));
        tmepMaps.get(13).put(timeL, String.format("%.2f", realPower));
        //日前加权电价
        tmepMaps.get(14).put(timeL, riQianJiaQuanDianJia(v1, hisPower, marketClearingDOList));
        tmepMaps.get(15).put(timeL, shiShiJiaQuanDianJia(v1, realPower, marketClearingDOList));

        //中长期电费=中长期电量LongTermContractPower*中长期电价 LongTermContractPrice
        double zhongchangqiDianfei = v1.stream().filter(x -> (null != x.getLongTermContractPower() && !"--".equals(x.getLongTermContractPower()))).mapToDouble(x -> getFloatValueOrZero(x.getLongTermContractPower()) * getFloatValueOrZero(x.getLongTermContractPrice())).sum();
        //日前电费=日前正现货费用hisPositivePrices+日前负现货费用hisNegativePrices
        double riqianDianfei = v1.stream().filter(x -> (null != x.getHisPositivePrices() && !"--".equals(x.getHisPositivePrices())))
                .filter(x -> (null != x.getHisNegativePrices() && !"--".equals(x.getHisNegativePrices())))
                .mapToDouble(x -> getFloatValueOrZero(x.getHisPositivePrices()) + getFloatValueOrZero(x.getHisNegativePrices())).sum();
        //实时电费=实时正现货费用realPositivePrices+实时负现货费用realNegativePrices
        double shishiDianfei = v1.stream().filter(x -> (null != x.getRealPositivePrices() && !"--".equals(x.getRealPositivePrices())))
                .filter(x -> (null != x.getRealNegativePrices() && !"--".equals(x.getRealNegativePrices())))
                .mapToDouble(x -> getFloatValueOrZero(x.getRealPositivePrices()) + getFloatValueOrZero(x.getRealNegativePrices())).sum();

        double xianhuoZongYingKui = (riqianDianfei + shishiDianfei);

        tmepMaps.get(1).put(timeL, String.format("%.2f", (zhongchangqiDianfei + xianhuoZongYingKui)));

        double tmrSum = v1.stream().filter(x -> (null != x.getTmrPower() && !"--".equals(x.getTmrPower()))).mapToDouble(x -> getFloatValueOrZero(x.getTmrPower())).sum();
        if (tmrSum == 0) {
            tmepMaps.get(4).put(timeL, "--");
        } else {
            tmepMaps.get(4).put(timeL, String.format("%.2f", (zhongchangqiDianfei + xianhuoZongYingKui) / tmrSum));
        }

        String xianHuoYingKui = xianHuoYingKui(v1, marketClearingDOList);
        tmepMaps.get(16).put(timeL, xianHuoYingKui);
        double temp17Value = hisPower + realPower;
        if (temp17Value == 0) {
            tmepMaps.get(17).put(timeL, "--");
        } else {
            tmepMaps.get(17).put(timeL, String.format("%.2f", (Convert.toDouble(xianHuoYingKui) / temp17Value)));
        }
        double pianchakaoheFeiYong = v1.stream().filter(x -> (null != x.getDeviationReviewPrices() && !"--".equals(x.getDeviationReviewPrices()))).mapToDouble(x -> getFloatValueOrZero(x.getDeviationReviewPrices())).sum();
        tmepMaps.get(18).put(timeL, String.format("%.2f", pianchakaoheFeiYong));
        tmepMaps.get(19).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices() && !"--".equals(x.getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices())))
                .mapToDouble(x -> getFloatValueOrZero(x.getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices())).sum()));
        tmepMaps.get(20).put(timeL, "0");

        tmepMaps.get(21).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getMediumLongTermBlockagePrices() && !"--".equals(x.getMediumLongTermBlockagePrices())))
                .mapToDouble(x -> getFloatValueOrZero(x.getMediumLongTermBlockagePrices())).sum()));
        tmepMaps.get(22).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getBlockingRiskHedgingPrices() && !"--".equals(x.getBlockingRiskHedgingPrices())))
                .mapToDouble(x -> getFloatValueOrZero(x.getBlockingRiskHedgingPrices())).sum()));
        tmepMaps.get(23).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getModifiedCompensationPrices() && !"--".equals(x.getModifiedCompensationPrices())))
                .mapToDouble(x -> getFloatValueOrZero(x.getModifiedCompensationPrices())).sum()));
        tmepMaps.get(24).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getNecessaryStartUpCompensationPrices() && !"--".equals(x.getNecessaryStartUpCompensationPrices())))
                .mapToDouble(x -> getFloatValueOrZero(x.getNecessaryStartUpCompensationPrices())).sum()));
        tmepMaps.get(25).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getEmergencyInvokeStartUpCompensationPrices() && !"--".equals(x.getEmergencyInvokeStartUpCompensationPrices())))
                .mapToDouble(x -> getFloatValueOrZero(x.getEmergencyInvokeStartUpCompensationPrices())).sum()));
        tmepMaps.get(26).put(timeL, String.format("%.2f", v1.stream().filter(x -> (null != x.getThermalPowerStartUpCompensationPrices() && !"--".equals(x.getThermalPowerStartUpCompensationPrices())))
                .mapToDouble(x -> getFloatValueOrZero(x.getThermalPowerStartUpCompensationPrices())).sum()));
    }

    //计算日前加权电价
    private String riQianJiaQuanDianJia(List<TradingUnitDO> v1, double hisPower, List<MarketClearingDO> marketClearingDOList) {

        List<TradingUnitDO> errorDataList = v1.stream().filter(x -> (null == x.getHisPositivePower() || "--".equals(x.getHisPositivePower())
                || null == x.getHisNegativePower() || "--".equals(x.getHisNegativePower()))).toList();
        if (!errorDataList.isEmpty()) {
            return "--";
        }

        if (hisPower == 0) {
            return "0";
        }
        if (null == marketClearingDOList) {
            return "--";
        }
        Map<Timestamp, MarketClearingDO> marketClearingDOMap = marketClearingDOList.stream().collect(Collectors.toMap(MarketClearingDO::getTs, x -> x, (x1, x2) -> x1));

        AtomicReference<Double> resultSum = new AtomicReference<>(0d);
        v1.forEach(x -> {
            if (null != marketClearingDOMap.get(x.getTs())) {
                double v = (getFloatValueOrZero(x.getHisPositivePower()) + getFloatValueOrZero(x.getHisNegativePower()))
                        * Convert.toDouble(marketClearingDOMap.get(x.getTs()).getDayAheadNodalElectricityPrice(), 0d);

                resultSum.updateAndGet(v2 -> Convert.toDouble(v2 + (v / hisPower)));
            }
        });
        return String.format("%.2f", resultSum.get());
    }

    //计算日前加权电价
    private String riQianJiaQuanDianJia2(List<TradingUnitDO> v1, double hisPower, List<MarketClearingDO> marketClearingDOList) {

        List<TradingUnitDO> errorDataList = v1.stream().filter(x -> ((null != x.getHisPositivePower() && !"--".equals(x.getHisPositivePower()))
                && (null != x.getHisNegativePower() && !"--".equals(x.getHisNegativePower())))).toList();
        if (errorDataList.isEmpty()) {
            return "--";
        }

        if (hisPower == 0) {
            return "0";
        }
        if (null == marketClearingDOList) {
            return "--";
        }
        Map<Timestamp, MarketClearingDO> marketClearingDOMap = marketClearingDOList.stream().collect(Collectors.toMap(MarketClearingDO::getTs, x -> x, (x1, x2) -> x1));

        AtomicReference<Double> resultSum = new AtomicReference<>(0d);
        v1.forEach(x -> {
            if (null != marketClearingDOMap.get(x.getTs())) {
                double v = (getFloatValueOrZero(x.getHisPositivePower()) + getFloatValueOrZero(x.getHisNegativePower()))
                        * Convert.toDouble(marketClearingDOMap.get(x.getTs()).getDayAheadNodalElectricityPrice(), 0d);

                resultSum.updateAndGet(v2 -> Convert.toDouble(v2 + (v / hisPower)));
            }
        });
        return String.format("%.2f", resultSum.get());
    }

    //现货盈亏总额
    private String xianHuoYingKui(List<TradingUnitDO> v1, List<MarketClearingDO> marketClearingDOList) {

        List<TradingUnitDO> errorDataList = v1.stream().filter(x -> (null == x.getHisPositivePower() || "--".equals(x.getHisPositivePower())
                || null == x.getHisNegativePower() || "--".equals(x.getHisNegativePower()))).toList();
        if (!errorDataList.isEmpty()) {
            return "0";
        }

        if (null == marketClearingDOList) {
            return "0";
        }
        Map<Timestamp, MarketClearingDO> marketClearingDOMap = marketClearingDOList.stream().collect(Collectors.toMap(MarketClearingDO::getTs, x -> x, (x1, x2) -> x1));

        AtomicReference<Double> resultSum = new AtomicReference<>(0d);
        v1.forEach(x -> {
            if (null != marketClearingDOMap.get(x.getTs())) {
                double v = (getFloatValueOrZero(x.getHisPositivePower()) + getFloatValueOrZero(x.getHisNegativePower()))
                        * Convert.toDouble(marketClearingDOMap.get(x.getTs()).getDayAheadNodalElectricityPrice(), 0d);
                double v3 = (getFloatValueOrZero(x.getRealPositivePower()) + getFloatValueOrZero(x.getRealNegativePower()))
                        * Convert.toDouble(marketClearingDOMap.get(x.getTs()).getRealNodalElectricityPrice(), 0d);
                resultSum.updateAndGet(v2 -> Convert.toDouble(v2 + (v + v3)));
            }
        });
        return String.format("%.2f", resultSum.get());
    }

    //现货盈亏总额
    private String xianHuoYingKui2(List<TradingUnitDO> v1, List<MarketClearingDO> marketClearingDOList) {

        List<TradingUnitDO> errorDataList = v1.stream().filter(x -> ((null != x.getRealPositivePower() && !"--".equals(x.getRealPositivePower()))
                && (null != x.getRealNegativePower() && !"--".equals(x.getRealNegativePower()))) && ((null != x.getHisPositivePower() && !"--".equals(x.getHisPositivePower()))
                && (null != x.getHisNegativePower() && !"--".equals(x.getHisNegativePower())))).toList();
        if (errorDataList.isEmpty()) {
            return "0";
        }

        if (null == marketClearingDOList) {
            return "0";
        }
        Map<Timestamp, MarketClearingDO> marketClearingDOMap = marketClearingDOList.stream().collect(Collectors.toMap(MarketClearingDO::getTs, x -> x, (x1, x2) -> x1));

        AtomicReference<Double> resultSum = new AtomicReference<>(0d);
        v1.forEach(x -> {
            if (null != marketClearingDOMap.get(x.getTs())) {
                double v = (getFloatValueOrZero(x.getHisPositivePower()) + getFloatValueOrZero(x.getHisNegativePower()))
                        * Convert.toDouble(marketClearingDOMap.get(x.getTs()).getDayAheadNodalElectricityPrice(), 0d);
                double v3 = (getFloatValueOrZero(x.getRealPositivePower()) + getFloatValueOrZero(x.getRealNegativePower()))
                        * Convert.toDouble(marketClearingDOMap.get(x.getTs()).getRealNodalElectricityPrice(), 0d);
                resultSum.updateAndGet(v2 -> Convert.toDouble(v2 + (v + v3)));
            }
        });
        return String.format("%.2f", resultSum.get());
    }

    //计算实时加权电价
    private String shiShiJiaQuanDianJia(List<TradingUnitDO> v1, double realPower, List<MarketClearingDO> marketClearingDOList) {

        List<TradingUnitDO> errorDataList = v1.stream().filter(x -> (null == x.getRealPositivePower() || "--".equals(x.getRealPositivePower())
                || null == x.getRealNegativePower() || "--".equals(x.getRealNegativePower()))).toList();

        if (!errorDataList.isEmpty()) {
            return "--";
        }

        if (realPower == 0) {
            return "0";
        }
        if (null == marketClearingDOList) {
            return "--";
        }
        Map<Timestamp, MarketClearingDO> marketClearingDOMap = marketClearingDOList.stream().collect(Collectors.toMap(MarketClearingDO::getTs, x -> x, (x1, x2) -> x1));

        AtomicReference<Double> resultSum = new AtomicReference<>(0d);
        v1.forEach(x -> {
            if (null != marketClearingDOMap.get(x.getTs())) {
                double v = (getFloatValueOrZero(x.getRealPositivePower()) + getFloatValueOrZero(x.getRealNegativePower()))
                        * Convert.toDouble(marketClearingDOMap.get(x.getTs()).getRealNodalElectricityPrice(), 0d);

                resultSum.updateAndGet(v2 -> Convert.toDouble(v2 + (v / realPower)));
            }
        });
        return String.format("%.2f", resultSum.get());
    } //计算实时加权电价

    private String shiShiJiaQuanDianJia2(List<TradingUnitDO> v1, double realPower, List<MarketClearingDO> marketClearingDOList) {

        List<TradingUnitDO> errorDataList = v1.stream().filter(x -> ((null != x.getRealPositivePower() && !"--".equals(x.getRealPositivePower()))
                && (null != x.getRealNegativePower() && !"--".equals(x.getRealNegativePower())))).toList();
        if (errorDataList.isEmpty()) {
            return "--";
        }

        if (realPower == 0) {
            return "0";
        }
        if (null == marketClearingDOList) {
            return "--";
        }
        Map<Timestamp, MarketClearingDO> marketClearingDOMap = marketClearingDOList.stream().collect(Collectors.toMap(MarketClearingDO::getTs, x -> x, (x1, x2) -> x1));

        AtomicReference<Double> resultSum = new AtomicReference<>(0d);
        v1.forEach(x -> {
            if (null != marketClearingDOMap.get(x.getTs())) {
                double v = (getFloatValueOrZero(x.getRealPositivePower()) + getFloatValueOrZero(x.getRealNegativePower()))
                        * Convert.toDouble(marketClearingDOMap.get(x.getTs()).getRealNodalElectricityPrice(), 0d);

                resultSum.updateAndGet(v2 -> Convert.toDouble(v2 + (v / realPower)));
            }
        });
        return String.format("%.2f", resultSum.get());
    }

    private void setSummary(List<LinkedHashMap<String, String>> tmepMaps) {

        List<String> list1 = new ArrayList<>(tmepMaps.get(1).values());
        tmepMaps.get(1).put("Summary", String.format("%.2f", list1.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list2 = new ArrayList<>(tmepMaps.get(2).values());
        tmepMaps.get(2).put("Summary", String.format("%.2f", list2.stream().mapToDouble(Convert::toDouble).sum()));

        List<String> list3 = new ArrayList<>(tmepMaps.get(3).values());
        tmepMaps.get(3).put("Summary", String.format("%.2f", list3.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list4 = new ArrayList<>(tmepMaps.get(4).values());
        tmepMaps.get(4).put("Summary", String.format("%.2f", list4.stream().mapToDouble(this::getFloatValueOrZero).average().orElse(0)));

        List<String> list5 = new ArrayList<>(tmepMaps.get(5).values());
        tmepMaps.get(5).put("Summary", String.format("%.2f", list5.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list6 = new ArrayList<>(tmepMaps.get(6).values());
        tmepMaps.get(6).put("Summary", String.format("%.2f", list6.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list7 = new ArrayList<>(tmepMaps.get(7).values());
        tmepMaps.get(7).put("Summary", String.format("%.2f", list7.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list8 = new ArrayList<>(tmepMaps.get(8).values());
        tmepMaps.get(8).put("Summary", String.format("%.2f", list8.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list9 = new ArrayList<>(tmepMaps.get(9).values());
        tmepMaps.get(9).put("Summary", String.format("%.2f", list9.stream().mapToDouble(Convert::toDouble).average().orElse(0)));
        List<String> list10 = new ArrayList<>(tmepMaps.get(10).values());
        tmepMaps.get(10).put("Summary", String.format("%.2f", list10.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list11 = new ArrayList<>(tmepMaps.get(11).values());
        tmepMaps.get(11).put("Summary", String.format("%.2f", list11.stream().mapToDouble(Convert::toDouble).average().orElse(0)));

        List<String> list12 = new ArrayList<>(tmepMaps.get(12).values());
        tmepMaps.get(12).put("Summary", String.format("%.2f", list12.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list13 = new ArrayList<>(tmepMaps.get(13).values());
        tmepMaps.get(13).put("Summary", String.format("%.2f", list13.stream().mapToDouble(Convert::toDouble).sum()));
        /*List<String> list14 = new ArrayList<>(tmepMaps.get(14).values());
        tmepMaps.get(14).put("Summary", String.format("%.2f", list14.stream().mapToDouble(this::getFloatValueOrZero).average().orElse(0)));
        List<String> list15 = new ArrayList<>(tmepMaps.get(15).values());
        tmepMaps.get(15).put("Summary", String.format("%.2f", list15.stream().mapToDouble(this::getFloatValueOrZero).average().orElse(0)));
        List<String> list16 = new ArrayList<>(tmepMaps.get(16).values());
        tmepMaps.get(16).put("Summary", String.format("%.2f", list16.stream().mapToDouble(this::getFloatValueOrZero).sum()));
        List<String> list17 = new ArrayList<>(tmepMaps.get(17).values());
        tmepMaps.get(17).put("Summary", String.format("%.2f", list17.stream().mapToDouble(this::getFloatValueOrZero).average().orElse(0)));*/

        List<String> list18 = new ArrayList<>(tmepMaps.get(18).values());
        tmepMaps.get(18).put("Summary", String.format("%.2f", list18.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list19 = new ArrayList<>(tmepMaps.get(19).values());
        tmepMaps.get(19).put("Summary", String.format("%.2f", list19.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list20 = new ArrayList<>(tmepMaps.get(20).values());
        tmepMaps.get(20).put("Summary", String.format("%.2f", list20.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list21 = new ArrayList<>(tmepMaps.get(21).values());
        tmepMaps.get(21).put("Summary", String.format("%.2f", list21.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list22 = new ArrayList<>(tmepMaps.get(22).values());
        tmepMaps.get(22).put("Summary", String.format("%.2f", list22.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list23 = new ArrayList<>(tmepMaps.get(23).values());
        tmepMaps.get(23).put("Summary", String.format("%.2f", list23.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list24 = new ArrayList<>(tmepMaps.get(24).values());
        tmepMaps.get(24).put("Summary", String.format("%.2f", list24.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list25 = new ArrayList<>(tmepMaps.get(25).values());
        tmepMaps.get(25).put("Summary", String.format("%.2f", list25.stream().mapToDouble(Convert::toDouble).sum()));
        List<String> list26 = new ArrayList<>(tmepMaps.get(26).values());
        tmepMaps.get(26).put("Summary", String.format("%.2f", list26.stream().mapToDouble(Convert::toDouble).sum()));
    }

    /**
     * 交易复盘-预测偏差分析
     *
     * @param date
     * @return
     */
    @Override
    public Map<String, List<String>> getDeviation(String unitId, Long[] date) {
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }
        List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getDeviation(unitId, timestamps[0], timestamps[timestamps.length - 1]);
        //预测偏差电量=预测发电量(ShortTermForecast / 4)-实际发电量(tmr)
        Map<String, List<String>> map = new HashMap<>();
        List<String> realPower = new ArrayList<>();
        List<String> forecastPower = new ArrayList<>();
        List<String> devseries = new ArrayList<>();
        List<String> deviationRate = new ArrayList<>();
        for (TradingUnitDO tradingUnitDO : tradingUnitDOList) {
            //预测发电量(ShortTermForecast / 4)
            String forecast = tradingUnitDO.getShortTermForecast().equals("--") ? "--" : new BigDecimal(tradingUnitDO.getShortTermForecast()).divide(new BigDecimal(4), 4, RoundingMode.HALF_UP).toString();
            //预测偏差电量=预测发电量-实际发电量(tmr)
            BigDecimal devserie = parseNumber(forecast).subtract(parseNumber(tradingUnitDO.getTmrPower())).setScale(4, RoundingMode.HALF_UP);
            realPower.add(tradingUnitDO.getTmrPower());
            forecastPower.add(forecast);
            devseries.add(devserie.toString());
            //偏差率：(实际发电量-预测发电量)/预测发电量
            if (parseNumber(forecast).compareTo(BigDecimal.ZERO) == 0) {
                deviationRate.add("0");
            } else {
                BigDecimal devserie1 = parseNumber(tradingUnitDO.getTmrPower()).subtract(parseNumber(forecast)).setScale(4, RoundingMode.HALF_UP);
                deviationRate.add(devserie1.divide(parseNumber(forecast), 4, RoundingMode.HALF_UP).toString());
            }
        }
        map.put("realPower", realPower);
        map.put("forecastPower", forecastPower);
        map.put("devseries", devseries);
        map.put("deviationRate", deviationRate);
        return map;
    }

    /**
     * 阻塞对冲机制费用计算
     *
     * @param unitId 交易单元
     * @param dates  日期
     * @return
     */
    @Override
    public Map<String, Object> getCongestionCost(String unitId, Long[] dates) {
        //settlement_power/tmr_power tmr电量/scada 这三个是实时电量 有优先级
        //long_term_contract_power 中长期合同电量
        //his_unified_settlement_point_price 日前统一结算点价格。
        //day_ahead_nodal_electricity_price 节点电价
        String start = DateUtil.format(new Date(dates[0]), "yyyy-MM-dd HH:mm:ss");
        Timestamp startDate = Timestamp.valueOf(start);
        // 使用 Calendar 进行时间操作
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startDate.getTime());
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 获取下个月 1 号的时间戳
        Timestamp endDate = new Timestamp(calendar.getTimeInMillis());
        Long[] date = new Long[2];
        date[0] = startDate.getTime();
        date[1] = endDate.getTime() - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }
        DispatchNodeDO dispatchNodeDO = dispatchNodeService.getDispatchNodeByCode(unitId);
        List<TradingUnitDO> tradingUnitDOList = tradingUnitMapper.getCongestionCost(unitId, timestamps[0], timestamps[timestamps.length - 1]);
        //变电站，通过，主库场站（power_station表）org_code，查询gs分库 substation 表，查询对应变电站节点
        PowerStationDO powerStationDO = powerStationService.getPowerStationByCode(dispatchNodeDO.getOrgCode());
        SubstationDO substationDO = substationService.getSubstationByOrgCode(powerStationDO.getCode());
        List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getSettlementPrice(powerStationDO.getOrgCode(), timestamps[0], timestamps[timestamps.length - 1]);
        List<MarketClearingDO> marketClearingDOList = marketClearingMapper.getCongestionCost(powerStationDO.getOrgCode(), timestamps[0], timestamps[timestamps.length - 1], substationDO.getTableName());
        Map<String, Object> map = new LinkedHashMap<>();
        int flag = 0;
        for (long time = date[0]; time <= date[1]; time += 24 * 60 * 60 * 1000) {
            flag++;
            long startTime = time;
            long endTime = time + 24 * 60 * 60 * 1000L;
            List<TradingUnitDO> tradingCollect = tradingUnitDOList.stream().filter(tradingUnitDO -> tradingUnitDO.getTs().getTime() >= startTime && tradingUnitDO.getTs().getTime() < endTime).toList();
            List<CompanyUnitDO> companyCollect = companyUnitDOList.stream().filter(companyUnitDO -> companyUnitDO.getTs().getTime() >= startTime && companyUnitDO.getTs().getTime() < endTime).toList();
            List<MarketClearingDO> marketClearingCollect = marketClearingDOList.stream().filter(marketClearingDO -> marketClearingDO.getTs().getTime() >= startTime && marketClearingDO.getTs().getTime() < endTime).toList();
            Map<String, Object> map1 = new LinkedHashMap<>();
            //中长期电量
            List<BigDecimal> longTermContractPowerList = tradingCollect.stream().map(item -> Objects.equals(item.getLongTermContractPower(), "--") ? null : new BigDecimal(item.getLongTermContractPower()).divide(new BigDecimal(4), 5, RoundingMode.HALF_UP)).toList();
            //实发电量 先取settlement_power结算电量 如果为空取tmr tmr为空就取scada scada如果也为空则直接set null
            List<BigDecimal> realPower = new ArrayList<>();
            for (TradingUnitDO t : tradingCollect) {
                if (!t.getSettlementPower().equals("--")) {
                    realPower.add(new BigDecimal(t.getSettlementPower()).setScale(3, RoundingMode.HALF_UP));
                } else if (!t.getTmrPower().equals("--")) {
                    realPower.add(new BigDecimal(t.getTmrPower()).setScale(3, RoundingMode.HALF_UP));
                } else if (!t.getScadaPower().equals("--")) {
                    realPower.add(new BigDecimal(t.getScadaPower()).setScale(3, RoundingMode.HALF_UP));
                } else {
                    realPower.add(null);
                }
            }
            //统一结算点电价
            List<BigDecimal> unifiedSettlementPointPrice = companyCollect.stream().map(item -> Objects.equals(item.getHisUnifiedSettlementPointPrice(), "--") ? null : new BigDecimal(item.getHisUnifiedSettlementPointPrice()).setScale(2, RoundingMode.HALF_UP)).toList();
            //节点电价
            List<BigDecimal> nodalElectricityPrice = marketClearingCollect.stream().map(item -> BigDecimal.valueOf(item.getDayAheadNodalElectricityPrice()).setScale(2, RoundingMode.HALF_UP)).toList();
            //中长期阻塞费用=中长期电量*(统一结算点电价-节点电价)
            List<BigDecimal> longTermBlockagePrices = new ArrayList<>();
            for (int i = 0; i < longTermContractPowerList.size(); i++) {
                //判断三个List是否有值
                if (i < unifiedSettlementPointPrice.size() && i < nodalElectricityPrice.size()) {
                    BigDecimal longTermContractPower = longTermContractPowerList.get(i);
                    BigDecimal unifiedSettlementPoint = unifiedSettlementPointPrice.get(i);
                    BigDecimal nodalElectricity = nodalElectricityPrice.get(i);
                    //判断三个数据是否为null
                    if (longTermContractPower != null && unifiedSettlementPoint != null && nodalElectricity != null) {
                        try {
                            longTermBlockagePrices.add(longTermContractPower.multiply(unifiedSettlementPoint.subtract(nodalElectricity)).setScale(2, RoundingMode.HALF_UP));
                        } catch (NumberFormatException e) {
                            // 处理解析异常
                            longTermBlockagePrices.add(null);
                        }
                    } else {
                        longTermBlockagePrices.add(null);
                    }
                } else {
                    longTermBlockagePrices.add(null);
                }
            }
            //对冲机制费用 excel公式为 IF((统一结算点电价-节点电价)>0,(统一结算点电价-节点电价)*MIN(中长期电价:实发电量),(统一结算点电价-节点电价)*中长期电价)
            List<BigDecimal> offsettingMechanismPrices = new ArrayList<>();
            for (int i = 0; i < unifiedSettlementPointPrice.size() && i < nodalElectricityPrice.size() && i < longTermContractPowerList.size() && i < realPower.size(); i++) {
                BigDecimal unifiedSettlementPoint = unifiedSettlementPointPrice.get(i);
                BigDecimal nodalElectricity = nodalElectricityPrice.get(i);
                BigDecimal longTermContractPower = longTermContractPowerList.get(i);
                BigDecimal realPowerValue = realPower.get(i);
                if (unifiedSettlementPoint != null && nodalElectricity != null && longTermContractPower != null && realPowerValue != null) {
                    try {
                        BigDecimal priceDifference = unifiedSettlementPoint.subtract(nodalElectricity);
                        BigDecimal offsettingMechanismPrice;
                        if (priceDifference.compareTo(BigDecimal.ZERO) > 0) {
                            offsettingMechanismPrice = priceDifference.multiply(longTermContractPower.min(realPowerValue));
                        } else {
                            offsettingMechanismPrice = priceDifference.multiply(longTermContractPower);
                        }
                        offsettingMechanismPrices.add(offsettingMechanismPrice.setScale(2, RoundingMode.HALF_UP));
                    } catch (NumberFormatException e) {
                        // 处理解析异常
                        offsettingMechanismPrices.add(null);
                    }
                } else {
                    offsettingMechanismPrices.add(null);
                }
            }

            map1.put("中长期电量", longTermContractPowerList);
            map1.put("实发电量", realPower);
            map1.put("统一结算点电价", unifiedSettlementPointPrice);
            map1.put(substationDO.getName() + "节点电价", nodalElectricityPrice);
            map1.put("中长期阻塞费用", longTermBlockagePrices);
            map1.put("对冲机制费用", offsettingMechanismPrices);
            map.put(flag + "日", map1);
        }
        return map;
    }

    @Override
    public void downloadExcelTemplate(HttpServletResponse response, HttpServletRequest request, String unitId, Long[] date) {
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("excel/template.xlsx");
        ServletOutputStream out = null;
        XSSFWorkbook xwb = null;
        try {
            xwb = new XSSFWorkbook(inputStream);
            XSSFSheet longTermContractPowerSheet = xwb.getSheetAt(0);
            XSSFSheet realPowerSheet = xwb.getSheetAt(1);
            XSSFSheet unifiedSettlementPointPriceSheet = xwb.getSheetAt(2);
            XSSFSheet nodalElectricityPriceSheet = xwb.getSheetAt(3);

            Map<String, Object> map = this.getCongestionCost(unitId, date);
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

            AtomicInteger longTermContractPowerRow = new AtomicInteger(1);
            AtomicInteger realPowerRow = new AtomicInteger(1);
            AtomicInteger unifiedSettlementPointPriceRow = new AtomicInteger(1);
            AtomicInteger nodalElectricityPriceRow = new AtomicInteger(1);

            longTermContractPowerSheet.setForceFormulaRecalculation(true);
            realPowerSheet.setForceFormulaRecalculation(true);
            unifiedSettlementPointPriceSheet.setForceFormulaRecalculation(true);
            nodalElectricityPriceSheet.setForceFormulaRecalculation(true);
            map.forEach((key, value) -> {
                Map<String, Object> map1 = (Map<String, Object>) value;
                map1.forEach((key1, value1) -> {
                    List<String> datas = (List<String>) value1;
                    if ("中长期电量".equals(key1)) {
                        setCellValueForSheet(longTermContractPowerSheet, key, longTermContractPowerRow.get(), datas);
                        longTermContractPowerRow.getAndIncrement();
                    } else if ("实发电量".equals(key1)) {
                        setCellValueForSheet(realPowerSheet, key, realPowerRow.get(), datas);
                        realPowerRow.getAndIncrement();
                    } else if ("统一结算点电价".equals(key1)) {
                        setCellValueForSheet(unifiedSettlementPointPriceSheet, key, unifiedSettlementPointPriceRow.get(), datas);
                        unifiedSettlementPointPriceRow.getAndIncrement();
                    } else if (key1.contains("节点电价")) {
                        setCellValueForSheet(nodalElectricityPriceSheet, key, nodalElectricityPriceRow.get(), datas);
                        nodalElectricityPriceRow.getAndIncrement();
                    }
                });
            });

            String name = "阻塞对冲机制费用计算 " + formatter.format(new Date(date[0])) + "至" + formatter.format(new Date(date[date.length - 1])) + ".xlsx";
            String fileName = URLEncoder.encode(name, StandardCharsets.UTF_8);
            response.setContentType("application/msexcel");
            //response.setContentType("multipart/form-data");
            response.addHeader("Content-Type", "application/octet-stream;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
            out = response.getOutputStream();
            xwb.write(out);
            out.flush();
        } catch (Exception ex) {
            // 记录异常信息
            ex.printStackTrace();
            IoUtil.close(out);
        } finally {
            IoUtil.close(inputStream);
            if (xwb != null) {
                try {
                    xwb.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void setCellValueForSheet(XSSFSheet sheet, String key, int rowLength, List<String> values) {

        XSSFRow row = sheet.createRow(rowLength);

        XSSFCell cell0 = row.createCell(0);//第0列
        cell0.setCellValue(key);
        CellStyle cellStyle = sheet.getWorkbook().createCellStyle();
        for (int i = 1; i <= values.size(); i++) {
            XSSFCell cell = row.createCell(i); // 第一列
            Object value = values.get(i - 1);
            if (value == null) {
                cell.setCellValue("");
            } else if (value instanceof String) {
                cell.setCellValue(Double.parseDouble(value.toString()));
            } else if (value instanceof Number) {
                // 如果是数字类型（包括 Integer, Float, Double 等），可以使用 setCellValue(double)
                cell.setCellValue(((Number) value).doubleValue());
            } else {
                // 其他类型的对象，可以调用 toString() 方法
                cell.setCellValue(value.toString());
            }
            cell.setCellStyle(cellStyle);
        }
    }

    @Override
    public List<TradingUnitDO> getTradingUnitListByTenantIgnore() {
        return tradingUnitMapper.getTradingUnitListByTenantIgnore();
    }
}