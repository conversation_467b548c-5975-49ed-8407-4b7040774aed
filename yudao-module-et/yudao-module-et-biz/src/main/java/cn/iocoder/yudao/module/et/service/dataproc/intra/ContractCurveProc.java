package cn.iocoder.yudao.module.et.service.dataproc.intra;

import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.jy.soft.remote.bean.SocketPacket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * GS-B-411 中长期计划管理-中长期合同曲线发布
 *
 * <AUTHOR>
 * @date 2024-11-30
 **/
@Slf4j
@Service
public class ContractCurveProc implements BaseDataProc {
    @Override
    public boolean execute(SocketPacket dto) {
        return true;
    }
}
