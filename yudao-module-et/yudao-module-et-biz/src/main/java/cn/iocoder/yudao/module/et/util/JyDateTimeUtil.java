package cn.iocoder.yudao.module.et.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.time.DateUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 时间工具类
 */
@SuppressWarnings("WeakerAccess")
public class JyDateTimeUtil {

    /**
     * 获取月份
     *
     * @param dateTime 时间，单位：毫秒
     * @return 月份
     */
    public static int getMonth(final Long dateTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(dateTime);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 获取指定时间在当天所处于的时刻(15分钟为一个时刻)
     * 例如：dateTime：2018-10-22 16:44:00
     * 从0点0分开始计算，0点0分-0点15为第一个时刻，依次类推该时间的时刻数为67
     *
     * @param dateTime 时间，单位：毫秒
     * @return 时刻
     */
    public static int getMomentFor15Minute(final Long dateTime) {
        return (int) ((dateTime - JyDateTimeUtil.getDayStartTime(dateTime).getTime()) / (15 * 60 * 1000)) + 1;
    }

    /**
     * 获取指定时间在当天所处于的时刻(5分钟为一个时刻)
     * 例如：dateTime：2018-10-22 01:44:00
     * 从0点0分开始计算，0点0分-0点5为第一个时刻，依次类推该时间的时刻数为
     *
     * @param dateTime 时间，单位：毫秒
     * @return 时刻
     */
    public static int getMomentFor5Minute(final Long dateTime) {
        return (int) ((dateTime - JyDateTimeUtil.getDayStartTime(dateTime).getTime()) / (5 * 60 * 1000)) + 1;
    }

    /**
     * 获取指定时间所在时刻的标记时间(15分钟为一个时刻)<br>
     * 例如：dateTime:2018-10-22 16:44:00 <br>
     * 标记时间为2018-10-22 16:30:00
     *
     * @param dateTime 时间，单位：毫秒
     * @return 指定时间所在时刻的标记时间，单位：毫秒
     */
    public static Date getMomentTimeFor15Minute(final Long dateTime) {
        int moment = JyDateTimeUtil.getMomentFor15Minute(dateTime);// 获取指定时间所在时刻数
        long differentTime = (long) moment * 15 * 60 * 1000;// 获取从0分0秒开始到现在的时间间隔，单位：毫秒
        long dayStartTime = JyDateTimeUtil.getDayStartTime(dateTime).getTime();
        return new Date(dayStartTime + differentTime - 15 * 60 * 1000);
    }

    /**
     * 获取指定时间所在时刻的标记时间(5分钟为一个时刻)<br>
     * 例如：dateTime:2018-10-22 16:44:00 <br>
     * 标记时间为2018-10-22 16:40:00
     *
     * @param dateTime 时间，单位：毫秒
     * @return 指定时间所在时刻的标记时间，单位：毫秒
     */
    public static Date getMomentTimeFor5Minute(final Long dateTime) {
        int moment = JyDateTimeUtil.getMomentFor15Minute(dateTime);// 获取指定时间所在时刻数
        long differentTime = (long) moment * 5 * 60 * 1000;// 获取从0分0秒开始到现在的时间间隔，单位：毫秒
        long dayStartTime = JyDateTimeUtil.getDayStartTime(dateTime).getTime();
        return new Date(dayStartTime + differentTime - 5 * 60 * 1000);
    }

    /**
     * 获取当前时间（时间粒度分钟）
     *
     * @return 当前时间
     */
    public static Date getCurrentTimeForMinute() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定时间所在天的0点0分
     *
     * @param dateTime 时间毫秒
     * @return 0点0分的毫秒
     */
    public static Date getDayStartTime(final Long dateTime) {
        Calendar date = Calendar.getInstance();
        date.setTimeInMillis(dateTime);
        date.set(Calendar.HOUR_OF_DAY, 0);
        date.set(Calendar.MINUTE, 0);
        date.set(Calendar.SECOND, 0);
        date.set(Calendar.MILLISECOND, 0);
        return date.getTime();
    }

    /**
     * 今日凌晨 毫秒
     *
     * @return 时间
     */
    public static long getMillisecondsSubDay() {

        Calendar c = Calendar.getInstance();

        return c.getTimeInMillis() - DateUtils.getFragmentInMilliseconds(c, Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取指定时间所在天的23点59分59秒
     *
     * @param dateTime 时间毫秒
     * @return 23点59分59秒的毫秒
     */
    public static Date getDayLastTime(final Long dateTime) {
        Calendar date = Calendar.getInstance();
        date.setTimeInMillis(dateTime);
        date.set(Calendar.HOUR_OF_DAY, 23);
        date.set(Calendar.MINUTE, 59);
        date.set(Calendar.SECOND, 59);
        date.set(Calendar.MILLISECOND, 999);
        return date.getTime();
    }

    /**
     * 获取两个时间间的间隔天数(自然天)
     *
     * @param dateFrom 开始时间
     * @param dateTo   结束时间
     * @return 自然天数
     */
    public static int getDaysBetweenTwoDate(Long dateFrom, Long dateTo) {
        return (int) (
                (JyDateTimeUtil.getDayStartTime(dateTo).getTime() - JyDateTimeUtil.getDayStartTime(dateFrom).getTime()) / (
                        1000 * 60 * 60 * 24L));
    }

    /**
     * 获取两个时间间的间隔间隔时刻数(15分钟一个时刻)
     *
     * @param dateFrom 开始时间
     * @param dateTo   结束时间
     * @return 间隔时刻数
     */
    public static int getMomentsBetweenTwoDate(Long dateFrom, Long dateTo) {
        return (int) ((dateTo - dateFrom) / (15 * 60 * 1000));
    }

    /**
     * 获取现在时间
     *
     * @return 返回字符串格式 yyyy-MM-dd HH:mm:ss
     */
    public static String getStringDate() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 将长时间格式时间转换为字符串 yyyy-MM-dd HH:mm:ss
     *
     * @param dateDate
     * @return
     */
    public static String dateToStrLong(Date dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 将长时间格式时间转换为字符串 yyyy-MM-dd HH:mm:ss
     *
     * @param dateDate
     * @return
     */
    public static String getFormatDateStr(Long dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 将长时间格式时间转换为字符串 yyyy-MM-dd HH:mm
     *
     * @param dateDate
     * @return
     */
    public static String getFormatDateStr2(Long dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 将长时间格式时间转换为字符串  HH:mm
     *
     * @param dateDate
     * @return
     */
    public static String getFormatDateStr3(Long dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 将长时间格式时间转换为字符串  dd
     *
     * @param dateDate
     * @return
     */
    public static String getFormatDateStr4(Long dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("dd");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 将长时间格式时间转换为字符串 yyyy-MM-dd HH:mm
     *
     * @param dateDate
     * @return
     */
    public static String getFormatDateStrForHH(Long dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 将长时间格式时间转换为字符串 yyyy-MM-dd
     *
     * @param dateDate
     * @return
     */
    public static String getFormatDateStrForDay(Long dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 将长时间格式时间转换为字符串 yyyy-MM-dd HH:mm
     *
     * @param dateDate
     * @return
     */
    public static String getFormatDateStrForHour(Long dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("HH");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 将长时间格式时间转换为字符串 yyyy-MM-dd
     *
     * @param dateDate
     * @return
     */
    public static String getFormatDateStrForMonth(Long dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 将长时间格式时间转换为字符串 MM
     *
     * @param dateDate
     * @return
     */
    public static String getFormatDateStrForMonthStr(Long dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("MM");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 将长时间格式时间转换为字符串 yyyy
     *
     * @param dateDate
     * @return
     */
    public static String getFormatDateStrForYearStr(Long dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy");
        return formatter.format(dateDate);
    }

    /**
     * 将长时间格式时间转换为字符串 MM
     *
     * @param dateDate
     * @return
     */
    public static String getFormatDateStrForMonthStr2(Long dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("M");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 将时间格式字符串转换为时间 HH:mm
     *
     * @param strDate
     * @return
     */
    public static Date strToDateLongHM(String strDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
    }

    /**
     * 判断时间是不是今天
     *
     * @param date
     * @return 是返回true，不是返回false
     */
    public static boolean isNow(Date date) {
        //当前时间
        Date now = new Date();
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        //获取今天的日期
        String nowDay = sf.format(now);
        //对比的时间
        String day = sf.format(date);
        return day.equals(nowDay);
    }

    /**
     * 获取指定时间所在的月初
     *
     * @param date
     * @return
     */
    public static Date beginOfMonth(Date date) {
        return DateUtil.beginOfMonth(date);
    }

    /**
     * 获取指定时间所在的月末
     *
     * @param date
     * @return
     */
    public static Date endOfMonth(Date date) {
        return DateUtil.endOfMonth(date);
    }

    /**
     * 将时间戳转换为指定格式并加上分钟
     *
     * @param
     * @param strDate
     * @param hour
     * @return
     */
    @SneakyThrows
    public static Date strToDateLongYMDH(long strDate, Integer hour, String minute) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String strtodate = formatter.format(new Date(strDate));
        return formatter2.parse(strtodate + " " + hour + ":" + minute);
    }

    /**
     * 时间戳 获取天
     */
    public static int getDay(Long time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String timeString = sdf.format(time);
        try {
            date = sdf.parse(timeString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int day = (calendar.get(Calendar.DAY_OF_MONTH));
        return day;
    }

    /*当月的前n个月得第一天*/
    public static Date mouthOneDay(int num) {
        Calendar date = Calendar.getInstance();
        date.set(Calendar.MONDAY, date.get(Calendar.MONDAY) - num);
        date.set(Calendar.DAY_OF_MONTH, 1);
        return date.getTime();
    }

    /*当月的前n个月得最后一天*/
    public static Date mouthLastDay(int num) {
        Calendar date = Calendar.getInstance();
        date.set(Calendar.MONDAY, date.get(Calendar.MONDAY) - num);
        date.set(Calendar.DAY_OF_MONTH, date.getActualMaximum(Calendar.DAY_OF_MONTH));
        return date.getTime();
    }

    /*
     * 指定去年的某一天
     *
     */
    public static Date getYearDay(Long dayTime) {
        //过去一年
        Calendar date = Calendar.getInstance();
        date.setTime(new Date(dayTime));
        date.add(Calendar.YEAR, -1);
        return date.getTime();
    }

    /**
     * 按照开始时间结束时间分割出所有月份(开始时间月初 结束时间月末)
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    public static List<Long> getIntervalTimeByMonth(Date startTime, Date endTime) {
        startTime = beginOfMonth(startTime);
        endTime = endOfMonth(endTime);
        List<Long> dateList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        dateList.add(calendar.getTimeInMillis());
        while (calendar.getTimeInMillis() < endTime.getTime()) {
            // 当月底
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.add(Calendar.DATE, -1);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            if (calendar.getTimeInMillis() >= DateUtil.beginOfDay(endTime).getTime()) {
                //                dateList.add(endTime.getTime());
                break;
            }
            // 下月初
            calendar.add(Calendar.DATE, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            dateList.add(calendar.getTimeInMillis());
        }
        //        dateList.remove(dateList.size() - 1);
        return dateList;
    }

    /**
     * 根据传入的时间获取下个月一号的时间戳
     *
     * @param time
     * @return
     */
    public static long nextMonthFirstDayTimestamp(long time) {

        // 将时间戳转换为LocalDate
        LocalDate currentMonthFirstDay = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(time),
                ZoneId.systemDefault()).toLocalDate();

        // 获取下个月的第一天
        LocalDate nextMonthFirstDay = currentMonthFirstDay.plusMonths(1).withDayOfMonth(1);

        // 将LocalDate转换回时间戳
        return nextMonthFirstDay.atStartOfDay()
                .atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli();
    }

    /**
     * 获取给定日期所在年的第一天的0点0分0秒的Date对象。
     *
     * @param date 给定的日期
     * @return 给定年份第一天的0点0分0秒的Date对象
     */
    public static Date getFirstDayOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MONTH, Calendar.JANUARY); // 月份从0开始计数，所以1月是0
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取给定日期所在年份的最后一天的23点59分59秒的Date对象。
     *
     * @param inputDate 给定的日期
     * @return 当年最后一天23点59分59秒的Date对象
     */
    public static Date getLastSecondOfYear(Date inputDate) {
        // 将Date转换为LocalDate
        LocalDate localDate = inputDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 获取当年最后一天
        LocalDate endOfYear = localDate.withMonth(12).withDayOfMonth(31);

        // 设置时间为23:59:59
        LocalTime endOfDay = LocalTime.MAX;

        // 合并日期和时间
        LocalDateTime endOfYearDateTime = LocalDateTime.of(endOfYear, endOfDay);

        // 转换回Date
        return Date.from(endOfYearDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取给定日期所在月份的最后一天的23点59分59秒的Date对象。
     *
     * @param date) 给定的日期
     * @return 当月最后一天23点59分59秒的Date对象
     */
    public static Date getLastDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 获取当前时间处于今天第几个15分钟
     *
     * @param timestamp 时间戳
     * @return 今天第几个15分钟
     */
    public static int getTimeSlot(long timestamp) {
        //正常应该是00点00是第0个点 但是这个系统是00点15是第0个点 所以往后蹿一个点 要减去15分钟
        Instant instant = Instant.ofEpochMilli(timestamp - 900000);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, java.time.ZoneId.systemDefault());
        LocalDateTime startOfDay = dateTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
        long millisSinceStartOfDay =
                dateTime.toInstant(java.time.ZoneOffset.UTC).toEpochMilli() - startOfDay.toInstant(java.time.ZoneOffset.UTC)
                        .toEpochMilli();

        return (int) (millisSinceStartOfDay / (15 * 60 * 1000));
    }

    /**
     * 获取指定日期的96个时点的集合
     *
     * @param d          指定日期
     * @param startPoint 从第几个时点开始（eg：从00:00开始为0，从00:15开始为1）
     * @return java.util.List<java.util.Date>
     **/
    public static List<Date> get96TimePoints(Date d, Integer startPoint) {
        List<Date> timePoints = CollUtil.newArrayList();
        Date beginTime = DateUtil.beginOfDay(d);
        if (ObjectUtil.isNotNull(startPoint) && startPoint > 0) {
            beginTime = DateUtil.offsetMinute(beginTime, 15 * startPoint);
        }
        timePoints.add(beginTime);
        for (int i = 1; i < 96; i++) {
            timePoints.add(DateUtil.offsetMinute(beginTime, 15 * i));
        }
        return timePoints;
    }

    /**
     * 获取指定日期的96个时点的集合
     *
     * @param d 指定日期
     * @return java.util.List<java.util.Date>
     **/
    public static List<Date> get96TimePoints(Date d) {
        return get96TimePoints(d, 0);
    }


    /**
     * 替换第二天00点时间字符串为前一天的24：00
     *
     * @param yestarDayZeroPoint
     * @return
     */
    public static String getCurent24HourPoint(String yestarDayZeroPoint) {
        if (yestarDayZeroPoint.contains("00:00")) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(DateUtil.date(DateUtil.parse(yestarDayZeroPoint)));

            calendar.add(Calendar.DAY_OF_MONTH, -1);
            return DateUtil.format(calendar.getTime(), "yyyy-MM-dd 24:00");
        } else {
            return yestarDayZeroPoint;
        }
    }

    /**
     * 加15分钟
     * 替换第二天00点时间字符串为前一天的24：00
     *
     * @param yestarDayZeroPoint
     * @return
     */
    public static String getAdd15MinPoint(String yestarDayZeroPoint) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtil.date(DateUtil.parse(yestarDayZeroPoint)));

        calendar.add(Calendar.MINUTE, 15);
        String timeStr = DateUtil.format(calendar.getTime(), "yyyy-MM-dd HH:mm");
        if (timeStr.contains("00:00")) {
            calendar.setTime(DateUtil.date(DateUtil.parse(timeStr)));

            calendar.add(Calendar.DAY_OF_MONTH, -1);
            timeStr = DateUtil.format(calendar.getTime(), "yyyy-MM-dd 24:00");
        }
        return timeStr;

    }

    public static Timestamp addTimestamp15Minutes(Timestamp orgTimestamp) {
        return new Timestamp(orgTimestamp.getTime() + 900000);
    }

    public static Timestamp minusTimestamp15Minutes(Timestamp orgTimestamp) {
        return new Timestamp(orgTimestamp.getTime() - 900000);
    }

    public static Timestamp changeTimestamp00To24Hour(Timestamp orgTimestamp) {
        String yestarDayZeroPoint = DateUtil.format(DateUtil.date(orgTimestamp.getTime()), "yyyy-MM-dd HH:mm");
        return new Timestamp(DateUtil.parse(getCurent24HourPoint(yestarDayZeroPoint)).getTime());
    }

    /**
     * 根据传入的时间  获取当月1号0点15到下月1号0点14分59 两个时间戳
     *
     * @param monthStr "2025-01"
     * @return Long[]{当月1号0点15,下月1号0点14分59}
     */
    public static Long[] getMonthDateByDate(String monthStr) {

        // 解析年月字符串为 YearMonth 对象
        YearMonth yearMonth = YearMonth.parse(monthStr, DateTimeFormatter.ofPattern("yyyy-MM"));

        // 获取当月第一天 00:15 的时间
        LocalDateTime startDateTime = yearMonth
                .atDay(1)                         // 当月第一天
                .atTime(0, 15);                   // 00:15

        // 获取下个月的第一天 00:15 的时间
        LocalDateTime endDateTime = yearMonth
                .plusMonths(1)                    // 下个月
                .atDay(1)                         // 第一天
                .atTime(0, 15);                   // 00:15

        // 转换为时间戳（毫秒，使用系统默认时区）
        long startTimestamp = startDateTime
                .atZone(ZoneId.systemDefault())   // 指定时区
                .toInstant()
                .toEpochMilli();

        long endTimestamp = endDateTime
                .atZone(ZoneId.systemDefault())    // 指定时区
                .toInstant()
                .toEpochMilli();
        return new Long[]{startTimestamp, endTimestamp - 1000L};
    }

    /**
     * 根据传入的时间  获取当年1月1号0点15到下一年1月1号0点14分59 两个时间戳
     *
     * @param yearStr "2025"
     * @return Long[]{当年1月1号0点15,下一年1月1号0点14分59}
     */
    public static Long[] getYearDateByDate(String yearStr) {
        // 解析年份
        int year = Integer.parseInt(yearStr);

        // 创建当年 1 月 1 号 0 点 15 的 LocalDateTime 对象
        LocalDateTime startDateTime = LocalDateTime.of(year, 1, 1, 0, 15);
        // 创建下一年 1 月 1 号 0 点 15 的 LocalDateTime 对象
        LocalDateTime endDateTime = LocalDateTime.of(year + 1, 1, 1, 0, 15);

        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 将 LocalDateTime 对象转换为 ZonedDateTime 对象
        ZonedDateTime startZonedDateTime = startDateTime.atZone(zoneId);
        ZonedDateTime endZonedDateTime = endDateTime.atZone(zoneId);

        // 获取时间戳
        long startTimestamp = startZonedDateTime.toInstant().toEpochMilli();
        long endTimestamp = endZonedDateTime.toInstant().toEpochMilli();

        return new Long[]{startTimestamp, endTimestamp - 1000L};
    }
}
