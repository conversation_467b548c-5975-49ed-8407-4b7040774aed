package cn.iocoder.yudao.module.et.controller.admin.contractsequipment.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 合同机组信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContractsEquipmentPageReqVO extends PageParam {

    @Schema(description = "合同角色", example = "王五")
    private String contractsRoleName;

    @Schema(description = "机组名称", example = "张三")
    private String equipmentName;

    @Schema(description = "机组类型", example = "2")
    private String equipmentType;

    @Schema(description = "装机容量")
    private BigDecimal quantity;

    @Schema(description = "批复电价", example = "580")
    private BigDecimal approvedPrice;

    @Schema(description = "机组电量")
    private BigDecimal equipmentGen;

    @Schema(description = "时段名称", example = "王五")
    private String timeSlotName;

    @Schema(description = "时段起止时间")
    private String timeSlotRange;

    @Schema(description = "部门ID", example = "5428")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] gatherDate;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] modificationTime;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] beginTime;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endTime;

    @Schema(description = "合同id", example = "1244")
    private String contractsId;

}