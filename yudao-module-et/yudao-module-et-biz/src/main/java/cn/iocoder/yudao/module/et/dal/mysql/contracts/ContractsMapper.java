package cn.iocoder.yudao.module.et.dal.mysql.contracts;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.contracts.vo.ContractsPageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contracts.ContractsDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 合同基本信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("shardingsphereDB")
public interface ContractsMapper extends BaseMapperX<ContractsDO> {

    default PageResult<ContractsDO> selectPage(ContractsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContractsDO>()
                .likeIfPresent(ContractsDO::getName, reqVO.getName())
                .eqIfPresent(ContractsDO::getContractsTypeId, reqVO.getContractsTypeId())
                .likeIfPresent(ContractsDO::getBeginTime, DateUtil.format(reqVO.getYear(), "yyyy"))
                .inIfPresent(ContractsDO::getSeriesContractsId, reqVO.getSeriesContractsId())
                .eqIfPresent(ContractsDO::getContractsCode, reqVO.getContractsCode())
                .eqIfPresent(ContractsDO::getTradingCode, reqVO.getTradingCode())
                .likeIfPresent(ContractsDO::getSigningDate, DateUtil.format(reqVO.getSigningDate(), "yyyy-MM-dd"))
                .eqIfPresent(ContractsDO::getRecordStatus, reqVO.getRecordStatus())
                .eqIfPresent(ContractsDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(ContractsDO::getHistoryContract, reqVO.getHistoryContracts())
                .apply(reqVO.getYearMonth() != null,
                        "DATE_FORMAT(signing_date, '%Y-%m') = '" +
                                (reqVO.getYearMonth() != null ? reqVO.getYearMonth().trim() : "") + "'")
                .apply(reqVO.getIds() != null && reqVO.getIds().length > 0,
                        buildInCondition("org_code", reqVO.getIds()))
                .apply(reqVO.getIds() == null || reqVO.getIds().length == 0,
                        "1 = 0")
                .orderByDesc(ContractsDO::getId));

    }

    default String buildInCondition(String column, String[] values) {
        StringBuilder sb = new StringBuilder();
        sb.append(column).append(" IN (");
        for (int i = 0; i < values.length; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append("'").append(values[i]).append("'");
        }
        sb.append(")");
        return sb.toString();
    }

    @Select({
            "<script>",
            "SELECT",
            "    DATE_FORMAT(signing_date, '%Y-%m') AS month,",
            "    org_code AS unit,",
            "    SUM(a.quantity) AS totalQuantity,",
            "    SUM(a.quantity * price) AS totalPrice,",
            "    AVG(price)  AS avgPrice",
            "FROM",
            "    jy_contracts a",
            "WHERE",
            "    a.deleted = 0",
            "  AND signing_status = '已签订' and contracts_type_id = #{typeId}",
            "  <if test='month != null and month != \"\"'>",
            "    AND DATE_FORMAT(signing_date, '%Y-%m') =  #{month}",
            "  </if>",
            "  <if test='ids != null and ids.size > 0'>",
            "    AND org_code in",
            "    <foreach collection='ids' item='item' open='(' separator=',' close=')'>",
            "      #{item}",
            "    </foreach>",
            "  </if>",
            "GROUP BY",
            "    DATE_FORMAT(signing_date, '%Y-%m'),",
            "    org_code",
            "ORDER BY",
            "    month,org_code",
            "  <if test='month == null or month == \"\"'>",
            "   limit 0,300",
            "  </if>",
            "</script>"
    })
    List<ContractsDO> selectListByTypeId(@Param("ids") List<String> ids, @Param("typeId") Long typeId, @Param("month") String month);


    @TenantIgnore
    @Select("select a.org_code orgCode, b.run_date runDate, group_concat(distinct b.contracts_id)ids, group_concat(distinct b.row_no) rowNos\n" +
            "from jy_contracts a\n" +
            "         left join jy_contracts_power_curve b on a.id = b.contracts_id\n" +
            "where  history_contract = '0'\n" +
            "  and date_format(b.run_date, '%Y-%m-%d') >= date_format(now(), '%Y-%m-%d')\n" +
            "  and date_format(b.run_date, '%Y-%m-%d') <= date_format(date_add(now(), interval 7 day), '%Y-%m-%d')\n" +
            "GROUP BY a.org_code , b.run_date")
    List<Map> selectContractsIdsAndRowNos();

}