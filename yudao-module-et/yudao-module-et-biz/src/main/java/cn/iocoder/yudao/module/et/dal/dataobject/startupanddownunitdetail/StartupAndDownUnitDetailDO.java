package cn.iocoder.yudao.module.et.dal.dataobject.startupanddownunitdetail;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 必开必停机组明细信息 DO
 *
 * <AUTHOR>
 */
@TableName("jy_startup_and_down_unit_detail")
@KeySequence("jy_startup_and_down_unit_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class StartupAndDownUnitDetailDO {
    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 父ID
     */
    private Long pid;
    /**
     * 必开 1 必停 0
     */
    private String type;
    /**
     * 业务时间
     */
    private LocalDate bizDate;
    /**
     * 电厂名称
     */
    private String stationName;
    /**
     * 机组台数
     */
    private Integer units;
    /**
     * 电压等级(kV)
     */
    private Integer voltageLevel;
    /**
     * 原因
     */
    private String cause;
    /**
     * 开始时间
     */
    private LocalDate dateBeign;
    /**
     * 结束时间
     */
    private LocalDate dateEnd;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherDate;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;

}
