package cn.iocoder.yudao.module.et.controller.admin.districtarea.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 省份对应算法类型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DistrictAreaPageReqVO extends PageParam {

    @Schema(description = "区域代码，甘肃-河东、甘肃-河西、内蒙古-蒙东")
    private String code;

    @Schema(description = "区域简称", example = "张三")
    private String name;

    @Schema(description = "区域全称", example = "赵六")
    private String fullName;

    @Schema(description = "所属行政区代码")
    private String districtCode;

    @Schema(description = "算法，normal：正位预测算法；cross：错位预测算法")
    private String predictionModel;

}