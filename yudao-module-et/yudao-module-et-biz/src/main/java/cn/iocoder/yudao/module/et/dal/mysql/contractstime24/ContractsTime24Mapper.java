package cn.iocoder.yudao.module.et.dal.mysql.contractstime24;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.et.controller.admin.contractstime24.vo.ContractsTime24PageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstime24.ContractsTime24DO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 合同分时段信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("shardingsphereDB")
public interface ContractsTime24Mapper extends BaseMapperX<ContractsTime24DO> {

    default PageResult<ContractsTime24DO> selectPage(ContractsTime24PageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContractsTime24DO>()
                .eqIfPresent(ContractsTime24DO::getTimeSlotCoding, reqVO.getTimeSlotCoding())
                .likeIfPresent(ContractsTime24DO::getTimeSlotName, reqVO.getTimeSlotName())
                .eqIfPresent(ContractsTime24DO::getPurchaserQuantity, reqVO.getPurchaserQuantity())
                .eqIfPresent(ContractsTime24DO::getSellerQuantity, reqVO.getSellerQuantity())
                .eqIfPresent(ContractsTime24DO::getPurchaserPrice, reqVO.getPurchaserPrice())
                .eqIfPresent(ContractsTime24DO::getSellerPrice, reqVO.getSellerPrice())
                .eqIfPresent(ContractsTime24DO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(ContractsTime24DO::getOrgCode, reqVO.getOrgCode())
                .betweenIfPresent(ContractsTime24DO::getGatherDate, reqVO.getGatherDate())
                .betweenIfPresent(ContractsTime24DO::getModificationTime, reqVO.getModificationTime())
                .eqIfPresent(ContractsTime24DO::getTimeSlotsRange, reqVO.getTimeSlotsRange())
                .eqIfPresent(ContractsTime24DO::getTimeSlots, reqVO.getTimeSlots())
                .betweenIfPresent(ContractsTime24DO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(ContractsTime24DO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(ContractsTime24DO::getContractsId, reqVO.getContractsId())
                .orderByDesc(ContractsTime24DO::getId));
    }

    @Select("select * from jy_contracts_time24 where contracts_id = #{id} and deleted = 0")
    List<ContractsTime24DO> selectListByContractsId(String id);
}