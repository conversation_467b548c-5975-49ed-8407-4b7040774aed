package cn.iocoder.yudao.module.et.service.dataproc.intra;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO;
import cn.iocoder.yudao.module.et.dal.tdengine.dataproc.PrivateDataProcMapper;
import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.iocoder.yudao.module.et.util.JyDateTimeUtil;
import cn.jy.soft.remote.bean.SocketPacket;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * GS-B-251 市场出清监视
 * 每小时爬取，更新D-1至D+8共计10天数据
 *
 * <AUTHOR>
 * @date 2024-11-11
 **/
@Slf4j
@Service
@AllArgsConstructor
public class ClearingMonitoringProc implements BaseDataProc {

    private final PrivateDataProcMapper privateDataProcMapper;

    @Override
    public boolean execute(SocketPacket dto) {
        long runtime = System.currentTimeMillis();
        try {
            TenantUtils.execute(dto.getTenantId(), () -> {
                JSONObject jsonObject = JSONUtil.parseObj(dto.getData());
                List<TradingUnitDO> tradingUnitDOList = CollUtil.newArrayList();
                List<CsvRow> rowList;
                TradingUnitDO entity;
                List<Date> timePoints;
                for (String key : jsonObject.keySet()) {
                    timePoints = JyDateTimeUtil.get96TimePoints(DateUtil.parseDate(key), 1);
                    rowList = CsvUtil.getReader(new CsvReadConfig().setBeginLineNo(0).setTrimField(true))
                            .readFromStr(jsonObject.getStr(key)).getRows();
                    int step = 1;
                    if (rowList.size() > 8) {
                        for (int i = 0; i < timePoints.size(); i++) {
                            entity = new TradingUnitDO();
                            // 时间
                            entity.setTs(Timestamp.valueOf(DateUtil.formatDateTime(timePoints.get(i))));
                            // 中长期结算曲线
                            if (StrUtil.isNotBlank(rowList.get(1).get(i + step))) {
                                entity.setMediumLongTermSettlementCurves(Float.parseFloat(rowList.get(1).get(i + step)));
                            }
                            // 自计划
                            if (StrUtil.isNotBlank(rowList.get(2).get(i + step))) {
                                entity.setSelfPlan(Float.parseFloat(rowList.get(2).get(i + step)));
                            }
                            // 可靠性计划
                            if (StrUtil.isNotBlank(rowList.get(3).get(i + step))) {
                                entity.setPrebalancePlan(Float.parseFloat(rowList.get(3).get(i + step)));
                            }
                            // 预平衡计划
                            if (StrUtil.isNotBlank(rowList.get(4).get(i + step))) {
                                entity.setReliabilityPlan(Float.parseFloat(rowList.get(4).get(i + step)));
                            }
                            // 双边出清
                            if (StrUtil.isNotBlank(rowList.get(5).get(i + step))) {
                                entity.setBilateralClearance(Float.parseFloat(rowList.get(5).get(i + step)));
                            }
                            // 实时计划
                            if (StrUtil.isNotBlank(rowList.get(6).get(i + step))) {
                                entity.setRealPlan(Float.parseFloat(rowList.get(6).get(i + step)));
                            }
                            // 实时上网scada电力
                            if (StrUtil.isNotBlank(rowList.get(7).get(i + step))) {
                                entity.setRealScadaPower(Float.parseFloat(rowList.get(7).get(i + step)));
                            }
                            // 短期预测
                            if (StrUtil.isNotBlank(rowList.get(8).get(i + step))) {
                                entity.setShortTermForecast(Float.parseFloat(rowList.get(8).get(i + step)));
                            }
                            // 超短期预测
                            if (StrUtil.isNotBlank(rowList.get(9).get(i + step))) {
                                entity.setUltraShortTermForecast(Float.parseFloat(rowList.get(9).get(i + step)));
                            }
                            // 跨省区现货出清电力
                            if (StrUtil.isNotBlank(rowList.get(10).get(i + step))) {
                                entity.setCrossProvincialSpotClearingOfElectricity(
                                        Float.parseFloat(rowList.get(10).get(i + step)));
                            }
                            // 实时出清依据
                            if (StrUtil.isNotBlank(rowList.get(11).get(i + step))) {
                                entity.setRealBasisClearance(Float.parseFloat(rowList.get(11).get(i + step)));
                            }
                            tradingUnitDOList.add(entity);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(tradingUnitDOList)) {
                    privateDataProcMapper.insertClearingMonitoring(dto.getOrgCode(), tradingUnitDOList);
                    log.info("[{}] > 解析入库完成，共 {} 条，用时 {} ms", dto.getBizCode(), tradingUnitDOList.size(),
                            System.currentTimeMillis() - runtime);
                }
            });
        } catch (Exception ex) {
            log.error(StrUtil.format("[{}] > 解析出现异常", dto.getBizCode()), ex);
            return false;
        }
        return true;
    }
}
