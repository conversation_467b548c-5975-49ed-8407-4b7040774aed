package cn.iocoder.yudao.module.et.dal.dataobject.tradingunit;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.sql.Timestamp;

/**
 * 交易单元 DO
 *
 * <AUTHOR>
 */
@TableName("trading_unit")
@KeySequence("trading_unit_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradingUnitDO {

    /**
     * 时间
     */
    private Timestamp ts;

    /**
     * 场站id
     */
    private String unitId;
    /**
     * 电厂侧日前市场化结算价格限制
     */
    private Float hisSettlementPricesLimit;

    /**
     * 电厂侧实时结算价格
     */
    private Float realSettlementPrices;

    /**
     * 中长期结算曲线
     */
    private Float mediumLongTermSettlementCurves;

    /**
     * 日前出清电量详情
     */
//    private Float dayAheadTotalPowerQuantity;
    /**
     * 自计划
     */
    private Float selfPlan;
    /**
     * 预平衡计划
     */
    private Float prebalancePlan;
    /**
     * 可靠性计划
     */
    private Float reliabilityPlan;
    /**
     * 双边出清
     */
    private Float bilateralClearance;
    /**
     * 实时计划
     */
    private Float realPlan;
    /**
     * 实时上网scada电力
     */
    private Float realScadaPower;
    /**
     * 短期预测
     */
    private Float shortTermForecast;
    /**
     * 超短期预测
     */
    private Float ultraShortTermForecast;
    /**
     * 跨省区现货出清电力
     */
    private Float crossProvincialSpotClearingOfElectricity;
    /**
     * 实时出清依据
     */
    private Float realBasisClearance;
    /**
     * 日前正现货电量(MW.h)
     */
    private Float hisPositivePower;
    /**
     * 日前负现货电量(MW.h)
     */
    private Float hisNegativePower;
    /**
     * 日前正现货费用(元)
     */
    private Float hisPositivePrices;
    /**
     * 日前负现货费用(元)
     */
    private Float hisNegativePrices;
    /**
     * 日前发电计划(MW)
     */
    private Float hisGenerateElectricityPlan;
    /**
     * 中长期计划（上网）(MW)
     */
    private Float mediumLongTermPlan;
    /**
     * 日前省间出清(MW)
     */
    private Float hisProvinceClearance;
    /**
     * 日前市场结算价格(元/MW.h)
     */
    private Float hisMarketSettlementPrices;
    /**
     * 实时市场结算价格(元/MW.h)
     */
    private Float realMarketSettlementPrices;
    /**
     * 实时正现货电量(MW.h)
     */
    private Float realPositivePower;
    /**
     * 实时负现货电量(MW.h)
     */
    private Float realNegativePower;
    /**
     * 实时正现货费用(元)
     */
    private Float realPositivePrices;
    /**
     * 实时负现货费用(元)
     */
    private Float realNegativePrices;
    /**
     * 实时省间出清(MW)
     */
    private Float realProvinceClearance;
    /**
     * 跨省调峰计划(MW)
     */
    private Float crossProvincialPeakLoadBalancingPlan;
    /**
     * 跨省调峰返还(MW)
     */
    private Float crossProvincialPeakLoadBalancingReturn;
    /**
     * 实际结算电量(MW.h)
     */
    private Float realSettlementPower;
    /**
     * 紧急调用开机补偿费用(元)
     */
    private Float emergencyInvokeStartUpCompensationPrices;
    /**
     * 火电开机补偿费用(元)
     */
    private Float thermalPowerStartUpCompensationPrices;
    /**
     * 偏差考核费用(元)
     */
    private Float deviationReviewPrices;
    /**
     * 新能源增发超额获利回收费用
     */
    private Float recoveryOfExcessProfitsFromNewEnergyIssuancePrices;
    /**
     * 日前备用出清(MW)
     */
    private Float hisStandbyClearance;
    /**
     * 实时备用出清(MW)
     */
    private Float realStandbyClearance;
    /**
     * 中长期阻塞费用(元)
     */
    private Float mediumLongTermBlockagePrices;
    /**
     * 阻塞风险对冲费用(元)
     */
    private Float blockingRiskHedgingPrices;
    /**
     * 修正补偿费用(元)
     */
    private Float modifiedCompensationPrices;
    /**
     * 必开机组补偿费用(元)
     */
    private Float necessaryStartUpCompensationPrices;
    /**
     * 叠加后中长期(MW.h)
     */
    private Float mediumLongTermFusion;
    /**
     * 日前免结算电量(MW.h)
     */
    private Float hisSettlementFreePower;
    /**
     * 实时免结算电量(MW.h)
     */
    private Float realSettlementFreePower;
    /**
     * 酒钢开口合同(MW)
     */
    private Float jiscoBpo;
    /**
     * 酒钢双边交易(MW)
     */
    private Float jiscoBilateralTransactions;
    /**
     * 考核电量(MW.h)
     */
    private Float reviewPower;
    /**
     * 紧急调用开机上网电量(MW.h)
     */
    private Float fmergencyInvokeStartUpOnGridPower;
    /**
     * 调频增发能量补偿(元)
     */
    private Float adjustmentFrequencyAdditionEnergyCompensation;
    /**
     * 调频减发能量补偿(元)
     */
    private Float adjustmentFrequencyLessenEnergyCompensation;
    /**
     * 非计划停运考核(元)
     */
    private Float unplannedOutageAssessment;
    /**
     * 是否允许替换
     */
    private Float whetherSubstitutionIsAllowed;
    /**
     * 偏差率
     */
    private Float deviationRate;
    /**
     * TMR电量
     */
    private Float tmrPower;
    /**
     * SCADA电量
     */
    private Float scadaPower;
    /**
     * 新能源实时消纳电量
     */
    private Float realUsePower;
    /**
     * 校正电量
     */
    private Float calibrationPower;
    /**
     * 结算电量
     */
    private Float settlementPower;
    /**
     * 基数电量
     */
    private Float radixPower;
    /**
     * 外送电量
     */
    private Float deliveryPower;
    /**
     * 双边电量
     */
    private Float bilateralPower;
    /**
     * 日滚动电量
     */
    private Float dayScrollPower;
    /**
     * 中长期合同电量
     */
    private Float longTermContractPower;
    /**
     * 基数电价
     */
    private Float radixPrice;
    /**
     * 外送电价
     */
    private Float deliveryPrice;
    /**
     * 双边电价
     */
    private Float bilateralPrice;
    /**
     * 日滚动电价
     */
    private Float dayScrollPrice;
    /**
     * 中长期合同电价
     */
    private Float longTermContractPrice;
    /**
     * 短期发电量预测
     */
    private Float shortTermForecastGen;
    /**
     * 当前装机容量
     */
    private Float capacity;

    private String getValueOrDefault(Float value) {
        return value == null ? "--" : value.toString();
    }

    public String getCap() {
        return getValueOrDefault(capacity);
    }

    public String getSelfPlan() {
        return getValueOrDefault(selfPlan);
    }

    public String getPrebalancePlan() {
        return getValueOrDefault(prebalancePlan);
    }

    public String getReliabilityPlan() {
        return getValueOrDefault(reliabilityPlan);
    }

    public String getBilateralClearance() {
        return getValueOrDefault(bilateralClearance);
    }

    public String getRealPlan() {
        return getValueOrDefault(realPlan);
    }

    public String getRealScadaPower() {
        return getValueOrDefault(realScadaPower);
    }

    public String getShortTermForecast() {
        return getValueOrDefault(shortTermForecast);
    }

    public String getUltraShortTermForecast() {
        return getValueOrDefault(ultraShortTermForecast);
    }

    public String getCrossProvincialSpotClearingOfElectricity() {
        return getValueOrDefault(crossProvincialSpotClearingOfElectricity);
    }

    public String getRealBasisClearance() {
        return getValueOrDefault(realBasisClearance);
    }

    public String getHisPositivePower() {
        return getValueOrDefault(hisPositivePower);
    }

    public String getHisNegativePower() {
        return getValueOrDefault(hisNegativePower);
    }

    public String getHisPositivePrices() {
        return getValueOrDefault(hisPositivePrices);
    }

    public String getHisNegativePrices() {
        return getValueOrDefault(hisNegativePrices);
    }

    public String getHisGenerateElectricityPlan() {
        return getValueOrDefault(hisGenerateElectricityPlan);
    }

    public String getMediumLongTermPlan() {
        return getValueOrDefault(mediumLongTermPlan);
    }

    public String getHisProvinceClearance() {
        return getValueOrDefault(hisProvinceClearance);
    }

    public String getRealMarketSettlementPrices() {
        return getValueOrDefault(realMarketSettlementPrices);
    }

    public String getRealPositivePower() {
        return getValueOrDefault(realPositivePower);
    }

    public String getRealNegativePower() {
        return getValueOrDefault(realNegativePower);
    }

    public String getRealPositivePrices() {
        return getValueOrDefault(realPositivePrices);
    }

    public String getRealNegativePrices() {
        return getValueOrDefault(realNegativePrices);
    }

    public String getRealProvinceClearance() {
        return getValueOrDefault(realProvinceClearance);
    }

    public String getCrossProvincialPeakLoadBalancingPlan() {
        return getValueOrDefault(crossProvincialPeakLoadBalancingPlan);
    }

    public String getCrossProvincialPeakLoadBalancingReturn() {
        return getValueOrDefault(crossProvincialPeakLoadBalancingReturn);
    }

    public String getRealSettlementPower() {
        return getValueOrDefault(realSettlementPower);
    }

    public String getEmergencyInvokeStartUpCompensationPrices() {
        return getValueOrDefault(emergencyInvokeStartUpCompensationPrices);
    }

    public String getThermalPowerStartUpCompensationPrices() {
        return getValueOrDefault(thermalPowerStartUpCompensationPrices);
    }

    public String getDeviationReviewPrices() {
        return getValueOrDefault(deviationReviewPrices);
    }

    public String getRecoveryOfExcessProfitsFromNewEnergyIssuancePrices() {
        return getValueOrDefault(recoveryOfExcessProfitsFromNewEnergyIssuancePrices);
    }

    public String getHisStandbyClearance() {
        return getValueOrDefault(hisStandbyClearance);
    }

    public String getRealStandbyClearance() {
        return getValueOrDefault(realStandbyClearance);
    }

    public String getMediumLongTermBlockagePrices() {
        return getValueOrDefault(mediumLongTermBlockagePrices);
    }

    public String getBlockingRiskHedgingPrices() {
        return getValueOrDefault(blockingRiskHedgingPrices);
    }

    public String getModifiedCompensationPrices() {
        return getValueOrDefault(modifiedCompensationPrices);
    }

    public String getNecessaryStartUpCompensationPrices() {
        return getValueOrDefault(necessaryStartUpCompensationPrices);
    }

    public String getMediumLongTermFusion() {
        return getValueOrDefault(mediumLongTermFusion);
    }

    public String getHisSettlementFreePower() {
        return getValueOrDefault(hisSettlementFreePower);
    }

    public String getRealSettlementFreePower() {
        return getValueOrDefault(realSettlementFreePower);
    }

    public String getJiscoBpo() {
        return getValueOrDefault(jiscoBpo);
    }

    public String getJiscoBilateralTransactions() {
        return getValueOrDefault(jiscoBilateralTransactions);
    }

    public String getReviewPower() {
        return getValueOrDefault(reviewPower);
    }

    public String getFmergencyInvokeStartUpOnGridPower() {
        return getValueOrDefault(fmergencyInvokeStartUpOnGridPower);
    }

    public String getAdjustmentFrequencyAdditionEnergyCompensation() {
        return getValueOrDefault(adjustmentFrequencyAdditionEnergyCompensation);
    }

    public String getAdjustmentFrequencyLessenEnergyCompensation() {
        return getValueOrDefault(adjustmentFrequencyLessenEnergyCompensation);
    }

    public String getUnplannedOutageAssessment() {
        return getValueOrDefault(unplannedOutageAssessment);
    }

    public String getWhetherSubstitutionIsAllowed() {
        return getValueOrDefault(whetherSubstitutionIsAllowed);
    }

    public String getDeviationRate() {
        return getValueOrDefault(deviationRate);
    }

    public String getRadixPower() {
        return getValueOrDefault(radixPower);
    }

    public String getDeliveryPower() {
        return getValueOrDefault(deliveryPower);
    }

    public String getBilateralPower() {
        return getValueOrDefault(bilateralPower);
    }

    public String getDayScrollPower() {
        return getValueOrDefault(dayScrollPower);
    }

    public String getLongTermContractPower() {
        return getValueOrDefault(longTermContractPower);
    }

    public String getradixPrice() {
        return getValueOrDefault(radixPrice);
    }

    public String getDeliveryPrice() {
        return getValueOrDefault(deliveryPrice);
    }

    public String getBilateralPrice() {
        return getValueOrDefault(bilateralPrice);
    }

    public String getDayScrollPrice() {
        return getValueOrDefault(dayScrollPrice);
    }

    public String getLongTermContractPrice() {
        return getValueOrDefault(longTermContractPrice);
    }

    public String getShortTermForecastGen() {
        return getValueOrDefault(shortTermForecastGen);
    }

    public String getMediumLongTermSettlementCurves() {
        return getValueOrDefault(mediumLongTermSettlementCurves);
    }

    public String getHisSettlementPricesLimit() {
        return getValueOrDefault(hisSettlementPricesLimit);
    }

    public String getHisMarketSettlementPrices() {
        return getValueOrDefault(hisMarketSettlementPrices);
    }

    public String getRealSettlementPrices() {
        return getValueOrDefault(realSettlementPrices);
    }

    public String getTmrPower() {
        return getValueOrDefault(tmrPower);
    }

    public String getScadaPower() {
        return getValueOrDefault(scadaPower);
    }

    public String getRealUsePower() {
        return getValueOrDefault(realUsePower);
    }

    public String getCalibrationPower() {
        return getValueOrDefault(calibrationPower);
    }

    public String getSettlementPower() {
        return getValueOrDefault(settlementPower);
    }

    public String getTmrPowerStr() {
        return getValueOrDefault(tmrPower);
    }

    public String getScadaPowerStr() {
        return getValueOrDefault(scadaPower);
    }

    public String getRealUsePowerStr() {
        return getValueOrDefault(realUsePower);
    }

    public String getCalibrationPowerStr() {
        return getValueOrDefault(calibrationPower);
    }

    public String getSettlementPowerStr() {
        return getValueOrDefault(settlementPower);
    }

    // 日前电费
    public float getHisPrices() {
        return getValueOrFloat(hisPositivePrices) + getValueOrFloat(hisNegativePrices);
    }

    // 实时电费
    public float getRealPrices() {
        return getValueOrFloat(realPositivePrices) + getValueOrFloat(realNegativePrices);
    }

    // 中长期电费
    public float getLongPrices() {
        return getValueOrFloat(longTermContractPower) * getValueOrFloat(longTermContractPrice);
    }

    private float getValueOrFloat(Float value) {
        return value == null ? 0f : value;
    }
}