package cn.iocoder.yudao.module.et.controller.admin.dispatchnode;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.dispatchnode.vo.DispatchNodePageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.dispatchnode.vo.DispatchNodeRespVO;
import cn.iocoder.yudao.module.et.controller.admin.dispatchnode.vo.DispatchNodeSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.DispatchNodeDO;
import cn.iocoder.yudao.module.et.service.dispatchnode.DispatchNodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 交易单元")
@RestController
@RequestMapping("/jy/dispatch-node")
@Validated
public class DispatchNodeController {

    @Resource
    private DispatchNodeService dispatchNodeService;

    @PostMapping("/create")
    @Operation(summary = "创建交易单元")
    @PreAuthorize("@ss.hasPermission('jy:dispatch-node:create')")
    public CommonResult<Integer> createDispatchNode(@Valid @RequestBody DispatchNodeSaveReqVO createReqVO) {
        return success(dispatchNodeService.createDispatchNode(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新交易单元")
    @PreAuthorize("@ss.hasPermission('jy:dispatch-node:update')")
    public CommonResult<Boolean> updateDispatchNode(@Valid @RequestBody DispatchNodeSaveReqVO updateReqVO) {
        dispatchNodeService.updateDispatchNode(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除交易单元")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('jy:dispatch-node:delete')")
    public CommonResult<Boolean> deleteDispatchNode(@RequestParam("id") Integer id) {
        dispatchNodeService.deleteDispatchNode(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得交易单元")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('jy:dispatch-node:query')")
    public CommonResult<DispatchNodeRespVO> getDispatchNode(@RequestParam("id") Integer id) {
        DispatchNodeDO dispatchNode = dispatchNodeService.getDispatchNode(id);
        return success(BeanUtils.toBean(dispatchNode, DispatchNodeRespVO.class));
    }

    @GetMapping("/getNameByCode")
    @Operation(summary = "获得交易单元名称")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('jy:dispatch-node:query')")
    public CommonResult<String> getDispatchNode(@RequestParam("code") String code) {
        return success( dispatchNodeService.getNameByCode(code));
    }

    @GetMapping("/page")
    @Operation(summary = "获得交易单元分页")
    @PreAuthorize("@ss.hasPermission('jy:dispatch-node:query')")
    public CommonResult<PageResult<DispatchNodeRespVO>> getDispatchNodePage(@Valid DispatchNodePageReqVO pageReqVO) {
        PageResult<DispatchNodeDO> pageResult = dispatchNodeService.getDispatchNodePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DispatchNodeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出交易单元 Excel")
    @PreAuthorize("@ss.hasPermission('jy:dispatch-node:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDispatchNodeExcel(@Valid DispatchNodePageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DispatchNodeDO> list = dispatchNodeService.getDispatchNodePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "交易单元.xls", "数据", DispatchNodeRespVO.class,
                BeanUtils.toBean(list, DispatchNodeRespVO.class));
    }

}