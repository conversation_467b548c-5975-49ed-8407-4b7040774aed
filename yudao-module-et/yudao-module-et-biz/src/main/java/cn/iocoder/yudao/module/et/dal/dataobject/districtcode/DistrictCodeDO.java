package cn.iocoder.yudao.module.et.dal.dataobject.districtcode;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 行政区划 DO
 *
 * <AUTHOR>
 */
@TableName("jy_district_code")
@KeySequence("jy_district_code_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistrictCodeDO {

    /**
     * 行政区划代码
     */
    @TableId
    private Integer code;
    /**
     * 名字
     */
    private String name;
    /**
     * 等级:1-省级;2-地级市;3-区/县;4-乡/镇
     */
    private Integer level;
    /**
     * 类型:1-省;2-自治区;3-直辖市;4-特别行政区;5-地级市;6-地区;7-自治州;8-盟;9-市辖区;10-县;11- 县级市;12-自治县;13-旗;14-自治旗;15-特区;16-林区
     */
    private Integer type;
    /**
     * 简称
     */
    private String abname;
    /**
     * 所属行政区划代码
     */
    private Integer pid;
    /**
     * 所属行政区划名字
     */
    private String pname;
    /**
     * 备注
     */
    private String note;
    /**
     * 纬度
     */
    private Double lat;
    /**
     * 经度
     */
    private Double lng;

}