package cn.iocoder.yudao.module.et.controller.admin.tradingunit;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.et.controller.admin.tradingunit.vo.TradingUnitRespVO;
import cn.iocoder.yudao.module.et.service.tradingunit.TradingUnitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 交易单元")
@RestController
@RequestMapping("/et/tradingUnit")
@Validated
public class TradingUnitController {
    @Resource
    private TradingUnitService tradingUnitService;

    @PostMapping("/getRealTimePriceComparison")
    @Operation(summary = "现货价格-日前或实时价格")
    //@PreAuthorize("@ss.hasPermission('p:A-day-ahead-results:query')")
    public CommonResult<Map<String, Object>> getRealTimePriceComparison(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> tradingUnitDO = tradingUnitService.getRealTimePriceComparison(tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getMonthDate(), tradingUnitRespVO.getDate(), tradingUnitRespVO.getDataSelectTypeOption());
        return success(tradingUnitDO);
    }

    @PostMapping("/getRealTimePriceCompare")
    @Operation(summary = "现货价格-日前实时价格对比")
    //@PreAuthorize("@ss.hasPermission('p:A-day-ahead-results:query')")
    public CommonResult<Map<String, Object>> getRealTimePriceCompare(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> tradingUnitDO = tradingUnitService.getRealTimePriceCompare(tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getMonthDate(), tradingUnitRespVO.getDate());
        return success(tradingUnitDO);
    }

    /**
     * 交易复盘 预测偏差分析
     */
    @PostMapping("/getDeviation")
    @Operation(summary = "交易复盘 预测偏差分析")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, List<String>>> getDeviation(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, List<String>> resultMap = tradingUnitService.getDeviation(tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getDate());
        return success(resultMap);
    }

    /**
     * 交易复盘 交易概览
     */
    @PostMapping("/getTransactionOverview")
    @Operation(summary = "交易复盘 交易概览")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, Object>> getTransactionOverview(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> resultMap = tradingUnitService.getTransactionOverview(tradingUnitRespVO.getIds(), tradingUnitRespVO.getDate());
        return success(resultMap);
    }

    @PostMapping("/getPowerGeneration")
    @Operation(summary = "复盘分析-发电量分析")
    @PreAuthorize("@ss.hasPermission('et:tradingUnit_power_generation:query')")
    public CommonResult<Map<String, Object>> getPowerGeneration(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> tradingUnitDO = tradingUnitService.getPowerGeneration(tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getMonthDate(), tradingUnitRespVO.getDate());
        return success(tradingUnitDO);
    }

    @PostMapping("/getTradingCalendar")
    @Operation(summary = "交易日历")
    @PreAuthorize("@ss.hasPermission('et:tradingUnit_trading_calendar:query')")
    public CommonResult<Map<String, Object>> getTradingCalendar(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> tradingUnitDO = tradingUnitService.getTradingCalendar(tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getMonthDate());
        return success(tradingUnitDO);
    }

    @PostMapping("/getReviewAnalysis")
    @Operation(summary = "交易复盘")
    @PreAuthorize("@ss.hasPermission('et:tradingUnit_review_analysis:query')")
    public CommonResult<Map<String, Object>> getReviewAnalysis(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> tradingUnitDO = tradingUnitService.getReviewAnalysis(tradingUnitRespVO.getIds(), tradingUnitRespVO.getMonthDate(), tradingUnitRespVO.getDate());
        return success(tradingUnitDO);
    }

    @PostMapping("/getCurrentPrices")
    @Operation(summary = "日前价格")
    //@PreAuthorize("@ss.hasPermission('et:tradingUnit_review_analysis:query')")
    public CommonResult<List<List<Object>>> getCurrentPrices(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        List<List<Object>> tradingUnitDO = tradingUnitService.getCurrentPrices(tradingUnitRespVO.getIds(), tradingUnitRespVO.getDate(), tradingUnitRespVO.getDataSelectTypeOption());
        return success(tradingUnitDO);
    }

    @PostMapping("/getRemainingSpace")
    @Operation(summary = "剩余空间")
    //@PreAuthorize("@ss.hasPermission('et:tradingUnit_review_analysis:query')")
    public CommonResult<Map<String, List<Double>>> getRemainingSpace(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, List<Double>> tradingUnitDO = tradingUnitService.getRemainingSpace(tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getDate());
        return success(tradingUnitDO);
    }

    @PostMapping("/exportRemainingSpace")
    @Operation(summary = "个性化导出剩余空间")
    //@PreAuthorize("@ss.hasPermission('et:tradingUnit_review_analysis:query')")
    public void exportRemainingSpace(HttpServletResponse response, HttpServletRequest request, @RequestBody TradingUnitRespVO tradingUnitRespVO) {
        tradingUnitService.exportRemainingSpace(response, request, tradingUnitRespVO.getDate());
    }

    @PostMapping("/getSettlementRelease")
    @Operation(summary = "结算发布")
    @PreAuthorize("@ss.hasPermission('et:tradingUnit_settlement_release:query')")
    public CommonResult<Map<String, Object>> getSettlementRelease(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> tradingUnitDO = tradingUnitService.getSettlementRelease(tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getMonthDate());
        return success(tradingUnitDO);
    }

    @PostMapping("/getSettlementElectricity")
    @Operation(summary = "结算电量")
    @PreAuthorize("@ss.hasPermission('et:tradingUnit_settlement_electricity:query')")
    public CommonResult<Map<String, Object>> getSettlementElectricity(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> tradingUnitDO = tradingUnitService.getSettlementElectricity(tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getDate());
        return success(tradingUnitDO);
    }

    @PostMapping("/getSettlementReleaseSum")
    @Operation(summary = "结算电量")
    @PreAuthorize("@ss.hasPermission('et:tradingUnit_settlement_release_sum:query')")
    public CommonResult<Map<String, Object>> getSettlementReleaseSum(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> tradingUnitDO = tradingUnitService.getSettlementReleaseSum(tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getMonthDate(), tradingUnitRespVO.getDate());
        return success(tradingUnitDO);
    }

    @PostMapping("/getClearanceMonitor")
    @Operation(summary = "市场动态 市场出清监视")
    //@PreAuthorize("@ss.hasPermission('et:tradingUnit_settlement_release_sum:query')")
    public CommonResult<Map<String, Object>> getClearanceMonitor(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> tradingUnitDO = tradingUnitService.getClearanceMonitor(tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getDate());
        return success(tradingUnitDO);
    }

    @PostMapping("/getCongestionCost")
    @Operation(summary = "数据管理 阻塞对冲机制费用计算")
    //@PreAuthorize("@ss.hasPermission('et:tradingUnit_settlement_release_sum:query')")
    public CommonResult<Map<String, Object>> getCongestionCost(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> tradingUnitDO = tradingUnitService.getCongestionCost(tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getDate());
        return success(tradingUnitDO);
    }

    @PostMapping("/downloadExcelTemplate")
    @Operation(summary = "数据管理 阻塞对冲机制费用计算")
    //@PreAuthorize("@ss.hasPermission('et:tradingUnit_settlement_release_sum:query')")
    public void downloadExcelTemplate(HttpServletResponse response, HttpServletRequest request, @RequestBody TradingUnitRespVO tradingUnitRespVO) {
        tradingUnitService.downloadExcelTemplate(response, request, tradingUnitRespVO.getUnitId(), tradingUnitRespVO.getDate());
    }


}