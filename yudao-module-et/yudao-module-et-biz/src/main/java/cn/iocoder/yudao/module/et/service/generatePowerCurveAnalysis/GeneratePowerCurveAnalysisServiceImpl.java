package cn.iocoder.yudao.module.et.service.generatePowerCurveAnalysis;

import cn.iocoder.yudao.module.et.controller.admin.tradingunit.vo.TradingUnitRespVO;
import cn.iocoder.yudao.module.et.dal.dataobject.generatePowerCurveAnalysis.GeneratePowerCurveAnalysisDO;
import cn.iocoder.yudao.module.et.dal.tdengine.generatePowerCurveAnalysis.GeneratePowerCurveAnalysisMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Validated
@Slf4j
public class GeneratePowerCurveAnalysisServiceImpl implements GeneratePowerCurveAnalysisService {

    @Resource
    private GeneratePowerCurveAnalysisMapper generatePowerCurveAnalysisMapper;


    @Override
    public Map<String, Object> getGeneratePowerCurveAnalysis(TradingUnitRespVO tradingUnitRespVO) {
        Map<String, Object> result = new HashMap<>();
        log.info("getGeneratePowerCurveAnalysis:{}", tradingUnitRespVO);
        List<GeneratePowerCurveAnalysisDO> generatePowerCurveAnalysis = generatePowerCurveAnalysisMapper.getGeneratePowerCurveAnalysis(tradingUnitRespVO);
        if (generatePowerCurveAnalysis == null || generatePowerCurveAnalysis.isEmpty()) {
            result.put("avgRealScadaPower", Arrays.asList(0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f));
            result.put("avgShortTermForecast", Arrays.asList(0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f,0.00f));
            return result;
        }
        Map<String, List<Float>> realScadaPowerMap = new HashMap<>();
        Map<String, List<Float>> shortTermForecastMap = new HashMap<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm");
        for (GeneratePowerCurveAnalysisDO data : generatePowerCurveAnalysis) {
            // 按小时和分钟分组
            String timeKey = dateFormat.format(data.getTs());
            realScadaPowerMap.computeIfAbsent(timeKey, k -> new ArrayList<>()).add(
                    data.getRealScadaPower() != null ? data.getRealScadaPower() : 0.0f
            );
            shortTermForecastMap.computeIfAbsent(timeKey, k -> new ArrayList<>()).add(
                    data.getShortTermForecast() != null ? data.getShortTermForecast() : 0.0f
            );
        }
        log.info("realScadaPowerMap:{}", realScadaPowerMap.size());
        log.info("shortTermForecastMap:{}", shortTermForecastMap.size());
        double realScadaPowerSum = realScadaPowerMap.values().stream()
                .flatMap(List::stream)
                .mapToDouble(Float::doubleValue)
                .sum();
        double shortTermForecastSum = shortTermForecastMap.values().stream()
                .flatMap(List::stream)
                .mapToDouble(Float::doubleValue)
                .sum();
        log.info("realScadaPowerMap 总和: {}", String.format("%.2f", realScadaPowerSum));
        log.info("shortTermForecastMap 总和: {}", String.format("%.2f", shortTermForecastSum));
        // 获取所有的时间键，并按升序排序
        List<String> sortedTimeKeys = new ArrayList<>(realScadaPowerMap.keySet());
        Collections.sort(sortedTimeKeys);
        List<String> avgRealScadaPowerList = new ArrayList<>();
        List<String> avgShortTermForecastList = new ArrayList<>();
        for (String timeKey : sortedTimeKeys) {
            double avgRealScadaPower = realScadaPowerMap.get(timeKey).stream().mapToDouble(Float::doubleValue).average().orElse(0.0);
            double avgShortTermForecast = shortTermForecastMap.get(timeKey).stream().mapToDouble(Float::doubleValue).average().orElse(0.0);
            avgRealScadaPowerList.add(String.format("%.2f", avgRealScadaPower));
            avgShortTermForecastList.add(String.format("%.2f", avgShortTermForecast));
        }
        result.put("avgRealScadaPower", avgRealScadaPowerList);
        result.put("avgShortTermForecast", avgShortTermForecastList);
        log.info("result  : {}",result);
        return result;
    }
}
