package cn.iocoder.yudao.module.et.dal.dataobject.rollingMatchingTransactions;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 日滚动交易结果实体类
 */
@Data
@TableName("jy_daily_rolling_result")
public class JyDailyRollingResult {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 申报日
     */
    private String bidDate;

    /**
     * 标的日
     */
    private String targetDate;

    /**
     * 交易名称
     */
    private String tradeSeq;

    /**
     * 时段
     */
    private String timeSlot;

    /**
     * 买卖方向
     */
    private String tradingDirection;

    /**
     * 成交电量
     */
    private BigDecimal quantity;

    /**
     * 成交均价
     */
    private BigDecimal price;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;

    /**
     * 交易单元ID
     */
    private Long tradingUnitId;

    /**
     * 数据爬取时间
     */
    private Date gatherTime;

    /**
     * 修改时间
     */
    private Date modificationTime;
}