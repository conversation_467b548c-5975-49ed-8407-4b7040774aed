package cn.iocoder.yudao.module.et.dal.mysql.contractSummary;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.et.dal.dataobject.contractSummary.ContractSequenceMonthlyDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

/**
 * ContractSequenceMonthlyMapper
 **/
@DS("shardingsphereDB")
@Mapper
public interface ContractSequenceMonthlyMapper extends BaseMapperX<ContractSequenceMonthlyDO> {
}
