package cn.iocoder.yudao.module.et.service.spiderlog;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.DispatchNodeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.spiderlog.SpiderLogDO;
import cn.iocoder.yudao.module.et.dal.mysql.dispatchnode.DispatchNodeMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.spiderlog.SpiderLogMapper;
import cn.iocoder.yudao.module.et.service.companyunit.CompanyUnitService;
import cn.iocoder.yudao.module.et.util.JyDateTimeUtil;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.sql.Timestamp;
import java.util.*;

/**
 * 数据入库记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpiderLogServiceImpl implements SpiderLogService {
    @Resource
    private SpiderLogMapper spiderLogMapper;
    @Resource
    private CompanyUnitService companyUnitService;
    @Resource
    private DispatchNodeMapper dispatchNodeMapper;
    @Resource
    private DeptService deptService;

    @Override
    public Map<String, Object> getSettlementReleaseSum(String unitId, String monthDate, Long[] date) {
        Map<String, Object> map = new HashMap<>();
        Timestamp startDate;
        Timestamp endDate;
        if (date.length == 0 || date[0] < 0) {
            monthDate = monthDate + "-01 00:00:00";
            startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            endDate = new Timestamp(calendar.getTimeInMillis() + 1000);
        } else {
            String dateStr = DateUtil.format(new Date(date[0]), "yyyy-MM-dd HH:mm:00");
            startDate = Timestamp.valueOf(dateStr);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            endDate = new Timestamp(calendar.getTimeInMillis());
        }
        //传入的交易单元id
        //结算发布-新 标签时间
        List<SpiderLogDO> spiderLogDOListNew = spiderLogMapper.selectDataTime(startDate, endDate, unitId, "GS-B-511");
        //结算发布 标签时间
        List<SpiderLogDO> spiderLogDOList = spiderLogMapper.selectDataTime(startDate, endDate, unitId, "GS-B-611");
        List<String> dataTimeNew = spiderLogDOListNew.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList();
        List<String> dataTime = spiderLogDOList.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList();
        map.put("结算发布-新", dataTimeNew);
        map.put("结算发布", dataTime);
        return map;
    }

    @Override
    public Map<String, List<String>> getCurrentPrices(String[] ids, Long[] date) {
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(JyDateTimeUtil.getDayStartTime(date[i])));
        }
        Map<String, List<String>> map = new HashMap<>();
        String companyId = companyUnitService.getCurrentUserOrgCode();
        for (String id : ids) {
            List<SpiderLogDO> spiderLogDOList = spiderLogMapper.selectDataTime(timestamps[0], timestamps[1], id, "GS-B-231");
            if (id.equals(companyId)) {
                List<DeptDO> deptDOS = deptService.getByOrgCode(id);
                if (!deptDOS.isEmpty())
                    map.put(deptDOS.get(0).getName(), spiderLogDOList.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList());
            } else {
                LambdaQueryWrapper<DispatchNodeDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DispatchNodeDO::getCode, id);
                DispatchNodeDO d = dispatchNodeMapper.selectOne(queryWrapper);
                map.put(d.getName(), spiderLogDOList.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList());
            }
        }
        return map;
    }

    @Override
    public Map<String, List<String>> getRealCurrentPrices(String[] ids, Long[] date) {
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(JyDateTimeUtil.getDayStartTime(date[i])));
        }
        Map<String, List<String>> map = new HashMap<>();
        String companyId = companyUnitService.getCurrentUserOrgCode();
        for (String id : ids) {
            List<SpiderLogDO> spiderLogDOList = spiderLogMapper.selectDataTime(timestamps[0], timestamps[1], id, "GS-B-241");
            if (id.equals(companyId)) {
                List<DeptDO> deptDOS = deptService.getByOrgCode(id);
                if (!deptDOS.isEmpty())
                    map.put(deptDOS.get(0).getName(), spiderLogDOList.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList());
            } else {
                LambdaQueryWrapper<DispatchNodeDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DispatchNodeDO::getCode, id);
                DispatchNodeDO d = dispatchNodeMapper.selectOne(queryWrapper);
                map.put(d.getName(), spiderLogDOList.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList());
            }
        }
        return map;
    }

    @Override
    public List<String> getClearanceMonitor(String unitId, Long[] date) {
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(JyDateTimeUtil.getDayStartTime(date[i])));
        }
        List<SpiderLogDO> spiderLogDOList = spiderLogMapper.selectDataTime(timestamps[0], timestamps[1], unitId, "GS-B-251");
        return spiderLogDOList.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList();
    }

    @Override
    public List<String> getPartitionPrice(Long[] date) {
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(JyDateTimeUtil.getDayStartTime(date[i])));
        }
        List<SpiderLogDO> spiderLogDOList = spiderLogMapper.selectDataTime(timestamps[0], timestamps[1], companyUnitService.getCurrentUserOrgCode(), "GS-B-271");
        return spiderLogDOList.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList();
    }

    @Override
    public List<String> getSettlementPrice(Long[] date) {
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(JyDateTimeUtil.getDayStartTime(date[i])));
        }
        List<SpiderLogDO> spiderLogDOList = spiderLogMapper.selectDataTime(timestamps[0], timestamps[1], companyUnitService.getCurrentUserOrgCode(), "GS-B-271");
        return spiderLogDOList.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList();
    }

    @Override
    public List<String> getSettlementElectricity(String unitId, Long[] date) {
        date[1] = date[1] + 86400000L;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(JyDateTimeUtil.getDayStartTime(date[i])));
        }
        List<SpiderLogDO> spiderLogDOList = spiderLogMapper.selectDataTime(timestamps[0], timestamps[1], unitId, "GS-B-512");
        return spiderLogDOList.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList();
    }

    @Override
    public Map<String, Object> getSettlementRelease(String unitId, String monthDate) {
        Map<String, Object> map = new HashMap<>();
        Timestamp startDate;
        Timestamp endDate;
        monthDate = monthDate + "-01 00:00:00";
        startDate = Timestamp.valueOf(monthDate);
        // 使用 Calendar 进行时间操作
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startDate.getTime());
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 获取下个月 1 号的时间戳
        endDate = new Timestamp(calendar.getTimeInMillis());
        //传入的交易单元id
        //结算发布-新 标签时间
        List<SpiderLogDO> spiderLogDOListNew = spiderLogMapper.selectDataTime(startDate, endDate, unitId, "GS-B-511");
        //结算发布 标签时间
        List<SpiderLogDO> spiderLogDOList = spiderLogMapper.selectDataTime(startDate, endDate, unitId, "GS-B-611");
        List<String> dataTimeNew = spiderLogDOListNew.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList();
        List<String> dataTime = spiderLogDOList.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList();
        map.put("结算发布-新", dataTimeNew);
        map.put("结算发布", dataTime);
        return map;
    }

    @Override
    public List<String> getRemainingSpace(String unitId, Long[] date) {
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(JyDateTimeUtil.getDayStartTime(date[i])));
        }
        List<SpiderLogDO> spiderLogDOList = spiderLogMapper.selectDataTime(timestamps[0], timestamps[1], unitId, "GS-B-251");
        return spiderLogDOList.stream().map(SpiderLogDO::getGatherTime).sorted(Comparator.reverseOrder()).toList();
    }
}