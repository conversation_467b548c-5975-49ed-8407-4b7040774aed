package cn.iocoder.yudao.module.et.service.powerstation;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.et.controller.admin.powerstation.vo.PowerStationPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.powerstation.vo.PowerStationSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.MysqlTradingUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 场站信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PowerStationService {

    /**
     * 创建场站信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createPowerStation(@Valid PowerStationSaveReqVO createReqVO);

    /**
     * 更新场站信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePowerStation(@Valid PowerStationSaveReqVO updateReqVO);

    /**
     * 删除场站信息
     *
     * @param id 编号
     */
    void deletePowerStation(Integer id);

    /**
     * 获得场站信息
     *
     * @param id 编号
     * @return 场站信息
     */
    PowerStationDO getPowerStation(Integer id);

    /**
     * 通过传入的交易单元code获取场站信息
     *
     * @param code 交易单元code
     * @return 场站信息
     */
    PowerStationDO getPowerStationByCode(String code);

    /**
     * 获得场站信息分页
     *
     * @param pageReqVO 分页查询
     * @return 场站信息分页
     */
    PageResult<PowerStationDO> getPowerStationPage(PowerStationPageReqVO pageReqVO);

    List<Map<String, Object>> getTradingUnit();

    List<PowerStationDO> selectAll();
    List<PowerStationDO> selectAllByTenantIgnore();
    List<MysqlTradingUnitDO> selectAllTradingUnitByTenantIgnore();

    List<Map<String, Object>> getGroup();

    List<Map<String, Object>> getGroupTradingUnit();

    List<Map<String, Object>> getGroupOffCompany();

    /**
     * 根据传入的集团id获取所属的公司集合
     *
     * @param orgCode 集团id
     * @return
     */
    List<PowerStationDO> getPowerStationByOrgCode(String orgCode);
}