package cn.iocoder.yudao.module.et.service.dispatchnode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.et.controller.admin.dispatchnode.vo.DispatchNodePageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.dispatchnode.vo.DispatchNodeSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.DispatchNodeDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 交易单元 Service 接口
 *
 * <AUTHOR>
 */
public interface DispatchNodeService {

    /**
     * 创建交易单元
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createDispatchNode(@Valid DispatchNodeSaveReqVO createReqVO);

    /**
     * 更新交易单元
     *
     * @param updateReqVO 更新信息
     */
    void updateDispatchNode(@Valid DispatchNodeSaveReqVO updateReqVO);

    /**
     * 删除交易单元
     *
     * @param id 编号
     */
    void deleteDispatchNode(Integer id);

    /**
     * 获得交易单元
     *
     * @param id 编号
     * @return 交易单元
     */
    DispatchNodeDO getDispatchNode(Integer id);

    /**
     * 获得交易单元分页
     *
     * @param pageReqVO 分页查询
     * @return 交易单元分页
     */
    PageResult<DispatchNodeDO> getDispatchNodePage(DispatchNodePageReqVO pageReqVO);

    //根据code查name
    String getNameByCode(String code);

    //根据code查dispatch_node
    DispatchNodeDO getDispatchNodeByCode(String code);

    List<DispatchNodeDO> getDispatchNodeList();
}