package cn.iocoder.yudao.module.et.dal.mysql.startupanddownunitdetail;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.dal.dataobject.startupanddownunitdetail.StartupAndDownUnitDetailDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 必开必停机组明细信息 Mapper
 *
 * <AUTHOR>
 */
@DS("shardingsphereDB")
@Mapper
public interface StartupAndDownUnitDetailMapper extends BaseMapperX<StartupAndDownUnitDetailDO> {

    @TenantIgnore
    default List<StartupAndDownUnitDetailDO> getByPid(Long pid) {
        return selectList(new LambdaQueryWrapperX<StartupAndDownUnitDetailDO>()
                .ge(StartupAndDownUnitDetailDO::getBizDate, pid));
    }

}