package cn.iocoder.yudao.module.et.service.word;

import cn.iocoder.yudao.module.et.controller.admin.reviewinfo.vo.ReviewInfoVO;
import cn.iocoder.yudao.module.et.service.review1ddetails.Review1DDetailsService;
import cn.iocoder.yudao.module.et.util.WordUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * D-1复盘导出 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WordExportTradingServiceImpl implements WordExportTradingService {
    private static final String D1TEMPLATE_PATH = "templates/交易复盘日志-模版.docx";
    private static final String OUTPUT_D1FILENAME = "D-1日交易复盘日志.docx";
    private static final String CHARTS_DAYAHEAD = "${dayAheadPrice1DInfo}";
    private static final String CHARTS_SETTLEMENT = "${settlementPrice1DInfo}";
    private static final String INFLUENTIAL_TABLE_PLACEHOLDER = "${influentialFactorInfo}";
    @Resource
    Review1DDetailsService review1DDetailsService;

    @Override
    public void exportTradeLog(HttpServletResponse response, ReviewInfoVO reviewInfoVO) throws Exception {
        Map<String, Object> dataMap = review1DDetailsService.getTradingReview(reviewInfoVO.getReviewInfoId());
        Map<String, Map<String, Map<String, String>>> tradingInfo = (Map<String, Map<String, Map<String, String>>>) dataMap.get("tradingInfo");
        Map<String, Map<String, String>> hd = tradingInfo.get("hd");
        Map<String, Map<String, String>> hx = tradingInfo.get("hx");
        dataMap.put("wuwei", hd.get("武威"));
        dataMap.put("minqin", hx.get("民勤"));
        dataMap.put("linze", hx.get("临泽"));
        dataMap.put("dunhuang", hx.get("敦煌"));
        dataMap.put("yumen", hx.get("玉门"));
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(D1TEMPLATE_PATH);
             XWPFDocument doc = new XWPFDocument(is);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {


            // 2. 插入图标
            updateDayAheadPrice1DInfoCharts(doc, dataMap);
            updateSettlementPrice1DInfoCharts(doc, dataMap);

            // 3. 生成表格
            buildInfluentialTable(doc, dataMap);
            // 1. 替换基础信息
            processTables(doc, dataMap);
            replaceTradingText(doc, dataMap);

            //mergeCellsInTable(doc);
            // 写入响应流
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=" + OUTPUT_D1FILENAME);
            doc.write(baos);
            response.getOutputStream().write(baos.toByteArray());
        }
    }

    private void replaceTradingText(XWPFDocument document, Map<String, Object> dataMap) {

        //今日运行人员文本     这个不在表格里不能用寻找方法
        // 获取目标段落（假设是文档的第二个段落）
        XWPFParagraph peopleParagraph = document.getParagraphs().get(2);

        // 1. 合并段落中所有 Run 的文本
        StringBuilder mergedText = new StringBuilder();
        for (XWPFRun peopleParagraphRun : peopleParagraph.getRuns()) {
            String text = peopleParagraphRun.getText(0);
            if (text != null) {
                mergedText.append(text);
            }
        }
        String fullText = mergedText.toString();
        String placeholder = "${operatingDayPersons}";
        if (fullText.contains(placeholder)) {
            // 3. 替换占位符为实际值
            String replacement = dataMap.get("operatingDayPersons") == null ? "" : dataMap.get("operatingDayPersons").toString();
            String newText = fullText.replace(placeholder, replacement);

            // 4. 清空原有所有 Runs
            for (int i = peopleParagraph.getRuns().size() - 1; i >= 0; i--) {
                peopleParagraph.removeRun(i);
            }

            // 5. 创建新 Run 并插入替换后的文本
            XWPFRun newRun = peopleParagraph.createRun();
            newRun.setText(newText);
        }
    }

    private void processTables(XWPFDocument document, Map<String, Object> dataMap) {
        // 文本替换逻辑不变（无 POI 版本相关代码）
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    List<XWPFParagraph> paragraphs = cell.getParagraphs();
                    boolean hasPlaceholders = false;

                    for (XWPFParagraph para : paragraphs) {
                        for (XWPFRun run : para.getRuns()) {
                            String text = run.getText(0);
                            if (text != null && text.contains("${")) {
                                hasPlaceholders = true;
                                break;
                            }
                        }
                        if (hasPlaceholders) break;
                    }

                    if (!hasPlaceholders) continue;

                    for (XWPFParagraph para : paragraphs) {
                        List<XWPFRun> runs = para.getRuns();
                        for (int i = 0; i < runs.size(); i++) {
                            XWPFRun run = runs.get(i);
                            String originalText = run.getText(0);
                            if (originalText == null || !originalText.contains("${")) continue;

                            Map<String, String> stringDataMap = new HashMap<>();
                            dataMap.forEach((key, value) ->
                                    stringDataMap.put(key, value != null ? value.toString() : "")
                            );
                            String replacedText = WordUtil.replacePlaceholders(originalText, stringDataMap);
                            run.setText(replacedText, 0);
                        }
                    }
                }
            }
        }
    }


    private XWPFTableCell findTableCellByPlaceholder(XWPFDocument document, String placeholder) {
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph para : cell.getParagraphs()) {
                        String text = para.getText();
                        if (text != null && text.trim().equals(placeholder.trim())) {
                            // 找到占位符后，直接清空该段落
                            for (XWPFRun run : para.getRuns()) {
                                run.setText("", 0); // 清空所有运行的文本
                            }
                            return cell;
                        }
                    }
                }
            }
        }
        return null;
    }

    private void clearCellContent(XWPFTableCell cell) {
        // 1. 清空所有段落（包括底层 XML 节点）
        List<XWPFParagraph> paragraphs = cell.getParagraphs();
        for (int i = paragraphs.size() - 1; i >= 0; i--) {
            cell.removeParagraph(i);
        }

        // 2. 清空所有子表格（通过底层 XML 操作）
        CTTc ctTc = cell.getCTTc();
        for (int i = ctTc.sizeOfTblArray() - 1; i >= 0; i--) {
            ctTc.removeTbl(i);
        }

        // 3. 强制刷新单元格内容（确保 XML 结构更新）
        cell.getCTTc().setPArray(new CTP[0]);
    }

    // 辅助方法：设置单个边框属性
    private void setBorder(CTBorder border, STBorder.Enum style, int sizeEighthPoints, String color) {
        border.setVal(style); // 边框类型
        border.setSz(BigInteger.valueOf(sizeEighthPoints)); // 宽度（单位：1/8点）
        border.setColor(color); // 颜色
    }


    private void updateDayAheadPrice1DInfoCharts(XWPFDocument document, Map<String, Object> dataMap) throws Exception {
        Map<String, List<Float>> chartData = (Map<String, List<Float>>) dataMap.get("dayAheadPrice1DInfo");
        if (chartData == null || chartData.isEmpty()) return;

        XWPFTableCell chartCell = findTableCellByPlaceholder(document, CHARTS_DAYAHEAD);
        if (chartCell == null) {
            System.err.println("未找到图表占位符：" + CHARTS_DAYAHEAD);
            return;
        }

        clearCellContent(chartCell);
        insertChartIntoCell(chartCell, chartData, 462, 150);
    }

    private void updateSettlementPrice1DInfoCharts(XWPFDocument document, Map<String, Object> dataMap) throws Exception {
        Map<String, List<Float>> chartData = (Map<String, List<Float>>) dataMap.get("settlementPrice1DInfo");
        if (chartData == null || chartData.isEmpty()) return;

        XWPFTableCell chartCell = findTableCellByPlaceholder(document, CHARTS_SETTLEMENT);
        if (chartCell == null) {
            System.err.println("未找到图表占位符：" + CHARTS_SETTLEMENT);
            return;
        }

        clearCellContent(chartCell);
        insertChartIntoCell(chartCell, chartData, 462, 150);
    }

    private void insertChartIntoCell(XWPFTableCell cell, Map<String, List<Float>> chartData, int width, int hight) throws Exception {
        // 1. 生成图表图片
        File chartImage = WordUtil.generatePriceChart(chartData);

        // 2. 插入图片到单元格
        try (FileInputStream fis = new FileInputStream(chartImage)) {
            cell.addParagraph().createRun().addPicture(
                    fis,
                    XWPFDocument.PICTURE_TYPE_PNG,
                    "chart.png",
                    Units.toEMU(width),  // 宽度（400像素）
                    Units.toEMU(hight)    // 高度（250像素）
            );
        }

        // 3. 删除临时图片
        chartImage.delete();
    }

    // D-1决策表格
    private void buildInfluentialTable(XWPFDocument document, Map<String, Object> dataMap) {
        Map<String, Object> influentialData = (Map<String, Object>) dataMap.get("influentialFactorInfo");
        XWPFTableCell cell = findTableCellByPlaceholder(document, INFLUENTIAL_TABLE_PLACEHOLDER);
        if (cell == null) {
            System.err.println("未找到策略表格占位符：" + INFLUENTIAL_TABLE_PLACEHOLDER);
            return;
        }
        clearCellContent(cell);

        // 获取数据
        List<String> header1 = (List<String>) influentialData.get("header1");
        List<String> header2 = (List<String>) influentialData.get("header2");
        List<List<Object>> data = new ArrayList<>();
        data.add((List<Object>) influentialData.get("武威"));
        data.get(0).add(0, "武威");
        data.add((List<Object>) influentialData.get("民勤"));
        data.get(1).add(0, "民勤");
        data.add((List<Object>) influentialData.get("临泽"));
        data.get(2).add(0, "临泽");
        data.add((List<Object>) influentialData.get("敦煌"));
        data.get(3).add(0, "敦煌");
        data.add((List<Object>) influentialData.get("玉门"));
        data.get(4).add(0, "玉门");
        // 创建表格结构
        CTTc ctTc = cell.getCTTc();
        CTTbl ctTbl = ctTc.addNewTbl();
        // =============== 2. 第一表头行（4列跨8列） ===============
        {
            CTRow headerRow1 = ctTbl.addNewTr();
            // 合并逻辑调整（总列数需匹配）
            addMergedHeaderCell(headerRow1, header1.get(0), 1, "FFFFFF");  // 第1列
            addMergedHeaderCell(headerRow1, header1.get(1), 2, "FFFFFF");  // 合并2列（列2-3）
            addMergedHeaderCell(headerRow1, header1.get(2), 2, "FFFFFF");  // 合并2列（列4-5）
            addMergedHeaderCell(headerRow1, header1.get(3), 3, "FFFFFF");  // 合并3列（列6-8）
        }

        // =============== 3. 第二表头行（8个独立列） ===============
        {
            CTRow headerRow2 = ctTbl.addNewTr();
            for (String header : header2) {
                addHeaderCell(headerRow2, header, 1, "FFFFFF");
            }
        }

        // =============== 4. 列宽设置 ===============
        CTTblGrid grid = ctTbl.addNewTblGrid();
        grid.addNewGridCol().setW(BigInteger.valueOf(1050));  // 第1列（电站名称）
        grid.addNewGridCol().setW(BigInteger.valueOf(1250));  // 列2
        grid.addNewGridCol().setW(BigInteger.valueOf(1250));  // 列3
        grid.addNewGridCol().setW(BigInteger.valueOf(1150));  // 列4
        grid.addNewGridCol().setW(BigInteger.valueOf(1150));  // 列5
        grid.addNewGridCol().setW(BigInteger.valueOf(1300));  // 列6
        grid.addNewGridCol().setW(BigInteger.valueOf(1050));  // 列7
        grid.addNewGridCol().setW(BigInteger.valueOf(1050));  // 列8

        // =============== 5. 表格样式 ===============
        CTTblPr tblPr = ctTbl.addNewTblPr();
        // 固定布局
        CTTblLayoutType layout = tblPr.addNewTblLayout();
        layout.setType(STTblLayoutType.FIXED);
        // 边框
        CTTblBorders borders = tblPr.addNewTblBorders();
        setBorder(borders.addNewTop(), STBorder.SINGLE, 4, "000000");
        setBorder(borders.addNewLeft(), STBorder.SINGLE, 4, "000000");
        setBorder(borders.addNewBottom(), STBorder.SINGLE, 4, "000000");
        setBorder(borders.addNewRight(), STBorder.SINGLE, 4, "000000");
        setBorder(borders.addNewInsideH(), STBorder.SINGLE, 2, "000000");
        setBorder(borders.addNewInsideV(), STBorder.SINGLE, 2, "000000");

        // =============== 6. 填充数据 ===============
        for (List<Object> rowData : data) {
            CTRow dataRow = ctTbl.addNewTr();
            // 交替行背景色
            String bgColor = "FFFFFF";
            for (int i = 0; i < rowData.size(); i++) {
                //for (String rowDatum : rowData) {
                CTTc dataCell = dataRow.addNewTc();
                CTTcPr cellPr = dataCell.addNewTcPr();
                // 背景色
                CTShd shd = cellPr.addNewShd();
                shd.setFill(bgColor);
                shd.setVal(STShd.CLEAR);

                // 内容
                CTP p = dataCell.addNewP();
                p.addNewPPr().addNewJc().setVal(STJc.CENTER);
                CTR r = p.addNewR();
                r.addNewT().setStringValue(rowData.get(i).toString());
            }
        }
        //合并垂直单元格
        mergeCellsInTable(ctTbl);
        // =============== 7. 设置全局表格样式 ===============
        // 设置表格字体和行高
        setGlobalTableStyle(ctTbl);

        // =============== 8. 设置行高和垂直对齐 ===============
        setAllRowStyle(ctTbl); // 新增行高设置方法
    }

    // 设置全局表格样式
    private void setGlobalTableStyle(CTTbl ctTbl) {
        // 设置默认字体（影响整个表格）
        CTTblPr tblPr = ctTbl.getTblPr();
        CTTblCellMar cellMar = tblPr.addNewTblCellMar();
        cellMar.addNewLeft().setW(BigInteger.valueOf(100));  // 左边距100缇
        cellMar.addNewRight().setW(BigInteger.valueOf(100)); // 右边距100缇

        // 设置默认段落样式
        CTTblLayoutType layout = tblPr.addNewTblLayout();
        layout.setType(STTblLayoutType.FIXED);
    }

    // 设置所有行样式
    private void setAllRowStyle(CTTbl ctTbl) {
        List<CTRow> rows = ctTbl.getTrList();
        for (CTRow ctRow : rows) {
            // 设置行高（0.83厘米 = 470缇）
            CTHeight height = ctRow.addNewTrPr().addNewTrHeight();
            height.setVal(BigInteger.valueOf(470));
            height.setHRule(STHeightRule.EXACT);

            // 设置所有单元格垂直居中
            for (CTTc ctTc : ctRow.getTcList()) {
                CTTcPr tcPr = ctTc.isSetTcPr() ? ctTc.getTcPr() : ctTc.addNewTcPr();
                CTVerticalJc vAlign = tcPr.addNewVAlign();
                vAlign.setVal(STVerticalJc.CENTER);
            }
        }
    }

    // 修改后的合并方法（增加垂直居中）
    private void mergeCellsInTable(CTTbl ctTbl) {
        List<CTRow> rows = ctTbl.getTrList();
        for (int i = 0; i < rows.size(); i++) {
            if (i > 1) {
                List<CTTc> tcList = rows.get(i).getTcList();
                for (int j = 0; j < tcList.size(); j++) {
                    if (j == 3 || j == 4) {
                        CTTc ct = tcList.get(j);

                        // 设置垂直合并
                        CTVMerge contVMerge = CTVMerge.Factory.newInstance();
                        contVMerge.setVal(i == 2 ? STMerge.RESTART : STMerge.CONTINUE);

                        // 获取或创建单元格属性
                        CTTcPr tcPr = ct.isSetTcPr() ? ct.getTcPr() : ct.addNewTcPr();
                        tcPr.setVMerge(contVMerge);

                        // 新增垂直居中设置
                        CTVerticalJc vAlign = tcPr.addNewVAlign();
                        vAlign.setVal(STVerticalJc.CENTER);
                    }
                }
            }
        }
    }

    // 修改后的合并表头单元格方法
    private void addMergedHeaderCell(CTRow row, String text, int colspan, String bgColor) {
        CTTc cell = row.addNewTc();
        CTTcPr cellPr = cell.addNewTcPr();
        cellPr.addNewGridSpan().setVal(BigInteger.valueOf(colspan));

        // 样式设置
        CTShd shd = cellPr.addNewShd();
        shd.setFill(bgColor);
        cellPr.addNewVAlign().setVal(STVerticalJc.CENTER);

        CTP p = cell.addNewP();
        p.addNewPPr().addNewJc().setVal(STJc.CENTER);

        // 处理数值下划线
        String[] parts = text.split(" ", 2);
        if (parts.length == 2) {
            // 文字部分
            CTR textRun = p.addNewR();
            textRun.addNewT().setStringValue(parts[0] + " ");

            // 数值部分（带下划线）
            CTR numRun = p.addNewR();
            CTRPr numPr = numRun.addNewRPr();
            numPr.addNewU().setVal(STUnderline.SINGLE);
            numRun.addNewT().setStringValue(parts[1]);
        } else {
            // 无空格情况
            CTR normalRun = p.addNewR();
            normalRun.addNewT().setStringValue(text);
        }
    }

    // 添加普通表头单元格
    private void addHeaderCell(CTRow row, String text, int colspan, String bgColor) {
        CTTc cell = row.addNewTc();
        CTTcPr cellPr = cell.addNewTcPr();
        cellPr.addNewGridSpan().setVal(BigInteger.valueOf(colspan));

        CTShd shd = cellPr.addNewShd();
        shd.setFill(bgColor);
        cellPr.addNewVAlign().setVal(STVerticalJc.CENTER);

        CTP p = cell.addNewP();
        p.addNewPPr().addNewJc().setVal(STJc.CENTER);
        CTR r = p.addNewR();
        r.addNewRPr().addNewSz().setVal(BigInteger.valueOf(20));
        r.addNewT().setStringValue(text);
    }

}