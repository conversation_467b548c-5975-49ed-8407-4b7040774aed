package cn.iocoder.yudao.module.et.service.dataproc.intra;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO;
import cn.iocoder.yudao.module.et.dal.tdengine.dataproc.PrivateDataProcMapper;
import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.jy.soft.remote.bean.SocketPacket;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;

/**
 * GS-B-512 现货市场结算新-电厂侧结算展示-结算电量_电厂
 *
 * <AUTHOR>
 * @date 2024-11-08
 **/
@Slf4j
@Service
@AllArgsConstructor
public class ClearingQuantityProc implements BaseDataProc {

    private final PrivateDataProcMapper privateDataProcMapper;

    @Override
    public boolean execute(SocketPacket dto) {
        long runtime = System.currentTimeMillis();
        try {
            TenantUtils.execute(dto.getTenantId(), () -> {
                JSONObject jsonObject = JSONUtil.parseObj(dto.getData());
                List<TradingUnitDO> tradingUnitDOList = CollUtil.newArrayList();
                TradingUnitDO entity;
                for (String key : jsonObject.keySet()) {
                    for (CsvRow row : CsvUtil.getReader(new CsvReadConfig().setContainsHeader(true).setTrimField(true))
                        .readFromStr(jsonObject.getStr(key)).getRows()) {
                        entity = new TradingUnitDO();
                        // 时间
                        entity.setTs(Timestamp.valueOf(StrUtil.format("{} {}:00", key, row.get(1))));
                        // 偏差率
                        if (StrUtil.isNotBlank(row.get(2))) {
                            entity.setDeviationRate(Float.parseFloat(row.get(2)));
                        }
                        // TMR电量
                        if (StrUtil.isNotBlank(row.get(3))) {
                            entity.setTmrPower(Float.parseFloat(row.get(3)));
                        }
                        // SCADA电量
                        if (StrUtil.isNotBlank(row.get(4))) {
                            entity.setScadaPower(Float.parseFloat(row.get(4)));
                        }
                        // 新能源实时消纳电量
                        if (StrUtil.isNotBlank(row.get(5))) {
                            entity.setRealUsePower(Float.parseFloat(row.get(5)));
                        }
                        // 校正电量
                        if (StrUtil.isNotBlank(row.get(6))) {
                            entity.setCalibrationPower(Float.parseFloat(row.get(6)));
                        }
                        // 结算电量
                        if (StrUtil.isNotBlank(row.get(7))) {
                            entity.setSettlementPower(Float.parseFloat(row.get(7)));
                        }
                        tradingUnitDOList.add(entity);
                    }
                }
                if (CollUtil.isNotEmpty(tradingUnitDOList)) {
                    privateDataProcMapper.insertClearingQuantity(dto.getOrgCode(), tradingUnitDOList);
                    log.info("[{}] > 解析入库完成，共 {} 条，用时 {} ms", dto.getBizCode(), tradingUnitDOList.size(),
                        System.currentTimeMillis() - runtime);
                }
            });
        } catch (Exception ex) {
            log.error(StrUtil.format("[{}] > 解析出现异常", dto.getBizCode()), ex);
            return false;
        }
        return true;
    }
}
