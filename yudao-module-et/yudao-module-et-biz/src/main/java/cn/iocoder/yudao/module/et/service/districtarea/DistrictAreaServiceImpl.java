package cn.iocoder.yudao.module.et.service.districtarea;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.et.controller.admin.districtarea.vo.DistrictAreaPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.districtarea.vo.DistrictAreaSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.districtarea.DistrictAreaDO;
import cn.iocoder.yudao.module.et.dal.mysql.districtarea.DistrictAreaMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.et.enums.ErrorCodeConstants.DISTRICT_AREA_NOT_EXISTS;

/**
 * 省份对应算法类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DistrictAreaServiceImpl implements DistrictAreaService {

    @Resource
    private DistrictAreaMapper districtAreaMapper;

    @Override
    public String createDistrictArea(DistrictAreaSaveReqVO createReqVO) {
        // 插入
        DistrictAreaDO districtArea = BeanUtils.toBean(createReqVO, DistrictAreaDO.class);
        districtAreaMapper.insert(districtArea);
        // 返回
        return districtArea.getCode();
    }

    @Override
    public void updateDistrictArea(DistrictAreaSaveReqVO updateReqVO) {
        // 校验存在
        validateDistrictAreaExists(updateReqVO.getCode());
        // 更新
        DistrictAreaDO updateObj = BeanUtils.toBean(updateReqVO, DistrictAreaDO.class);
        districtAreaMapper.updateById(updateObj);
    }

    @Override
    public void deleteDistrictArea(String id) {
        // 校验存在
        validateDistrictAreaExists(id);
        // 删除
        districtAreaMapper.deleteById(id);
    }

    private void validateDistrictAreaExists(String id) {
        if (districtAreaMapper.selectById(id) == null) {
            throw exception(DISTRICT_AREA_NOT_EXISTS);
        }
    }

    @Override
    public DistrictAreaDO getDistrictArea(String id) {
        return districtAreaMapper.selectById(id);
    }

    @Override
    public PageResult<DistrictAreaDO> getDistrictAreaPage(DistrictAreaPageReqVO pageReqVO) {
        return districtAreaMapper.selectPage(pageReqVO);
    }

    @Override
    public DistrictAreaDO selectByCode(String code) {
        return districtAreaMapper.selectByCode(code);
    }

}