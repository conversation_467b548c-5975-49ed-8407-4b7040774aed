package cn.iocoder.yudao.module.et.dal.dataobject.timeslots;

import lombok.*;

import java.time.LocalTime;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 省份峰谷平时段管理 DO
 *
 * <AUTHOR>
 */
@TableName("jy_time_slots")
@KeySequence("jy_time_slots_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimeSlotsDO extends BaseDO {

    /**
     * 时间段类型，枚举值：'peak'（高峰）、'off-peak'（平时）、'valley'（低谷）
     */
    private String type;
    /**
     * 开始时间
     */
    private LocalTime startTime;
    /**
     * 结束时间
     */
    private LocalTime endTime;
    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 外键，省份编码
     */
    private String code;

    private String tenantId;
}