package cn.iocoder.yudao.module.et.controller.admin.contractstime24.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 合同分时段信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContractsTime24PageReqVO extends PageParam {

    @Schema(description = "时段编号")
    private String timeSlotCoding;

    @Schema(description = "时段名称", example = "张三")
    private String timeSlotName;

    @Schema(description = "购方电量")
    private BigDecimal purchaserQuantity;

    @Schema(description = "售方电量")
    private BigDecimal sellerQuantity;

    @Schema(description = "购方电价", example = "9432")
    private BigDecimal purchaserPrice;

    @Schema(description = "售方电价", example = "20518")
    private BigDecimal sellerPrice;

    @Schema(description = "部门ID", example = "8005")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] gatherDate;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] modificationTime;

    @Schema(description = "时段区间")
    private String timeSlotsRange;

    @Schema(description = "时间段")
    private String timeSlots;

    @Schema(description = "时间段开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] beginTime;

    @Schema(description = "时间段结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endTime;

    @Schema(description = "合同id", example = "25739")
    private String contractsId;

}