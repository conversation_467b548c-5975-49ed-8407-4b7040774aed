package cn.iocoder.yudao.module.et.dal.dataobject.mustOpenAndStopUnit;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 必开必停机组信息 DO
 *
 * <AUTHOR>
 */
@TableName("jy_startup_and_down_unit")
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class StartupAndDownUnitDO {
    @TableField("run_day")
    private LocalDateTime ts; // 时间

    private Integer powerPlantId; // 电厂Id

    @TableField(exist = false)
    private String powerPlantName; // 电厂名称

    private String unitAbbreviation; // 机组简称
    private String nonHeatAdditionMustOpenUnitNumber; //
    private String nonHeatAdditionMustStopUnitNumber; //
    private String heatAdditionMustOpenUnitNumber; //
    private String heatAdditionMustStopUnitNumber; //

    private String ratedCapacity; // 额定容量

    private String nonHeatAdditionMustOpenUnit; // 非供热期必开机组

    private String nonHeatAdditionMustOpenReason; // 非供热期必开原因

    private String nonHeatAdditionMustStopUnit; // 非供热期必停机组

    private String nonHeatAdditionMustStopReason; // 非供热期必停原因

    private String heatAdditionMustOpenUnit; // 供热期必开机组

    private String heatAdditionMustOpenReason; // 供热期必开原因

    private String heatAdditionMustStopUnit; // 供热期必停机组

    private String heatAdditionMustStopReason; // 供热期必停原因

    private Long tenantId; // 租户

    private String orgCode; // 公司Id

    private String creator; // 创建人

    private LocalDateTime createTime; // 创建时间

    private String updater; // 修改人

    private LocalDateTime updateTime; // 更新时间

    private Boolean deleted; // 删除：0否/1是

}
