package cn.iocoder.yudao.module.et.controller.admin.districtarea;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.districtarea.vo.DistrictAreaPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.districtarea.vo.DistrictAreaRespVO;
import cn.iocoder.yudao.module.et.controller.admin.districtarea.vo.DistrictAreaSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.districtarea.DistrictAreaDO;
import cn.iocoder.yudao.module.et.service.districtarea.DistrictAreaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 省份对应算法类型")
@RestController
@RequestMapping("/jy/district-area")
@Validated
public class DistrictAreaController {

    @Resource
    private DistrictAreaService districtAreaService;

    @PostMapping("/create")
    @Operation(summary = "创建省份对应算法类型")
    @PreAuthorize("@ss.hasPermission('jy:district-area:create')")
    public CommonResult<String> createDistrictArea(@Valid @RequestBody DistrictAreaSaveReqVO createReqVO) {
        return success(districtAreaService.createDistrictArea(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新省份对应算法类型")
    @PreAuthorize("@ss.hasPermission('jy:district-area:update')")
    public CommonResult<Boolean> updateDistrictArea(@Valid @RequestBody DistrictAreaSaveReqVO updateReqVO) {
        districtAreaService.updateDistrictArea(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除省份对应算法类型")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('jy:district-area:delete')")
    public CommonResult<Boolean> deleteDistrictArea(@RequestParam("id") String id) {
        districtAreaService.deleteDistrictArea(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得省份对应算法类型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('jy:district-area:query')")
    public CommonResult<DistrictAreaRespVO> getDistrictArea(@RequestParam("id") String id) {
        DistrictAreaDO districtArea = districtAreaService.getDistrictArea(id);
        return success(BeanUtils.toBean(districtArea, DistrictAreaRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得省份对应算法类型分页")
    @PreAuthorize("@ss.hasPermission('jy:district-area:query')")
    public CommonResult<PageResult<DistrictAreaRespVO>> getDistrictAreaPage(@Valid DistrictAreaPageReqVO pageReqVO) {
        PageResult<DistrictAreaDO> pageResult = districtAreaService.getDistrictAreaPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DistrictAreaRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出省份对应算法类型 Excel")
    @PreAuthorize("@ss.hasPermission('jy:district-area:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDistrictAreaExcel(@Valid DistrictAreaPageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DistrictAreaDO> list = districtAreaService.getDistrictAreaPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "省份对应算法类型.xls", "数据", DistrictAreaRespVO.class,
                BeanUtils.toBean(list, DistrictAreaRespVO.class));
    }

}