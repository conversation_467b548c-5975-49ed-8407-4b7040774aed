package cn.iocoder.yudao.module.et.service.contractstype;

import cn.hutool.core.convert.Convert;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.contractstype.vo.ContractsTypePageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractstype.vo.ContractsTypeSaveReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractstype.vo.ContractsTypeVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstype.ContractsTypeDO;
import cn.iocoder.yudao.module.et.dal.mysql.contractstype.ContractsTypeMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.et.enums.ErrorCodeConstants.CONTRACTS_TYPE_NOT_EXISTS;

/**
 * 合同类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ContractsTypeServiceImpl implements ContractsTypeService {

    @Resource
    private ContractsTypeMapper contractsTypeMapper;

    @Override
    public Long createContractsType(ContractsTypeSaveReqVO createReqVO) {
        // 插入
        ContractsTypeDO contractsType = BeanUtils.toBean(createReqVO, ContractsTypeDO.class);
        contractsTypeMapper.insert(contractsType);
        // 返回
        return contractsType.getId();
    }

    @Override
    public void updateContractsType(ContractsTypeSaveReqVO updateReqVO) {
        // 校验存在
        validateContractsTypeExists(updateReqVO.getId());
        // 更新
        ContractsTypeDO updateObj = BeanUtils.toBean(updateReqVO, ContractsTypeDO.class);
        contractsTypeMapper.updateById(updateObj);
    }

    @Override
    public void deleteContractsType(Long id) {
        // 校验存在
        validateContractsTypeExists(id);
        // 删除
        contractsTypeMapper.deleteById(id);
    }

    private void validateContractsTypeExists(Long id) {
        if (contractsTypeMapper.selectById(id) == null) {
            throw exception(CONTRACTS_TYPE_NOT_EXISTS);
        }
    }

    @Override
    @TenantIgnore
    public ContractsTypeDO getContractsType(Long id) {
        return contractsTypeMapper.selectById(id);
    }

    @Override
    public PageResult<ContractsTypeDO> getContractsTypePage(ContractsTypePageReqVO pageReqVO) {
        return contractsTypeMapper.selectPage(pageReqVO);
    }

    @Override
    @TenantIgnore
    public List<ContractsTypeVO> getContractsTypeList() {
        return queryMenuList();
    }


    /**
     * 对象实体类转换
     *
     * @param rcMenu
     * @return
     */
    private ContractsTypeVO rcMenuTORcMenuVO(ContractsTypeDO rcMenu) {
        ContractsTypeVO rcMenuVO = new ContractsTypeVO();
        org.springframework.beans.BeanUtils.copyProperties(rcMenu, rcMenuVO);
        return rcMenuVO;
    }


    /**
     * 根据一级递归调用子级
     * reversed方法 表示数字越大靠前
     *
     * @param menuList
     * @param rootMenuList
     * @return
     */
    private void findSubCategory(List<ContractsTypeDO> menuList, List<ContractsTypeVO> rootMenuList) {
        // 遍历一级
        for (ContractsTypeVO rcMenuVO : rootMenuList) {
            List<ContractsTypeVO> rcMenuVOList = new ArrayList<>();
            // 查找子级
            for (ContractsTypeDO rcMenu : menuList) {
                // 判断当前目录是否是子父级关系
                if (Objects.equals(rcMenu.getParentId(), Convert.toInt(rcMenuVO.getId()))) {
                    rcMenuVOList.add(rcMenuTORcMenuVO(rcMenu));
                }
                // 递归调用，不管有几级菜单，都能够适用
                findSubCategory(menuList, rcMenuVOList);
                // 类目显示排序，
                //rcMenuVOList.sort(Comparator.comparing(ContractsTypeVO::getSortOrder));
            }
            // 最后把查到的子级保存到一级目录中
            rcMenuVO.setChildren(rcMenuVOList);
        }
    }

    /**
     * 各级菜单列表展示
     *
     * @return
     */
    public List<ContractsTypeVO> queryMenuList() {
        // List<ContractsTypeDO> menuList = null;
        //Page<ContractsTypeDO> page = PageHelper.startPage(pageObject.getPageNum(), pageObject.getPageSize());
        // 将用户页面提交的参数拷贝到查询条件去
        // SpringBeanUtils.copyPropertiesIgnoreNull(pageObject, page);
        //先查出全部菜单
        // menuList = menuMapper.queryMenuList(menu);
        List<ContractsTypeDO> contractsTypeDOList = contractsTypeMapper.selectList();
        //获取一级菜单	0代表一级菜单 .reversed()
        List<ContractsTypeVO> rootMenuList = contractsTypeDOList.stream()
                .filter(e -> e.getParentId().equals(0))
                .map(this::rcMenuTORcMenuVO)
                //.sorted(Comparator.comparing(ContractsTypeVO::getSortOrder))
                .collect(Collectors.toList());
        //查找字节点
        findSubCategory(contractsTypeDOList, rootMenuList);

        // 将真实分页信息拷贝到用户页面
        //SpringBeanUtils.copyPropertiesIgnoreNull(page, pageObject);
        //返回值
        return rootMenuList;
    }
}