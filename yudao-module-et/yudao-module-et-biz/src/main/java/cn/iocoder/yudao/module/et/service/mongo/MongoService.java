package cn.iocoder.yudao.module.et.service.mongo;


import cn.hutool.core.util.StrUtil;
import cn.jy.soft.remote.bean.SocketPacket;
import com.mongodb.client.result.DeleteResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MongoService {

    @Autowired
    private MongoTemplate mongoTemplate;

    public void getById() {
        System.out.println("...........................");
        SocketPacket packet = this.getByID("674803039acd794133dddd15");
        System.out.println(packet.toString());

        List<SocketPacket> list = this.findByBizCode("GS-B-271");
        System.out.println(list.size());

        list = this.findByLimit(100);
        list.stream().forEach(System.out::println);
        System.out.println(list.size());

    }

    public SocketPacket getByID(String id) {
        return mongoTemplate.findById(id, SocketPacket.class);
    }

    public List<SocketPacket> findByLimit(int limit) {
        Query query = new Query();
        query.limit(limit);
        List<SocketPacket> list = mongoTemplate.find(query, SocketPacket.class);
        System.out.println(list.size());
        return list;
    }

    public List<SocketPacket> findByBizCode(String bizCode) {
        Query query = Query.query(Criteria.where("bizCode").is(bizCode))
                .with(Sort.by(Sort.Direction.ASC, "gatherTime"));
        List<SocketPacket> list = mongoTemplate.find(query, SocketPacket.class);
        System.out.println(list.size());
        return list;
    }

    public SocketPacket move2new(SocketPacket packet, String collName, String newCollName) {
        SocketPacket newPacket = null;
        if(packet != null && StrUtil.isNotBlank(packet.getId())) {
            newPacket = mongoTemplate.insert(packet, newCollName);
            DeleteResult result = mongoTemplate.remove(packet, collName);
            System.out.println(StrUtil.format("mongodb中删除记录：{}", result.getDeletedCount()));
        }
        return newPacket;
    }

}
