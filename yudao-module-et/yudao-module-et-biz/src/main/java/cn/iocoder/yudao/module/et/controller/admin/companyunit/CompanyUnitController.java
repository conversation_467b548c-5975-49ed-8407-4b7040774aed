package cn.iocoder.yudao.module.et.controller.admin.companyunit;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.et.controller.admin.companyunit.vo.CompanyUnitRespVO;
import cn.iocoder.yudao.module.et.service.companyunit.CompanyUnitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.et.enums.ErrorCodeConstants.TIME_RANGE_PARAMS_LENGTH_ERROR;

@Tag(name = "电力交易辅助 - 区域公司（披露数据）")
@RestController
@RequestMapping("/et/companyUnit")
@Validated
public class CompanyUnitController {

    @Resource
    private CompanyUnitService companyUnitService;

    /**
     * 市场概览 数据查询
     */
    @GetMapping("/marketOverview")
    @Operation(summary = "市场概览查询 省级披露数据")
    @PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Object> getMarketOverview(@NotBlank String timeRange) {
        HashMap<String, Object> resultMap = new HashMap<>();

        String[] timeArr = timeRange.split(",");
        if (timeArr.length != 2) {
            return CommonResult.error(TIME_RANGE_PARAMS_LENGTH_ERROR);
        }
        Timestamp startTs = new Timestamp(DateUtil.parse(timeArr[0]).getTime() + 1000);
        Timestamp endTs = new Timestamp(DateUtil.parse(timeArr[1] + " 23:59:59").getTime() + 2000);
        String companyId = companyUnitService.getCurrentUserOrgCode();
        //查询省级披露数据
        resultMap = companyUnitService.queryProvincialLevelData(companyId, startTs, endTs);

        resultMap = companyUnitService.getDongLiMeiPrice(resultMap, companyId, timeArr);

        resultMap = companyUnitService.getMustOpenAndStopUnit(resultMap, companyId, timeArr);

        // 使用Calendar类来添加天数
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtil.parse(timeArr[0] + " 23:59:59")); // 将Timestamp转换为Date对象
        calendar.add(Calendar.DAY_OF_MONTH, 6); // 添加7天

        // 将Calendar对象转换回Timestamp
        Timestamp newTimestamp = new Timestamp(calendar.getTimeInMillis());
        resultMap = companyUnitService.getWeatherInfo(resultMap, startTs, newTimestamp);

        return CommonResult.success(resultMap);
    }

    /**
     * 市场概览 数据查询
     */
    @PostMapping("/getPriceBiddingSpaceRends")
    @Operation(summary = "市场概览查询 省级披露数据")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, Object>> getPriceBiddingSpaceRends(@RequestBody CompanyUnitRespVO companyUnitRespVO) {

        Map<String, Object> resultMap = companyUnitService.getPriceBiddingSpaceRends(companyUnitRespVO.getDate());

        return success(resultMap);
    }

    /**
     * 现货价差 价差表现
     */
    @PostMapping("/getNodePriceDifference")
    @Operation(summary = "现货价差 价差表现")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, Object>> getNodePriceDifference(@RequestBody CompanyUnitRespVO companyUnitRespVO) {

        Map<String, Object> resultMap = companyUnitService.getNodePriceDifference(companyUnitRespVO.getDate());

        return success(resultMap);

    }

    /**
     * 现货价差 价差解释
     */
    @PostMapping("/getPriceDifferenceExplanation")
    @Operation(summary = "现货价差 价差解释")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, Object>> getPriceDifferenceExplanation(@RequestBody CompanyUnitRespVO companyUnitRespVO) {

        Map<String, Object> resultMap = companyUnitService.getPriceDifferenceExplanation(companyUnitRespVO.getDate());

        return success(resultMap);
    }

    /**
     * 现货价差 价差分布
     */
    @PostMapping("/getPriceDifferenceDistribution")
    @Operation(summary = "现货价差 价差分布")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, Object>> getPriceDifferenceDistribution(@RequestBody CompanyUnitRespVO companyUnitRespVO) {

        Map<String, Object> resultMap = companyUnitService.getPriceDifferenceDistribution(companyUnitRespVO.getDate());

        return success(resultMap);
    }

    /**
     * 现货价差 价差原因
     */
    @PostMapping("/getPriceDifferenceReason")
    @Operation(summary = "现货价差 价差原因")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<List<Map<String, Double>>> getPriceDifferenceReason(@RequestBody CompanyUnitRespVO companyUnitRespVO) {
        List<Map<String, Double>> resultMap = companyUnitService.getPriceDifferenceReason(companyUnitRespVO.getDate());
        return success(resultMap);
    }


    /**
     * 市场动态-新能源总出力
     */
    @PostMapping("/getTotalOutputOfNewEnergy")
    @Operation(summary = "市场动态-新能源总出力")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, List<String>>> getTotalOutputOfNewEnergy(@RequestBody CompanyUnitRespVO companyUnitRespVO) {
        Map<String, List<String>> resultMap = companyUnitService.getTotalOutputOfNewEnergy(companyUnitRespVO.getDate());
        return success(resultMap);
    }

    /**
     * 市场动态-实际负荷及系统备用
     */
    @PostMapping("/getUpwardRotation")
    @Operation(summary = "市场动态-实际负荷及系统备用")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, List<String>>> getUpwardRotation(@RequestBody CompanyUnitRespVO companyUnitRespVO) {
        Map<String, List<String>> resultMap = companyUnitService.getUpwardRotation(companyUnitRespVO.getDate(), companyUnitRespVO.getDataSelectTypeOption());
        return success(resultMap);
    }

    /**
     * 市场动态-省间联络线输电曲线预测
     */
    @PostMapping("/getLlx")
    @Operation(summary = "市场动态-省间联络线输电曲线预测")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, List<Float>>> geLlx(@RequestBody CompanyUnitRespVO companyUnitRespVO) {
        Map<String, List<Float>> resultMap = companyUnitService.getLlx(companyUnitRespVO.getDate(), companyUnitRespVO.getDataSelectTypeOption());
        return success(resultMap);
    }
    
    /**
     * 市场分析-必开必停机组信息
     */
    @PostMapping("/getBiKaiBiTing")
    @Operation(summary = "市场分析-必开必停机组信息")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult getBiKaiBiTing(@RequestBody CompanyUnitRespVO companyUnitRespVO) {
        return success(companyUnitService.getBiKaiBiTing(companyUnitRespVO));
    }
  /**
     * 市场分析-必开必停机组电厂选择器
     */
    @PostMapping("/getPowerPlantNames")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult getPowerPlantNames(@RequestBody CompanyUnitRespVO companyUnitRespVO) {
        return success(companyUnitService.getPowerPlantNames(companyUnitRespVO));
    }

    /**
     * 市场动态-分区价格
     */
    @PostMapping("/getPartitionPrice")
    @Operation(summary = "市场分析-必开必停机组信息")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<List<List<Object>>> getPartitionPrice(@RequestBody CompanyUnitRespVO companyUnitRespVO) {
        List<List<Object>> result = companyUnitService.getPartitionPrice(companyUnitRespVO.getDate(), companyUnitRespVO.getDataSelectTypeOption());
        return success(result);
    }

    /**
     * 市场动态-结算价格
     */
    @PostMapping("/getSettlementPrice")
    @Operation(summary = "市场分析-结算价格")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<List<List<Object>>> getSettlementPrice(@RequestBody CompanyUnitRespVO companyUnitRespVO) {
        List<List<Object>> result = companyUnitService.getSettlementPrice(companyUnitRespVO.getDate());
        return success(result);
    }

    /**
     * 市场分析-现货价格-统一结算点节点价格
     */
    @PostMapping("/getUnifiedSettlementPointPrice")
    @Operation(summary = "市场分析-现货价格-统一结算点节点价格")
    //@PreAuthorize("@ss.hasPermission('et:companyUnit_marketOverview:query')")
    public CommonResult<Map<String, Object>> getUnifiedSettlementPointPrice(@RequestBody CompanyUnitRespVO companyUnitRespVO) {
        Map<String, Object> result = companyUnitService.getUnifiedSettlementPointPrice(companyUnitRespVO.getMonthDate(), companyUnitRespVO.getDate(), companyUnitRespVO.getDataSelectTypeOption());
        return success(result);
    }


}