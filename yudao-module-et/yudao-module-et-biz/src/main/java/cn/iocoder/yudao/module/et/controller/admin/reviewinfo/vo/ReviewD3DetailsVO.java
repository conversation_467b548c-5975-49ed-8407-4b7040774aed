package cn.iocoder.yudao.module.et.controller.admin.reviewinfo.vo;

import lombok.Data;

import java.util.Date;

/**
 * 日滚动D+3 复盘内容表
 */
@Data
public class ReviewD3DetailsVO {
    private Integer id;
    /**
     * 复盘列表id
     */
    private Integer reviewInfoId;

    /**
     * 复盘日期
     */
    private Date reviewDate;

    /**
     * 天气信息表格
     */
    private String weatherInfo;

    /**
     * 节点电价曲线图数据
     */
    private String locationMarginalPriceData;

    /**
     * 节点电价复盘描述
     */
    private String locationMarginalPriceInfo;

    /**
     * 交易策略表格数据
     */
    private String dailyRollingResultData;

    /**
     * 交易策略复盘描述
     */
    private String dailyRollingResultInfo;

    /**
     * 今日电价走势
     */
    private String priceInfo;

    /**
     * 存量项目补充情况
     */
    private String stockProjectInfo;

    /**
     * 平价项目补充情况
     */
    private String parityProjectInfo;

    /**
     * 其他交易工作
     */
    private String otherInfo;

    /**
     * 今日运行人员
     */
    private String operatingDayPersons;

    /**
     * 今日值班人员
     */
    private String dutyPersons;
}
