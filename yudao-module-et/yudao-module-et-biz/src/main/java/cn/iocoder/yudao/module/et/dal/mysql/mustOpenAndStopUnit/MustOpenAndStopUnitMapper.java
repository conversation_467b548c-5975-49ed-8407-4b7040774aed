package cn.iocoder.yudao.module.et.dal.mysql.mustOpenAndStopUnit;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.companyunit.vo.CompanyUnitRespVO;
import cn.iocoder.yudao.module.et.dal.dataobject.mustOpenAndStopUnit.StartupAndDownUnitDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 必开必停机组信息 Mapper
 *
 * <AUTHOR>
 */
@DS("shardingsphereDB")
@Mapper
public interface MustOpenAndStopUnitMapper extends BaseMapperX<StartupAndDownUnitDO> {


    @TenantIgnore
    default List<StartupAndDownUnitDO> getMustOpenAndStopUnit(String startDay, String endDay) {
        return selectList(new LambdaQueryWrapperX<StartupAndDownUnitDO>()
                .geIfPresent(StartupAndDownUnitDO::getTs, startDay)
                .leIfPresent(StartupAndDownUnitDO::getTs, endDay));
    }

    @TenantIgnore
    IPage<StartupAndDownUnitDO> getBiKaiBiTing(Page<StartupAndDownUnitDO> startupAndDownUnitDOPage, @Param("bo") CompanyUnitRespVO companyUnitRespVO);

    @TenantIgnore
    List<Map> getPowerPlantNames();

}
