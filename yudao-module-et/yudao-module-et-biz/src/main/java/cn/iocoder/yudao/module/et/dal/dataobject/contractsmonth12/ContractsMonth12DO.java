package cn.iocoder.yudao.module.et.dal.dataobject.contractsmonth12;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合同分月信息 DO
 *
 * <AUTHOR>
 */
@TableName("jy_contracts_month12")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractsMonth12DO {

    /**
     * 自增主键
     */
    @TableId
    private Long id;
    /**
     * 分月电量
     */
    private BigDecimal quantity;
    /**
     * 分月电价
     */
    private BigDecimal price;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherDate;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
    /**
     * 开始日期
     */
    private LocalDateTime beginTime;
    /**
     * 结束日期
     */
    private LocalDateTime endTime;
    /**
     * 合同id
     */
    private String contractsId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;
}