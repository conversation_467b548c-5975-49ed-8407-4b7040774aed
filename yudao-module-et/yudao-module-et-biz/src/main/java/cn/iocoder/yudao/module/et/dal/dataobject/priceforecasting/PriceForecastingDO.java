package cn.iocoder.yudao.module.et.dal.dataobject.priceforecasting;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.sql.Timestamp;

@TableName("price_forecast")
@KeySequence("price_forecast_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PriceForecastingDO {

    /**
     * 时间
     */
    private Timestamp ts;

    /**
     * 正常算法
     */
    private Float normal;
    /**
     * 实时
     */
    private Float realNormal;
    /**
     * 交叉算法
     */
    private Float cross;

    /**
     * 所属地区
     */
    private String area;

    public String getNormal() {
        if (null == normal) {
            return "--";
        } else {
            return String.format("%.3f", normal);
        }
    }

    public String getRealNormal() {
        if (null == realNormal) {
            return "--";
        } else {
            return String.format("%.3f", realNormal);
        }
    }

    public String getCross() {
        if (null == cross) {
            return "--";
        } else {
            return String.format("%.3f", cross);
        }
    }

}