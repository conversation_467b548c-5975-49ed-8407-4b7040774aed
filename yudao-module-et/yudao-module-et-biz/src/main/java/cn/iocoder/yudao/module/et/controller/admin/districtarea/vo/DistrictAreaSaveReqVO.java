package cn.iocoder.yudao.module.et.controller.admin.districtarea.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 省份对应算法类型新增/修改 Request VO")
@Data
public class DistrictAreaSaveReqVO {

    @Schema(description = "区域代码，甘肃-河东、甘肃-河西、内蒙古-蒙东", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "区域简称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "区域简称不能为空")
    private String name;

    @Schema(description = "区域全称", example = "赵六")
    private String fullName;

    @Schema(description = "所属行政区代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "所属行政区代码不能为空")
    private String districtCode;

    @Schema(description = "算法，normal：正位预测算法；cross：错位预测算法")
    private String predictionModel;

}