package cn.iocoder.yudao.module.et.service.priceforecast;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

/**
 * 区域公司 Service 接口
 *
 * <AUTHOR>
 */
public interface PriceForecastService {

    Map<String, Object> getPriceForecastAnalysis(Long[] date, Long[] similarDate, String[] ids);

    Map<String, Object> getPriceForecast(Long[] date, String[] ids);


    void exportPrice(HttpServletResponse response, HttpServletRequest request, Long[] date, String[] ids);
}
