package cn.iocoder.yudao.module.et.service.dataproc.intra;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.et.dal.dataobject.companyunit.CompanyUnitDO;
import cn.iocoder.yudao.module.et.dal.tdengine.dataproc.PublicDataProcMapper;
import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.jy.soft.remote.bean.SocketPacket;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;

/**
 * GS-B-221 实时市场事前发布信息-超短期系统负荷预测
 * 每15分钟，生成未来4小时数据
 *
 * <AUTHOR>
 * @date 2024-11-11
 **/
@Slf4j
@Service
@AllArgsConstructor
public class UstLoadForecastingProc implements BaseDataProc {

    private final PublicDataProcMapper publicDataProcMapper;

    @Override
    public boolean execute(SocketPacket dto) {
        long runtime = System.currentTimeMillis();
        try {
            TenantUtils.execute(dto.getTenantId(), () -> {
                JSONObject jsonObject = JSONUtil.parseObj(dto.getData());
                List<CompanyUnitDO> companyUnitDOList = CollUtil.newArrayList();
                List<CsvRow> rowList;
                CompanyUnitDO entity;
                for (String key : jsonObject.keySet()) {
                    rowList = CsvUtil.getReader(new CsvReadConfig().setBeginLineNo(0).setTrimField(true))
                        .readFromStr(jsonObject.getStr(key)).getRows();
                    if (rowList.size() > 1) {
                        for (int i = 1; i < rowList.size(); i++) {
                            for (int j = 1; j <= 96; j++) {
                                entity = new CompanyUnitDO();
                                // 时间
                                entity.setTs(Timestamp.valueOf(StrUtil.format("{} {}:00", key, rowList.get(0).get(j))));
                                // 超短期系统负荷预测
                                if (StrUtil.isNotBlank(rowList.get(i).get(j))) {
                                    entity.setUltraShortTermForecast(Float.parseFloat(rowList.get(i).get(j)));
                                }
                                companyUnitDOList.add(entity);
                            }
                        }
                    }
                }
                if (CollUtil.isNotEmpty(companyUnitDOList)) {
                    publicDataProcMapper.insertUstLoadForecasting(dto.getOrgCode(), companyUnitDOList);
                    log.info("[{}] > 解析入库完成，共 {} 条，用时 {} ms", dto.getBizCode(), companyUnitDOList.size(),
                        System.currentTimeMillis() - runtime);
                }
            });
        } catch (Exception ex) {
            log.error(StrUtil.format("[{}] > 解析出现异常", dto.getBizCode()), ex);
            return false;
        }
        return true;
    }
}
