package cn.iocoder.yudao.module.et.dal.dataobject.sectionlimitchange;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 断面限额变化
 *
 * <AUTHOR>
 **/
@Deprecated
@TableName("jy_section_limit_change")
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class SectionLimitChangeDO {
    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 日期时间
     */
    private LocalDate bizDateTime;
    /**
     * 断面名称
     */
    private String sectionName;
    /**
     * 断面实际值
     */
    private BigDecimal realtimeValue;
    /**
     * 正向限值
     */
    private BigDecimal directLimit;
    /**
     * 反向限值
     */
    private BigDecimal reverseLimit;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 调度单元编码
     */
    private String dispatchNodeCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherDate;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
}
