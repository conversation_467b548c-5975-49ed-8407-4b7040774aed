package cn.iocoder.yudao.module.et.controller.admin.seriescontracts.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 合同序列新增/修改 Request VO")
@Data
public class SeriesContractsSaveReqVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "4961")
    private Long id;

    @Schema(description = "合同序列名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "合同序列名称不能为空")
    private String name;

    @Schema(description = "合同类型id（主库合同类型表id）", example = "14945")
    private Long contractsTypeId;

    @Schema(description = "交易状态", example = "1")
    private String status;

    @Schema(description = "交易发布")
    private String released;

    @Schema(description = "申报开始时间")
    private LocalDateTime beginTime;

    @Schema(description = "申报截止时间")
    private LocalDateTime endTime;

    @Schema(description = "火电可申报(0:不可申报；1:可申报)")
    private Integer thermalPower;

    @Schema(description = "新能源申报(0:不可申报；1:可申报)")
    private Integer greenPower;

    @Schema(description = "售电公司可申报(0:不可申报；1:可申报)")
    private Integer powerSales;

    @Schema(description = "部门ID", example = "1613")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    private LocalDateTime gatherDate;

    @Schema(description = "修改时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "修改时间不能为空")
    private LocalDateTime modificationTime;

}