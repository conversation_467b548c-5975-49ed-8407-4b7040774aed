package cn.iocoder.yudao.module.et.controller.admin.contractsmonth12.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 合同分月信息新增/修改 Request VO")
@Data
public class ContractsMonth12SaveReqVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10044")
    private Long id;

    @Schema(description = "分月电量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分月电量不能为空")
    private BigDecimal quantity;

    @Schema(description = "分月电价", requiredMode = Schema.RequiredMode.REQUIRED, example = "29264")
    @NotNull(message = "分月电价不能为空")
    private BigDecimal price;

    @Schema(description = "部门ID", example = "4645")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    private LocalDateTime gatherDate;

    @Schema(description = "修改时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "修改时间不能为空")
    private LocalDateTime modificationTime;

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始日期不能为空")
    private LocalDateTime beginTime;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结束日期不能为空")
    private LocalDateTime endTime;

    @Schema(description = "合同id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20843")
    @NotEmpty(message = "合同id不能为空")
    private String contractsId;

}