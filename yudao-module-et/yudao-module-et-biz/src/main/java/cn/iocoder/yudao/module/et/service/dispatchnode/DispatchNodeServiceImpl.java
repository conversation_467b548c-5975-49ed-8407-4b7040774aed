package cn.iocoder.yudao.module.et.service.dispatchnode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.et.controller.admin.dispatchnode.vo.DispatchNodePageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.dispatchnode.vo.DispatchNodeSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.DispatchNodeDO;
import cn.iocoder.yudao.module.et.dal.mysql.dispatchnode.DispatchNodeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.et.enums.ErrorCodeConstants.DISPATCH_NODE_NOT_EXISTS;

/**
 * 交易单元 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DispatchNodeServiceImpl implements DispatchNodeService {

    @Resource
    private DispatchNodeMapper dispatchNodeMapper;

    @Override
    public Integer createDispatchNode(DispatchNodeSaveReqVO createReqVO) {
        // 插入
        DispatchNodeDO dispatchNode = BeanUtils.toBean(createReqVO, DispatchNodeDO.class);
        dispatchNodeMapper.insert(dispatchNode);
        // 返回
        return dispatchNode.getId();
    }

    @Override
    public void updateDispatchNode(DispatchNodeSaveReqVO updateReqVO) {
        // 校验存在
        validateDispatchNodeExists(updateReqVO.getId());
        // 更新
        DispatchNodeDO updateObj = BeanUtils.toBean(updateReqVO, DispatchNodeDO.class);
        dispatchNodeMapper.updateById(updateObj);
    }

    @Override
    public void deleteDispatchNode(Integer id) {
        // 校验存在
        validateDispatchNodeExists(id);
        // 删除
        dispatchNodeMapper.deleteById(id);
    }

    private void validateDispatchNodeExists(Integer id) {
        if (dispatchNodeMapper.selectById(id) == null) {
            throw exception(DISPATCH_NODE_NOT_EXISTS);
        }
    }

    @Override
    public DispatchNodeDO getDispatchNode(Integer id) {
        return dispatchNodeMapper.selectById(id);
    }

    @Override
    public PageResult<DispatchNodeDO> getDispatchNodePage(DispatchNodePageReqVO pageReqVO) {
        return dispatchNodeMapper.selectPage(pageReqVO);
    }

    //根据code查name
    @Override
    public String getNameByCode(String code) {
        return dispatchNodeMapper.selectNameByCode(code);
    }

    //根据code查dispatch_node
    @Override
    public DispatchNodeDO getDispatchNodeByCode(String code) {
        LambdaQueryWrapper<DispatchNodeDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DispatchNodeDO::getCode, code);
        return dispatchNodeMapper.selectOne(queryWrapper);
    }

    @Override
    public List<DispatchNodeDO> getDispatchNodeList() {
        return dispatchNodeMapper.selectList();
    }

}