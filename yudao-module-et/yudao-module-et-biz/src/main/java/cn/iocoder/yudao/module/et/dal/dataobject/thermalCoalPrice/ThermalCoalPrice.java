package cn.iocoder.yudao.module.et.dal.dataobject.thermalCoalPrice;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@TableName("jy_thermal_coal_price")
@KeySequence("jy_thermal_coal_price_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ThermalCoalPrice {
    /**
     * 主键ID
     */
    @TableId
    private Long pkId;
    /**
     * 日期时间
     */
    private LocalDate bizDate;
    /**
     * 描述
     */
    private String description;
    /**
     * 品名
     */
    private String productName;
    /**
     * 规格
     */
    private String specifications;
    /**
     * 产地
     */
    private String producer;
    /**
     * 企业
     */
    private String enterprise;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 涨跌
     */
    private BigDecimal riseAndFall;
    /**
     * 备注
     */
    private String remark;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;

    /**
     * 页面展示数据需要
     *
     * @return
     */
    public BigDecimal getPriceForPage() {
        if (price.floatValue() < 0) {
            return BigDecimal.ZERO;
        }
        return price;
    }
}
