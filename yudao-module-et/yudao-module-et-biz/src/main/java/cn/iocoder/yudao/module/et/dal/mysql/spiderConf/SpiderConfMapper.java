package cn.iocoder.yudao.module.et.dal.mysql.spiderConf;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.spiderconf.vo.SpiderConfPageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.spiderConf.SpiderConfDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 爬取页面解析类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpiderConfMapper extends BaseMapperX<SpiderConfDO> {

    default PageResult<SpiderConfDO> selectPage(SpiderConfPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpiderConfDO>()
                .eqIfPresent(SpiderConfDO::getSpiderCode, reqVO.getSpiderCode())
                .likeIfPresent(SpiderConfDO::getSpiderName, reqVO.getSpiderName())
                .eqIfPresent(SpiderConfDO::getSpiderSystem, reqVO.getSpiderSystem())
                .eqIfPresent(SpiderConfDO::getClzImpl, reqVO.getClzImpl())
                .eqIfPresent(SpiderConfDO::getEnabled, reqVO.getEnabled())
                .betweenIfPresent(SpiderConfDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SpiderConfDO::getId));
    }

    @TenantIgnore
    default List<SpiderConfDO> findAll() {
        return this.selectList();
    }

    @Select("select * from jy_spider_conf where clz_impl != '' and clz_impl is not null and enabled = true and deleted = false")
    @TenantIgnore
    List<SpiderConfDO> findAllByUse();

    @Select("select * from jy_spider_conf where tenant_id = #{tenantId}")
    @TenantIgnore
    List<SpiderConfDO> findByTenantId(@Param("tenant_id") Long tenantId);

    @Select("select * from jy_spider_conf where tenant_id = #{tenantId} and biz_code = #{bizCode}")
    @TenantIgnore
    SpiderConfDO findByTidAndBizcode(@Param("tenant_id") Long tenantId, @Param("biz_code") String bizCode);


}
