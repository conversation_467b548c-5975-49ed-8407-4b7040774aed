package cn.iocoder.yudao.module.et.controller.admin.contractstype.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.List;

/**
 * 合同类型 VO
 *
 * <AUTHOR>
 */
@Data
public class ContractsTypeVO {

    /**
     * 自增主键
     */
    @TableId
    private Long id;
    /**
     * 合同类型名称
     */
    private String name;
    /**
     * 合同类型代码
     */
    private String code;
    /**
     * 父节点id
     */
    private Integer parentId;

    //存放子菜单目录
    private List<ContractsTypeVO> children;

}