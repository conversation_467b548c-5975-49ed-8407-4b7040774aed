package cn.iocoder.yudao.module.et.controller.admin.companyunit.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.net.ntp.TimeStamp;

@Schema(description = "管理后台 - 区域公司分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CompanyUnitPageReqVO extends PageParam {

    /**
     * 时间
     */
    private TimeStamp ts;
    /**
     * 公司id
     */
    private String companyId;

    /**
     * 新能源负荷预测
     */
    private float loadForecast;

    /**
     * 系统负荷-B59联络线
     */
    private float systemForecastB59;

    /**
     * 水电发电计划
     */
    private float hydroelectricPowerGenerationPlan;

    /**
     * 新能源发电预测
     */
    private float powerGenerationForecast;

    /**
     * 火电量小
     */
    private float thermalPowerSmall;

    /**
     * 火电可调
     */
    private float thermalPowerAdjustable;

    /**
     * 供需比
     */
    private float supplyDemandRatio;

    /**
     * 竞价空间
     */
    private float biddingSpace;

    /**
     * 超短期预测
     */
    private float ultraShortTermForecast;

    /**
     * 日前统一结算点分区价格-河东
     */
    private float hisUnifiedSettlementPointPartitionPricesHd;

    /**
     * 实时统一结算点分区价格-河东
     */
    private float realUnifiedSettlementPointPartitionPricesHd;

    /**
     * 日前统一结算点分区价格-河西
     */
    private float hisUnifiedSettlementPointPartitionPricesHx;

    /**
     * 实时统一结算点分区价格-河西
     */
    private float realUnifiedSettlementPointPartitionPricesHx;

    /**
     * 日前统一结算点价格
     */
    private float hisUnifiedSettlementPointPrice;

    /**
     * 实时统一结算点价格
     */
    private float realUnifiedSettlementPointPrice;

    /**
     * 日前风电
     */
    private float hisWindPower;

    /**
     * 日前光电
     */
    private float hisPhotovoltaicPower;

    /**
     * 甘肃_新疆
     */
    private float llxGsXj;

    /**
     * 甘肃_青海
     */
    private float llxGsQh;

    /**
     * 甘肃_宁夏
     */
    private float llxGsNx;

    /**
     * 甘肃_陕西
     */
    private float llxGsSx;

    /**
     * 甘肃_湖南
     */
    private float llxGsHn;

    /**
     * 实时风电
     */
    private float realWindPower;

    /**
     * 实时光电
     */
    private float realPhotovoltaicPower;

    /**
     * 省火上旋
     */
    private float provincialThermalPowerUpwardRotation;

    /**
     * 省水上旋
     */
    private float hydroelectricPowerUpwardRotation;

    /**
     * 电厂名称
     */
    private float stationName;

    /**
     * 必开机组台数
     */
    private float necessaryStartUpNumber;

    /**
     * 电压等级
     */
    private float voltageLevel;

    /**
     * 原因
     */
    private float reason;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private float endDate;

}