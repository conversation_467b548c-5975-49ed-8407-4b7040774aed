package cn.iocoder.yudao.module.et.dal.dataobject.tradingmarket;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 交易 DO
 *
 * <AUTHOR>
 */
@TableName("jy_trading_market")
@KeySequence("jy_trading_market_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradingMarketDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 交易中心唯一编码
     */
    private String code;
    /**
     * 交易市场简称
     */
    private String name;
    /**
     * 交易市场全称
     */
    private String fullName;
    /**
     * 交易市场所在省份
     */
    private Integer districtCode;
    /**
     * 所在区域分组，东北、华北、华东……
     */
    private String groupName;
    /**
     * 算法，normal：正位预测算法；cross：错位预测算法
     */
    private String predictionModel;

}