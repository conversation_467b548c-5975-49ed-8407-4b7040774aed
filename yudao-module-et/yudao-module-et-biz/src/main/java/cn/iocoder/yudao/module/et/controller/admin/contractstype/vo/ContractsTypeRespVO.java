package cn.iocoder.yudao.module.et.controller.admin.contractstype.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 合同类型 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContractsTypeRespVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "13492")
    @ExcelProperty("自增主键")
    private Long id;

    @Schema(description = "合同类型名称", example = "赵六")
    @ExcelProperty("合同类型名称")
    private String name;

    @Schema(description = "合同类型代码")
    @ExcelProperty("合同类型代码")
    private String code;

    @Schema(description = "父节点id", example = "4632")
    @ExcelProperty("父节点id")
    private Integer parentId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    //存放子菜单目录
    private List<ContractsTypeVO> children;
}