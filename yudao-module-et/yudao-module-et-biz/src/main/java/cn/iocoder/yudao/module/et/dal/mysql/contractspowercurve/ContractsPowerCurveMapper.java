package cn.iocoder.yudao.module.et.dal.mysql.contractspowercurve;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.et.controller.admin.contractspowercurve.vo.ContractsPowerCurvePageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractspowercurve.ContractsPowerCurveDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 电力曲线 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("shardingsphereDB")
public interface ContractsPowerCurveMapper extends BaseMapperX<ContractsPowerCurveDO> {

    default PageResult<ContractsPowerCurveDO> selectPage(ContractsPowerCurvePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContractsPowerCurveDO>()
                .eqIfPresent(ContractsPowerCurveDO::getCurveType, reqVO.getCurveType())
                .likeIfPresent(ContractsPowerCurveDO::getMemberName, reqVO.getMemberName())
                .betweenIfPresent(ContractsPowerCurveDO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(ContractsPowerCurveDO::getEndTime, reqVO.getEndTime())
                .betweenIfPresent(ContractsPowerCurveDO::getRunDate, reqVO.getRunDate())
                .eqIfPresent(ContractsPowerCurveDO::getQuantity, reqVO.getQuantity())
                .eqIfPresent(ContractsPowerCurveDO::getPoints, reqVO.getPoints())
                .eqIfPresent(ContractsPowerCurveDO::getContractsId, reqVO.getContractsId())
                .eqIfPresent(ContractsPowerCurveDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(ContractsPowerCurveDO::getOrgCode, reqVO.getOrgCode())
                .betweenIfPresent(ContractsPowerCurveDO::getGatherDate, reqVO.getGatherDate())
                .betweenIfPresent(ContractsPowerCurveDO::getModificationTime, reqVO.getModificationTime())
                .orderByDesc(ContractsPowerCurveDO::getId));
    }

    @Select("SELECT * FROM jy_contracts_power_curve WHERE contracts_id = #{id} and deleted = 0 order by row_no asc ")
    List<ContractsPowerCurveDO> selectListByContractsId(String id);
}