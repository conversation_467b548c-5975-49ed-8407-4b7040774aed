package cn.iocoder.yudao.module.et.controller.admin.districtcode.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 行政区划 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DistrictCodeRespVO {

    @Schema(description = "行政区划代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("行政区划代码")
    private Integer code;

    @Schema(description = "名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("名字")
    private String name;

    @Schema(description = "等级:1-省级;2-地级市;3-区/县;4-乡/镇", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("等级:1-省级;2-地级市;3-区/县;4-乡/镇")
    private Integer level;

    @Schema(description = "类型:1-省;2-自治区;3-直辖市;4-特别行政区;5-地级市;6-地区;7-自治州;8-盟;9-市辖区;10-县;11- 县级市;12-自治县;13-旗;14-自治旗;15-特区;16-林区", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("类型:1-省;2-自治区;3-直辖市;4-特别行政区;5-地级市;6-地区;7-自治州;8-盟;9-市辖区;10-县;11- 县级市;12-自治县;13-旗;14-自治旗;15-特区;16-林区")
    private Integer type;

    @Schema(description = "简称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("简称")
    private String abname;

    @Schema(description = "所属行政区划代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "17855")
    @ExcelProperty("所属行政区划代码")
    private Integer pid;

    @Schema(description = "所属行政区划名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("所属行政区划名字")
    private String pname;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("备注")
    private String note;

    @Schema(description = "纬度")
    @ExcelProperty("纬度")
    private Double lat;

    @Schema(description = "经度")
    @ExcelProperty("经度")
    private Double lng;

}