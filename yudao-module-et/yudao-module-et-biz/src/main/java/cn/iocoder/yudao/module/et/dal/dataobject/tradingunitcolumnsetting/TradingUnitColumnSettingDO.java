package cn.iocoder.yudao.module.et.dal.dataobject.tradingunitcolumnsetting;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 交易单元月统计配置统计字段表 DO
 *
 * <AUTHOR>
 */
@TableName("jy_trading_unit_column_setting")
@KeySequence("jy_trading_unit_column_setting_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradingUnitColumnSettingDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 统计类型
     */
    private String statisticType;
    /**
     * 统计周期
     */
    private Integer statisticPeriod;
    /**
     * 聚合方式(sum,avg,max,min)
     */
    private String statisticMode;

}