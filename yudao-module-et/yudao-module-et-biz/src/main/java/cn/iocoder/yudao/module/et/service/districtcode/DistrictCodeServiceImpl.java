package cn.iocoder.yudao.module.et.service.districtcode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.et.controller.admin.districtcode.vo.DistrictCodePageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.districtcode.vo.DistrictCodeSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.districtcode.DistrictCodeDO;
import cn.iocoder.yudao.module.et.dal.mysql.districtcode.DistrictCodeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.et.enums.ErrorCodeConstants.DISTRICT_CODE_NOT_EXISTS;

/**
 * 行政区划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DistrictCodeServiceImpl implements DistrictCodeService {

    @Resource
    private DistrictCodeMapper districtCodeMapper;

    @Override
    public Integer createDistrictCode(DistrictCodeSaveReqVO createReqVO) {
        // 插入
        DistrictCodeDO districtCode = BeanUtils.toBean(createReqVO, DistrictCodeDO.class);
        districtCodeMapper.insert(districtCode);
        // 返回
        return districtCode.getCode();
    }

    @Override
    public void updateDistrictCode(DistrictCodeSaveReqVO updateReqVO) {
        // 校验存在
        validateDistrictCodeExists(updateReqVO.getCode());
        // 更新
        DistrictCodeDO updateObj = BeanUtils.toBean(updateReqVO, DistrictCodeDO.class);
        districtCodeMapper.updateById(updateObj);
    }

    @Override
    public void deleteDistrictCode(Integer id) {
        // 校验存在
        validateDistrictCodeExists(id);
        // 删除
        districtCodeMapper.deleteById(id);
    }

    private void validateDistrictCodeExists(Integer id) {
        if (districtCodeMapper.selectById(id) == null) {
            throw exception(DISTRICT_CODE_NOT_EXISTS);
        }
    }

    @Override
    public DistrictCodeDO getDistrictCode(Integer id) {
        return districtCodeMapper.selectById(id);
    }

    @Override
    public PageResult<DistrictCodeDO> getDistrictCodePage(DistrictCodePageReqVO pageReqVO) {
        return districtCodeMapper.selectPage(pageReqVO);
    }

    /**
     * @return
     */
    @Override
    public List<DistrictCodeDO> getDistrictCodeTree(String number) {
        return districtCodeMapper.selectListByLikeCodeId(number);
    }

    @Override
    public List<DistrictCodeDO> getDistrictCodeByPid(Integer pid) {
        if (pid != null) {
            LambdaQueryWrapper<DistrictCodeDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DistrictCodeDO::getPid, pid);
            return districtCodeMapper.selectList(queryWrapper);
        }
        return List.of();
    }

}