package cn.iocoder.yudao.module.et.controller.admin.contractsequipment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 合同机组信息新增/修改 Request VO")
@Data
public class ContractsEquipmentSaveReqVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "27273")
    private Long id;

    @Schema(description = "合同角色", example = "王五")
    private String contractsRoleName;

    @Schema(description = "机组名称", example = "张三")
    private String equipmentName;

    @Schema(description = "机组类型", example = "2")
    private String equipmentType;

    @Schema(description = "装机容量")
    private BigDecimal quantity;

    @Schema(description = "批复电价", example = "580")
    private BigDecimal approvedPrice;

    @Schema(description = "机组电量")
    private BigDecimal equipmentGen;

    @Schema(description = "时段名称", example = "王五")
    private String timeSlotName;

    @Schema(description = "时段起止时间")
    private String timeSlotRange;

    @Schema(description = "部门ID", example = "5428")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    private LocalDateTime gatherDate;

    @Schema(description = "修改时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "修改时间不能为空")
    private LocalDateTime modificationTime;

    @Schema(description = "开始日期")
    private LocalDateTime beginTime;

    @Schema(description = "结束日期")
    private LocalDateTime endTime;

    @Schema(description = "合同id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1244")
    @NotEmpty(message = "合同id不能为空")
    private String contractsId;

}