package cn.iocoder.yudao.module.et.dal.mysql.contractstype;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.et.controller.admin.contractstype.vo.ContractsTypePageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstype.ContractsTypeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 合同类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContractsTypeMapper extends BaseMapperX<ContractsTypeDO> {

    default PageResult<ContractsTypeDO> selectPage(ContractsTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContractsTypeDO>()
                .likeIfPresent(ContractsTypeDO::getName, reqVO.getName())
                .eqIfPresent(ContractsTypeDO::getCode, reqVO.getCode())
                .eqIfPresent(ContractsTypeDO::getParentId, reqVO.getParentId())
                .betweenIfPresent(ContractsTypeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ContractsTypeDO::getId));
    }

}