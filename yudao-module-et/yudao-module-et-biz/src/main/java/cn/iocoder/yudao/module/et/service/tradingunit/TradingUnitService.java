package cn.iocoder.yudao.module.et.service.tradingunit;

import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;
import java.util.Map;

/**
 * 交易单元 Service 接口
 *
 * <AUTHOR>
 */
public interface TradingUnitService {


    Map<String, List<String>> getDeviation(String unitId, Long[] date);

    Map<String, Object> getRealTimePriceComparison(String unitId, String monthDate, Long[] date, String dataSelectTypeOption);

    Map<String, Object> getRealTimePriceCompare(String unitId, String monthDate, Long[] date);

    Map<String, Object> getTransactionOverview(String[] ids, Long[] date);

    //发电量分析
    Map<String, Object> getPowerGeneration(String unitId, String monthDate, Long[] date);

    //交易日历
    Map<String, Object> getTradingCalendar(String unitId, String monthDate);

    //交易复盘分析
    Map<String, Object> getReviewAnalysis(String[] ids, String monthDate, Long[] date);

    List<List<Object>> getCurrentPrices(String[] ids, Long[] date, String dataSelectTypeOption);

    Map<String, Object> getSettlementRelease(String unitId, String monthDate);

    Map<String, Object> getSettlementElectricity(String unitId, Long[] date);

    Map<String, Object> getSettlementReleaseSum(String unitId, String monthDate, Long[] date);

    Map<String, Object> getClearanceMonitor(String unitId, Long[] date);

    Map<String, List<Double>> getRemainingSpace(String unitId, Long[] date);

    Map<String, Object> getCongestionCost(String unitId, Long[] date);

    void downloadExcelTemplate(HttpServletResponse response, HttpServletRequest request, String unitId, Long[] date);

    void exportRemainingSpace(HttpServletResponse response, HttpServletRequest request, Long[] date);

    List<TradingUnitDO> getTradingUnitListByTenantIgnore();
}