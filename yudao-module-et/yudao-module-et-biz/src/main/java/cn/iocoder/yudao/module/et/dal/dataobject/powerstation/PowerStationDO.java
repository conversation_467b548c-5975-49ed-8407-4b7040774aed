package cn.iocoder.yudao.module.et.dal.dataobject.powerstation;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 场站信息 DO
 *
 * <AUTHOR>
 */
@TableName("jy_power_station")
@KeySequence("jy_power_station_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PowerStationDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId
    private Integer id;
    /**
     * 场站编码
     */
    private String code;
    /**
     * 场站简称
     */
    private String name;
    /**
     * 场站全称
     */
    private String fullName;
    /**
     * 场站调度名称
     */
    private String dispatchName;
    /**
     * 气象点号
     */
    private String districtCode;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 场站类型
     */
    private String type;
    /**
     * 场站装机容量
     */
    private Double capacity;
    /**
     * 场站设备台数
     */
    private Integer quantity;
    /**
     * 是否禁用
     */
    private Boolean enabled;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;

}