package cn.iocoder.yudao.module.et.controller.admin.contractspowercurve.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 电力曲线新增/修改 Request VO")
@Data
public class ContractsPowerCurveSaveReqVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19534")
    private Long id;

    @Schema(description = "曲线类型", example = "1")
    private String curveType;

    @Schema(description = "主体信息", example = "芋艿")
    private String memberName;

    @Schema(description = "开始日期")
    private LocalDateTime beginTime;

    @Schema(description = "结束日期")
    private LocalDateTime endTime;

    @Schema(description = "运行日期")
    private LocalDateTime runDate;

    @Schema(description = "日电量")
    private BigDecimal quantity;

    @Schema(description = "曲线点数")
    private String points;

    @Schema(description = "合同id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31435")
    @NotEmpty(message = "合同id不能为空")
    private String contractsId;

    @Schema(description = "部门ID", example = "15821")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    private LocalDateTime gatherDate;

    @Schema(description = "修改时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "修改时间不能为空")
    private LocalDateTime modificationTime;

}