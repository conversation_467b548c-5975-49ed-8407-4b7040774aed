package cn.iocoder.yudao.module.et.controller.admin.contractstype.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 合同类型新增/修改 Request VO")
@Data
public class ContractsTypeSaveReqVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "13492")
    private Long id;

    @Schema(description = "合同类型名称", example = "赵六")
    private String name;

    @Schema(description = "合同类型代码")
    private String code;

    @Schema(description = "父节点id", example = "4632")
    private Integer parentId;

}