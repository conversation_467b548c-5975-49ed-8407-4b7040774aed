package cn.iocoder.yudao.module.et.dal.dataobject.periodicSettlement;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 市场周期结算_电厂
 *
 * <AUTHOR>
 * @date 2024-11-14
 **/
@Accessors(chain = true)
@NoArgsConstructor
@Data
@TableName("jy_periodic_settlement")
public class PeriodicSettlementDO {
    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 结算编号
     */
    private String settlementNumber;
    /**
     * 结算周期名称
     */
    private String settlementCycleName;
    /**
     * 起始时间
     */
    private LocalDate bizDateBegin;
    /**
     * 结束时间
     */
    private LocalDate bizDateEnd;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 调度单元编码（场站编码）
     */
    private String dispatchNodeCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherDate;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
}
