package cn.iocoder.yudao.module.et.service.dataproc.intra;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO;
import cn.iocoder.yudao.module.et.dal.tdengine.dataproc.PrivateDataProcMapper;
import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.jy.soft.remote.bean.SocketPacket;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;

/**
 * GS-B-611 市场结算-结算结果
 *
 * <AUTHOR>
 * @date 2024-11-09
 **/
@Slf4j
@Service
@AllArgsConstructor
public class ClearingResultsProc implements BaseDataProc {

    private final PrivateDataProcMapper privateDataProcMapper;

    @Override
    public boolean execute(SocketPacket dto) {
        long runtime = System.currentTimeMillis();
        try {
            TenantUtils.execute(dto.getTenantId(), () -> {
                JSONObject jsonObject = JSONUtil.parseObj(dto.getData());
                List<TradingUnitDO> tradingUnitDOList = CollUtil.newArrayList();
                TradingUnitDO entity;
                for (String key : jsonObject.keySet()) {
                    for (CsvRow row : CsvUtil.getReader(new CsvReadConfig().setBeginLineNo(2).setTrimField(true))
                        .readFromStr(jsonObject.getJSONObject(key).getStr("detail")).getRows()) {
                        entity = new TradingUnitDO();
                        // 时间
                        entity.setTs(Timestamp.valueOf(StrUtil.format("{} {}:00", key, row.get(0))));
                        // 叠加后中长期(MW.h)
                        if (StrUtil.isNotBlank(row.get(1))) {
                            entity.setMediumLongTermFusion(Float.parseFloat(row.get(1)));
                        }
                        // 日前正现货电量(MW.h)
                        if (StrUtil.isNotBlank(row.get(2))) {
                            entity.setHisPositivePower(Float.parseFloat(row.get(2)));
                        }
                        // 日前负现货电量(MW.h)
                        if (StrUtil.isNotBlank(row.get(3))) {
                            entity.setHisNegativePower(Float.parseFloat(row.get(3)));
                        }
                        // 日前正现货费用(元)
                        if (StrUtil.isNotBlank(row.get(4))) {
                            entity.setHisPositivePrices(Float.parseFloat(row.get(4)));
                        }
                        // 日前负现货费用(元)
                        if (StrUtil.isNotBlank(row.get(5))) {
                            entity.setHisNegativePrices(Float.parseFloat(row.get(5)));
                        }
                        // 日前免结算电量(MW.h)
                        if (StrUtil.isNotBlank(row.get(6))) {
                            entity.setHisSettlementFreePower(Float.parseFloat(row.get(6)));
                        }
                        // 日前发电计划(MW)
                        if (StrUtil.isNotBlank(row.get(7))) {
                            entity.setHisGenerateElectricityPlan(Float.parseFloat(row.get(7)));
                        }
                        // 中长期计划（上网）(MW)
                        if (StrUtil.isNotBlank(row.get(8))) {
                            entity.setMediumLongTermPlan(Float.parseFloat(row.get(8)));
                        }
                        // 日前省间出清(MW)
                        if (StrUtil.isNotBlank(row.get(9))) {
                            entity.setHisProvinceClearance(Float.parseFloat(row.get(9)));
                        }
                        // 日前市场结算价格(元/MW.h)
                        if (StrUtil.isNotBlank(row.get(10))) {
                            entity.setHisMarketSettlementPrices(Float.parseFloat(row.get(10)));
                        }
                        // 实时正现货电量(MW.h)
                        if (StrUtil.isNotBlank(row.get(11))) {
                            entity.setRealPositivePower(Float.parseFloat(row.get(11)));
                        }
                        // 实时负现货电量(MW.h)
                        if (StrUtil.isNotBlank(row.get(12))) {
                            entity.setRealNegativePower(Float.parseFloat(row.get(12)));
                        }
                        // 实时正现货费用(元)
                        if (StrUtil.isNotBlank(row.get(13))) {
                            entity.setRealPositivePrices(Float.parseFloat(row.get(13)));
                        }
                        // 实时负现货费用(元)
                        if (StrUtil.isNotBlank(row.get(14))) {
                            entity.setRealNegativePrices(Float.parseFloat(row.get(14)));
                        }
                        // 实时免结算电量(MW.h)
                        if (StrUtil.isNotBlank(row.get(15))) {
                            entity.setRealSettlementFreePower(Float.parseFloat(row.get(15)));
                        }
                        // 日前发电计划(MW)
                        //    if (StrUtil.isNotBlank(row.get(16))) {
                        //        entity.setHisGenerateElectricityPlan(Float.parseFloat(row.get(16)));
                        //    }
                        // 实时省间出清(MW)
                        if (StrUtil.isNotBlank(row.get(17))) {
                            entity.setRealProvinceClearance(Float.parseFloat(row.get(17)));
                        }
                        // 跨省调峰计划(MW)
                        if (StrUtil.isNotBlank(row.get(18))) {
                            entity.setCrossProvincialPeakLoadBalancingPlan(Float.parseFloat(row.get(18)));
                        }
                        // 跨省调峰返还(MW)
                        if (StrUtil.isNotBlank(row.get(19))) {
                            entity.setCrossProvincialPeakLoadBalancingReturn(Float.parseFloat(row.get(19)));
                        }
                        // 实际结算电量(MW.h)
                        if (StrUtil.isNotBlank(row.get(20))) {
                            entity.setRealSettlementPower(Float.parseFloat(row.get(20)));
                        }
                        // 实时市场结算价格(元/MW.h)
                        if (StrUtil.isNotBlank(row.get(21))) {
                            entity.setRealMarketSettlementPrices(Float.parseFloat(row.get(21)));
                        }
                        // 酒钢开口合同(MW)
                        if (StrUtil.isNotBlank(row.get(22))) {
                            entity.setJiscoBpo(Float.parseFloat(row.get(22)));
                        }
                        // 酒钢双边交易(MW)
                        if (StrUtil.isNotBlank(row.get(23))) {
                            entity.setJiscoBilateralTransactions(Float.parseFloat(row.get(23)));
                        }
                        // 考核电量(MW.h)
                        if (StrUtil.isNotBlank(row.get(24))) {
                            entity.setReviewPower(Float.parseFloat(row.get(24)));
                        }
                        // 偏差考核费用(元)
                        if (StrUtil.isNotBlank(row.get(25))) {
                            entity.setDeviationReviewPrices(Float.parseFloat(row.get(25)));
                        }
                        // 紧急调用开机补偿费用(元)
                        if (StrUtil.isNotBlank(row.get(26))) {
                            entity.setEmergencyInvokeStartUpCompensationPrices(Float.parseFloat(row.get(26)));
                        }
                        // 紧急调用开机上网电量(MW.h)
                        if (StrUtil.isNotBlank(row.get(27))) {
                            entity.setFmergencyInvokeStartUpOnGridPower(Float.parseFloat(row.get(27)));
                        }
                        // 调频增发能量补偿(元)
                        if (StrUtil.isNotBlank(row.get(28))) {
                            entity.setAdjustmentFrequencyAdditionEnergyCompensation(Float.parseFloat(row.get(28)));
                        }
                        // 调频减发能量补偿(元)
                        if (StrUtil.isNotBlank(row.get(29))) {
                            entity.setAdjustmentFrequencyLessenEnergyCompensation(Float.parseFloat(row.get(29)));
                        }
                        // 火电开机补偿费用(元)
                        if (StrUtil.isNotBlank(row.get(30))) {
                            entity.setThermalPowerStartUpCompensationPrices(Float.parseFloat(row.get(30)));
                        }
                        // 非计划停运考核(元)
                        if (StrUtil.isNotBlank(row.get(31))) {
                            entity.setUnplannedOutageAssessment(Float.parseFloat(row.get(31)));
                        }
                        // 日前备用出清(MW)
                        if (StrUtil.isNotBlank(row.get(32))) {
                            entity.setHisStandbyClearance(Float.parseFloat(row.get(32)));
                        }
                        // 实时备用出清(MW)
                        if (StrUtil.isNotBlank(row.get(33))) {
                            entity.setRealStandbyClearance(Float.parseFloat(row.get(33)));
                        }
                        tradingUnitDOList.add(entity);
                    }
                }
                if (CollUtil.isNotEmpty(tradingUnitDOList)) {
                    privateDataProcMapper.insertClearingResults(dto.getOrgCode(), tradingUnitDOList);
                    log.info("[{}] > 解析入库完成，共 {} 条，用时 {} ms", dto.getBizCode(), tradingUnitDOList.size(),
                        System.currentTimeMillis() - runtime);
                }
            });
        } catch (Exception ex) {
            log.error(StrUtil.format("[{}] > 解析出现异常", dto.getBizCode()), ex);
            return false;
        }
        return true;
    }
}
