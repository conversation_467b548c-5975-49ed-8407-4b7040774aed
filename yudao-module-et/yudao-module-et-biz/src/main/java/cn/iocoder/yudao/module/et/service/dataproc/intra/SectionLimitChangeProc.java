package cn.iocoder.yudao.module.et.service.dataproc.intra;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.et.dal.dataobject.sectionlimitchange.SectionLimitChangeDO;
import cn.iocoder.yudao.module.et.dal.mysql.sectionlimitchange.SectionLimitChangeMapper;
import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.jy.soft.remote.bean.SocketPacket;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * GS-B-261 其它发布信息-断面限额变化
 *
 * <AUTHOR>
 * @date 2024-11-11
 **/
@Slf4j
@Service
@AllArgsConstructor
public class SectionLimitChangeProc implements BaseDataProc {

    private final SectionLimitChangeMapper sectionLimitChangeMapper;

    @Override
    public boolean execute(SocketPacket dto) {
        long runtime = System.currentTimeMillis();
        try {
            TenantUtils.execute(dto.getTenantId(), () -> {
                JSONObject jsonObject = JSONUtil.parseObj(dto.getData());
                List<SectionLimitChangeDO> sectionLimitChangeDOList = CollUtil.newArrayList();
                List<CsvRow> rowList;
                SectionLimitChangeDO entity;
                for (String key : jsonObject.keySet()) {
                    rowList = CsvUtil.getReader(new CsvReadConfig().setBeginLineNo(1).setTrimField(true))
                        .readFromStr(jsonObject.getStr(key)).getRows();
                    for (CsvRow row : rowList) {
                        entity = new SectionLimitChangeDO();
                        // 日期时间
                        entity.setBizDateTime(LocalDate.parse(key));
                        // 断面名称
                        entity.setSectionName(row.get(1));
                        // 断面实际值
                        entity.setRealtimeValue(StrUtil.isNotBlank(row.get(2)) ? new BigDecimal(row.get(2)) : null);
                        // 正向限值
                        entity.setDirectLimit(StrUtil.isNotBlank(row.get(3)) ? new BigDecimal(row.get(3)) : null);
                        // 反向限值
                        entity.setReverseLimit(StrUtil.isNotBlank(row.get(4)) ? new BigDecimal(row.get(4)) : null);
                        // 租户ID
                        entity.setTenantId(dto.getTenantId());
                        // 部门ID
                        // entity.setDeptId();
                        // 上级机构唯一编码（dept扩展字段）
                        entity.setOrgCode(StrUtil.sub(dto.getOrgCode(), 0, dto.getOrgCode().length() - 2));
                        // 调度单元编码
                        entity.setDispatchNodeCode(dto.getOrgCode());
                        // 数据爬取时间
                        entity.setGatherDate(DateUtil.toLocalDateTime(dto.getGatherTime()));
                        sectionLimitChangeDOList.add(entity);
                    }
                }
                if (CollUtil.isNotEmpty(sectionLimitChangeDOList)) {
                    sectionLimitChangeMapper.insertBatch(sectionLimitChangeDOList);
                    log.info("[{}] > 解析入库完成，共 {} 条，用时 {} ms", dto.getBizCode(),
                        sectionLimitChangeDOList.size(), System.currentTimeMillis() - runtime);
                }
            });
        } catch (Exception ex) {
            log.error(StrUtil.format("[{}] > 解析出现异常", dto.getBizCode()), ex);
            return false;
        }
        return true;
    }
}
