package cn.iocoder.yudao.module.et.dal.dataobject.rollingMatchingTransactions;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 日滚动交易明细实体类
 */
@TableName("jy_daily_rolling_result_detail")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JyDailyRollingResultDetailDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 关联主表ID
     */
    private Long pid;

    /**
     * 成交电量
     */
    private BigDecimal quantity;

    /**
     * 成交电价
     */
    private BigDecimal price;

    /**
     * 申报时间
     */
    private Date submissionTime;

    /**
     * 成交时间
     */
    private Date tradeTime;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;

    /**
     * 交易单元ID
     */
    private Long tradingUnitId;

    /**
     * 数据爬取时间
     */
    private Date gatherTime;

    /**
     * 修改时间
     */
    private Date modificationTime;

}