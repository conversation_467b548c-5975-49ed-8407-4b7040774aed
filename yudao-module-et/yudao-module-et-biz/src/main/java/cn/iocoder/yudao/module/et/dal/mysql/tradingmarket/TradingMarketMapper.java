package cn.iocoder.yudao.module.et.dal.mysql.tradingmarket;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.tradingmarket.vo.TradingMarketPageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingmarket.TradingMarketDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 交易 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TradingMarketMapper extends BaseMapperX<TradingMarketDO> {

    default PageResult<TradingMarketDO> selectPage(TradingMarketPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TradingMarketDO>()
                .eqIfPresent(TradingMarketDO::getCode, reqVO.getCode())
                .likeIfPresent(TradingMarketDO::getName, reqVO.getName())
                .likeIfPresent(TradingMarketDO::getFullName, reqVO.getFullName())
                .eqIfPresent(TradingMarketDO::getDistrictCode, reqVO.getDistrictCode())
                .likeIfPresent(TradingMarketDO::getGroupName, reqVO.getGroupName())
                .eqIfPresent(TradingMarketDO::getPredictionModel, reqVO.getPredictionModel())
                .betweenIfPresent(TradingMarketDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TradingMarketDO::getId));
    }

    @Select("select code from jy_trading_market where id = #{id}")
    @TenantIgnore
    TradingMarketDO selectByMyId(@Param("id") Long id);

    @Select("SELECT code, name FROM jy_trading_market")
    @TenantIgnore
    List<TradingMarketDO> selectAll();

    @Select("SELECT * FROM jy_trading_market where code = #{code}")
    @TenantIgnore
    List<TradingMarketDO> selectByCode(@Param("code") String code);

}