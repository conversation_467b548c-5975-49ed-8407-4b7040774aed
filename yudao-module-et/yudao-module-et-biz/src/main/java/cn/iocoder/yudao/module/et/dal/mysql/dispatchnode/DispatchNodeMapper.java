package cn.iocoder.yudao.module.et.dal.mysql.dispatchnode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.dispatchnode.vo.DispatchNodePageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.DispatchNodeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.MysqlTradingUnitDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 交易单元 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DispatchNodeMapper extends BaseMapperX<DispatchNodeDO> {

    default PageResult<DispatchNodeDO> selectPage(DispatchNodePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DispatchNodeDO>()
                .eqIfPresent(DispatchNodeDO::getCode, reqVO.getCode())
                .likeIfPresent(DispatchNodeDO::getName, reqVO.getName())
                .likeIfPresent(DispatchNodeDO::getFullName, reqVO.getFullName())
                .eqIfPresent(DispatchNodeDO::getSort, reqVO.getSort())
                .eqIfPresent(DispatchNodeDO::getCapacity, reqVO.getCapacity())
                .eqIfPresent(DispatchNodeDO::getQuantity, reqVO.getQuantity())
                .eqIfPresent(DispatchNodeDO::getSysUser, reqVO.getSysUser())
                .eqIfPresent(DispatchNodeDO::getSysPwd, reqVO.getSysPwd())
                .eqIfPresent(DispatchNodeDO::getEnabled, reqVO.getEnabled())
                .betweenIfPresent(DispatchNodeDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(DispatchNodeDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(DispatchNodeDO::getOrgCode, reqVO.getOrgCode())
                .orderByDesc(DispatchNodeDO::getId));
    }

    @Select("SELECT capacity FROM jy_dispatch_node WHERE code = #{code}")
    @TenantIgnore
    Double selectCapacityByCode(String code);

    @Select("SELECT name FROM jy_dispatch_node WHERE code = #{code}")
    @TenantIgnore
    String selectNameByCode(String code);

    @Select("SELECT * FROM jy_trading_unit order by sort")
    @TenantIgnore
    List<MysqlTradingUnitDO> selectAllTradingUint();

    @TenantIgnore
    default List<DispatchNodeDO> selectAll() {
        return selectList();
    }
}