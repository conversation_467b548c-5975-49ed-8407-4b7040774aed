package cn.iocoder.yudao.module.et.service.contractstype;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.et.controller.admin.contractstype.vo.ContractsTypePageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractstype.vo.ContractsTypeSaveReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractstype.vo.ContractsTypeVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstype.ContractsTypeDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 合同类型 Service 接口
 *
 * <AUTHOR>
 */
public interface ContractsTypeService {

    /**
     * 创建合同类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createContractsType(@Valid ContractsTypeSaveReqVO createReqVO);

    /**
     * 更新合同类型
     *
     * @param updateReqVO 更新信息
     */
    void updateContractsType(@Valid ContractsTypeSaveReqVO updateReqVO);

    /**
     * 删除合同类型
     *
     * @param id 编号
     */
    void deleteContractsType(Long id);

    /**
     * 获得合同类型
     *
     * @param id 编号
     * @return 合同类型
     */
    ContractsTypeDO getContractsType(Long id);

    /**
     * 获得合同类型分页
     *
     * @param pageReqVO 分页查询
     * @return 合同类型分页
     */
    PageResult<ContractsTypeDO> getContractsTypePage(ContractsTypePageReqVO pageReqVO);

    List<ContractsTypeVO> getContractsTypeList();
}