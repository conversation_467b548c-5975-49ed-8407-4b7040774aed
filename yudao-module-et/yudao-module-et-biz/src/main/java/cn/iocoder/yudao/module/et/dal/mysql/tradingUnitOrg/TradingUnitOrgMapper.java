package cn.iocoder.yudao.module.et.dal.mysql.tradingUnitOrg;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingUnitOrg.TradingUnitOrgDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 交易单元组织机构 Mapper
 *
 * <AUTHOR>
 */

@Mapper
public interface TradingUnitOrgMapper extends BaseMapperX<TradingUnitOrgDO> {


    @TenantIgnore
    @Select("SELECT code FROM jy_trading_unit WHERE deleted != 1 AND ORG_CODE = #{orgCode} lIMIT 1")
    String selectCodeByOrgCode(String orgCode);

    @TenantIgnore
    @Select("SELECT * FROM jy_trading_unit WHERE deleted != 1 AND CODE = #{code} lIMIT 1")
    TradingUnitOrgDO selectByCode(String code);

}
