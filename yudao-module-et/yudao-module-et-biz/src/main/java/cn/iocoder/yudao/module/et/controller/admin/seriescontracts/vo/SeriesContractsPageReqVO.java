package cn.iocoder.yudao.module.et.controller.admin.seriescontracts.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 合同序列分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SeriesContractsPageReqVO extends PageParam {

    @Schema(description = "合同序列名称", example = "王五")
    private String name;

    @Schema(description = "合同类型id（主库合同类型表id）", example = "14945")
    private Long contractsTypeId;

    @Schema(description = "交易时间区间", example = "1")
    private Long[] dateArr;

    private Integer minQuantity;

    private Integer maxQuantity;

    private Double minPrice;

    private Double maxPrice;
}