package cn.iocoder.yudao.module.et.dal.dataobject.companyunit;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.sql.Timestamp;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * 区域公司超级表(省级披露数据) DO
 *
 * <AUTHOR>
 */
@TableName("company_unit")
@KeySequence("company_unit") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyUnitDO {

    /**
     * 时间
     */
    private Timestamp ts;
    /**
     * 公司id
     */
    private String companyId;


    /**
     * 外来电受电计划(日前)
     */
    private Float dayAheadReceivingPower;

    /**
     * 联络线预测 日前
     */
    private Float tieLineForecast;
    /**
     * 联络线预测 日内
     */
    private Float realTieLine ;
    private Float tieLine ;
    /**
     * 新能源负荷预测
     */
    private Float loadForecast;

    /**
     * 系统负荷-B59联络线(省调负荷-日前)
     */
    private Float systemForecastB59;

    /**
     * 水电发电计划
     */
    private Float hydroelectricPowerGenerationPlan;

    /**
     * 新能源发电预测
     */
    private Float powerGenerationForecast;

    /**
     * 火电量小
     */
    private Float thermalPowerSmall;

    /**
     * 火电可调
     */
    private Float thermalPowerAdjustable;

    /**
     * 供需比
     */
    private Float supplyDemandRatio;

    /**
     * 竞价空间
     */
    private Float biddingSpace;

    /**
     * 超短期预测
     */
    private Float ultraShortTermForecast;

    /**
     * 日前统一结算点分区价格-河东
     */
    private Float hisUnifiedSettlementPointPartitionPricesHd;

    /**
     * 实时统一结算点分区价格-河东
     */
    private Float realUnifiedSettlementPointPartitionPricesHd;

    /**
     * 日前统一结算点分区价格-河西
     */
    private Float hisUnifiedSettlementPointPartitionPricesHx;

    /**
     * 实时统一结算点分区价格-河西
     */
    private Float realUnifiedSettlementPointPartitionPricesHx;

    /**
     * 日前统一结算点价格-日前平均出清电价
     */
    private Float hisUnifiedSettlementPointPrice;

    /**
     * 实时统一结算点价格-实时平均出清电价
     */
    private Float realUnifiedSettlementPointPrice;

    /**
     * 日前风电
     */
    private Float hisWindPower;

    /**
     * 日前光电
     */
    private Float hisPhotovoltaicPower;

    /**
     * 日前甘肃_新疆
     */
    private Float hisLlxGsXj;

    /**
     * 日前甘肃_青海
     */
    private Float hisLlxGsQh;

    /**
     * 日前甘肃_宁夏
     */
    private Float hisLlxGsNx;

    /**
     * 日前甘肃_陕西
     */
    private Float hisLlxGsSx;

    /**
     * 日前甘肃_湖南
     */
    private Float hisLlxGsHn;

    /**
     * 实时甘肃_新疆
     */
    private Float realLlxGsXj;

    /**
     * 实时甘肃_青海
     */
    private Float realLlxGsQh;

    /**
     * 实时甘肃_宁夏
     */
    private Float realLlxGsNx;

    /**
     * 实时甘肃_陕西
     */
    private Float realLlxGsSx;

    /**
     * 实时甘肃_湖南
     */
    private Float realLlxGsHn;

    /**
     * 实时风电
     */
    private Float realWindPower;

    /**
     * 实时光电
     */
    private Float realPhotovoltaicPower;

    /**
     * 省火上旋
     */
    private Float provincialThermalPowerUpwardRotation;

    /**
     * 省水上旋
     */
    private Float hydroelectricPowerUpwardRotation;

    /**
     * 实时频率
     */
    private Float realFrequency;

    /**
     * 省调负荷(实时)
     */
    private Float realLoad;

    /**
     * 火电开机容量
     */
    private Float thermalOpenCapacity;

    /**
     * 水电开机容量
     */
    private Float hydroelectricOpenCapacity;

    /**
     * 实际总出力
     */
    private Float realTotalPower;

    /**
     * 非市场化机组实际出力
     */
    private Float actualOutputOfNonMarketOrientedUnits;

    /**
     * 水电实时总出力
     */
    private Float realHydroelectricTotalPower;


    /**
     * 跨省区现货出清电力
     * 外送电(日前)
     */
    private Float crossProvincialSpotClearingOfElectricity;
    /**
     * 核电总出力（甘肃暂时没有，要预留计算）
     */
    private Float totalNuclearPowerOutput;

    /**
     * 短期
     */
    private Float loadSystemForecastShortTerm;

    /**
     * 超短期
     */
    private Float loadSystemForecastUltraShortTerm;

    /**
     * 对象内 负载率计算
     * <p>
     * 负载率 = 火电出力/火电开机
     * 火电出力 = 总出力（外综411）-非市场机组总出力（外综412）-新能源总出力（外综413）-水电总出力（外综414）-核电总出力（甘肃暂时没有，要预留计算）
     *
     * @return
     */
    public Float getAverageLoadRate() {
        float huoDianChuLi = (this.realTotalPower == null ? 0f : this.realTotalPower)
                - (this.actualOutputOfNonMarketOrientedUnits == null ? 0f : this.actualOutputOfNonMarketOrientedUnits)
                - ((this.realWindPower == null ? 0f : this.realWindPower) + (this.realPhotovoltaicPower == null ? 0f : this.realPhotovoltaicPower))
                - (this.realHydroelectricTotalPower == null ? 0f : this.realHydroelectricTotalPower)
                - (this.totalNuclearPowerOutput == null ? 0f : this.totalNuclearPowerOutput);
        float fuZaiLv = 0;
        try {
            if (null == this.thermalOpenCapacity || this.thermalOpenCapacity == 0) {
                return fuZaiLv;
            }
            fuZaiLv = huoDianChuLi / this.thermalOpenCapacity;
        } catch (Exception e) {
            System.out.printf("计算负载率异常:" + e.getLocalizedMessage());
        }
        if (fuZaiLv < 0) {
            fuZaiLv = 0;
        }
        return fuZaiLv;
    }

    /**
     * 对象内 日前负载率计算
     * 日前负载率=火电出力/火电开机
     * 火电出力(火电负荷)=总出力（外综3137）-非市场机组总出力（外综3138）-新能源总出力（外综313（10））-水电总出力（外综313（12））-核电总出力（甘肃暂时没有，要预留计算）
     *
     * @return
     */
    public Float getHisAverageLoadRate() {
        float hisHuoDianChuLi = (this.realTotalPower == null ? 0f : this.realTotalPower) - (this.actualOutputOfNonMarketOrientedUnits == null ? 0f : this.actualOutputOfNonMarketOrientedUnits)
                - ((this.hisWindPower == null ? 0f : this.hisWindPower) + (this.hisPhotovoltaicPower == null ? 0f : this.hisPhotovoltaicPower))
                - (this.realHydroelectricTotalPower == null ? 0f : this.realHydroelectricTotalPower) - 0;
        float fuZaiLv = 0;
        try {
            if (null == this.thermalOpenCapacity || this.thermalOpenCapacity == 0) {
                return fuZaiLv;
            }
            fuZaiLv = hisHuoDianChuLi / this.thermalOpenCapacity;
        } catch (Exception e) {
            System.out.printf("计算日前负载率异常:" + e.getLocalizedMessage());
        }
        if (fuZaiLv < 0) {
            fuZaiLv = 0;
        }
        return fuZaiLv;
    }

    /**
     * 对象内 竞价空间实际计算
     * 竞价空间实际=省调负荷实际+(外送电实际-受电负荷实际=五个联络线的和)-(新能源负荷实际=实时风电+实时光电)；
     *
     * @return
     */
    public Float getRealBiddingSpace() {
        float realBiddingSpace = Optional.ofNullable(this.realLoad).orElse(0f)
                + this.getRealWaiSong()
                - (Optional.ofNullable(this.realWindPower).orElse(0f) + Optional.ofNullable(this.realPhotovoltaicPower).orElse(0f));
        return Math.max(realBiddingSpace, 0);
    }


    /**
     * 对象内 竞价空间实时计算 不包含外送
     * 竞价空间实时=省调负荷实时-(新能源负荷实时=实时风电+实时光电)；
     *
     * @return
     */
    public Float getRealBiddingSpaceSubtractWaisong() {
        float realBiddingSpace = Optional.ofNullable(this.realLoad).orElse(0f)
                - (Optional.ofNullable(this.realWindPower).orElse(0f) + Optional.ofNullable(this.realPhotovoltaicPower).orElse(0f));
        return Math.max(realBiddingSpace, 0);
    }


    /**
     * 对象内 竞价空间日前计算
     * 竞价空间日前=省调负荷日前+(外送电日前-受电负荷日前=五个联络线的和)-(新能源负荷日前=日前风电+日前光电)；
     *
     * @return
     */
    public Float getHisBiddingSpace() {
        float hisBiddingSpace = Optional.ofNullable(this.loadSystemForecastShortTerm).orElse(0f)
                + this.getHisWaiSong()
                - (Optional.ofNullable(this.hisWindPower).orElse(0f) + Optional.ofNullable(this.hisPhotovoltaicPower).orElse(0f));
        return Math.max(hisBiddingSpace, 0);
    }


    /**
     * 对象内 竞价空间日前计算 不包含外送
     * 竞价空间日前=省调负荷日前-(新能源负荷日前=日前风电+日前光电)；
     *
     * @return
     */
    public Float getHisBiddingSpaceSubtractWaisong() {
        float hisBiddingSpace = Optional.ofNullable(this.loadSystemForecastShortTerm).orElse(0f)
                - (Optional.ofNullable(this.hisWindPower).orElse(0f) + Optional.ofNullable(this.hisPhotovoltaicPower).orElse(0f));
        return Math.max(hisBiddingSpace, 0);
    }


    /**
     * 对象内 外送电实时计算
     * 五个联络线(正数外送，负数受电)
     *
     * @return
     */
    public Float getRealWaiSong() {
        return Stream.of(
                Optional.ofNullable(this.realLlxGsHn).orElse(0f),
                Optional.ofNullable(this.realLlxGsQh).orElse(0f),
                Optional.ofNullable(this.realLlxGsNx).orElse(0f),
                Optional.ofNullable(this.realLlxGsSx).orElse(0f),
                Optional.ofNullable(this.realLlxGsXj).orElse(0f)
        ).reduce(0f, Float::sum);
    }

    /**
     * 对象内 日前外送电计算
     * 五个联络线(正数外送，负数受电)
     *
     * @return
     */
    public Float getHisWaiSong() {
        return Stream.of(
                Optional.ofNullable(this.hisLlxGsHn).orElse(0f),
                Optional.ofNullable(this.hisLlxGsQh).orElse(0f),
                Optional.ofNullable(this.hisLlxGsNx).orElse(0f),
                Optional.ofNullable(this.hisLlxGsSx).orElse(0f),
                Optional.ofNullable(this.hisLlxGsXj).orElse(0f)
        ).reduce(0f, Float::sum);
    }

    private String getValueOrDefault(Float value) {
        return value == null ? "--" : value.toString();
    }

    public String getLoadForecast() {
        return getValueOrDefault(loadForecast);
    }

    public String getSystemForecastB59() {
        return getValueOrDefault(systemForecastB59);
    }

    public String getHydroelectricPowerGenerationPlan() {
        return getValueOrDefault(hydroelectricPowerGenerationPlan);
    }

    public String getPowerGenerationForecast() {
        return getValueOrDefault(powerGenerationForecast);
    }

    public String getThermalPowerSmall() {
        return getValueOrDefault(thermalPowerSmall);
    }

    public String getThermalPowerAdjustable() {
        return getValueOrDefault(thermalPowerAdjustable);
    }

    public String getSupplyDemandRatio() {
        return getValueOrDefault(supplyDemandRatio);
    }

    public String getBiddingSpace() {
        return getValueOrDefault(biddingSpace);
    }

    public String getUltraShortTermForecast() {
        return getValueOrDefault(ultraShortTermForecast);
    }

    public String getHisUnifiedSettlementPointPartitionPricesHd() {
        return getValueOrDefault(hisUnifiedSettlementPointPartitionPricesHd);
    }

    public String getRealUnifiedSettlementPointPartitionPricesHd() {
        return getValueOrDefault(realUnifiedSettlementPointPartitionPricesHd);
    }

    public String getHisUnifiedSettlementPointPartitionPricesHx() {
        return getValueOrDefault(hisUnifiedSettlementPointPartitionPricesHx);
    }

    public String getRealUnifiedSettlementPointPartitionPricesHx() {
        return getValueOrDefault(realUnifiedSettlementPointPartitionPricesHx);
    }

    public String getHisUnifiedSettlementPointPrice() {
        return getValueOrDefault(hisUnifiedSettlementPointPrice);
    }

    public String getRealUnifiedSettlementPointPrice() {
        return getValueOrDefault(realUnifiedSettlementPointPrice);
    }

    public String getHisWindPower() {
        return getValueOrDefault(hisWindPower);
    }

    public String getHisPhotovoltaicPower() {
        return getValueOrDefault(hisPhotovoltaicPower);
    }

    public String getHisLlxGsXj() {
        return getValueOrDefault(hisLlxGsXj);
    }

    public String getHisLlxGsQh() {
        return getValueOrDefault(hisLlxGsQh);
    }

    public String getHisLlxGsNx() {
        return getValueOrDefault(hisLlxGsNx);
    }

    public String getHisLlxGsSx() {
        return getValueOrDefault(hisLlxGsSx);
    }

    public String getHisLlxGsHn() {
        return getValueOrDefault(hisLlxGsHn);
    }

    public String getRealLlxGsXj() {
        return getValueOrDefault(realLlxGsXj);
    }

    public String getRealLlxGsQh() {
        return getValueOrDefault(realLlxGsQh);
    }

    public String getRealLlxGsNx() {
        return getValueOrDefault(realLlxGsNx);
    }

    public String getRealLlxGsSx() {
        return getValueOrDefault(realLlxGsSx);
    }

    public String getRealLlxGsHn() {
        return getValueOrDefault(realLlxGsHn);
    }

    public String getRealWindPower() {
        return getValueOrDefault(realWindPower);
    }

    public String getRealPhotovoltaicPower() {
        return getValueOrDefault(realPhotovoltaicPower);
    }

    public String getProvincialThermalPowerUpwardRotation() {
        return getValueOrDefault(provincialThermalPowerUpwardRotation);
    }

    public String getHydroelectricPowerUpwardRotation() {
        return getValueOrDefault(hydroelectricPowerUpwardRotation);
    }

    public String getRealFrequency() {
        return getValueOrDefault(realFrequency);
    }

    public String getRealLoad() {
        return getValueOrDefault(realLoad);
    }

    public String getThermalOpenCapacity() {
        return getValueOrDefault(thermalOpenCapacity);
    }

    public String getHydroelectricOpenCapacity() {
        return getValueOrDefault(hydroelectricOpenCapacity);
    }

    public String getRealTotalPower() {
        return getValueOrDefault(realTotalPower);
    }

    public String getActualOutputOfNonMarketOrientedUnits() {
        return getValueOrDefault(actualOutputOfNonMarketOrientedUnits);
    }

    public String getRealHydroelectricTotalPower() {
        return getValueOrDefault(realHydroelectricTotalPower);
    }

    public String getCrossProvincialSpotClearingOfElectricity() {
        return getValueOrDefault(crossProvincialSpotClearingOfElectricity);
    }

    public String getTotalNuclearPowerOutput() {
        return getValueOrDefault(totalNuclearPowerOutput);
    }

    public String getLoadSystemForecastShortTerm() {
        return getValueOrDefault(loadSystemForecastShortTerm);
    }

    public String getLoadSystemForecastUltraShortTerm() {
        return getValueOrDefault(loadSystemForecastUltraShortTerm);
    }


    //外送电  所有联络线累和
    public float getWaiSongElectricity() {
        float waisong = 0;
        waisong += Convert.toFloat(this.hisLlxGsHn, 0f);
        waisong += Convert.toFloat(this.hisLlxGsXj, 0f);
        waisong += Convert.toFloat(this.hisLlxGsQh, 0f);
        waisong += Convert.toFloat(this.hisLlxGsNx, 0f);
        waisong += Convert.toFloat(this.hisLlxGsSx, 0f);
        return waisong;
    }

}