package cn.iocoder.yudao.module.et.controller.admin.contractsmember;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.contractsmember.vo.ContractsMemberPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractsmember.vo.ContractsMemberRespVO;
import cn.iocoder.yudao.module.et.controller.admin.contractsmember.vo.ContractsMemberSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsmember.ContractsMemberDO;
import cn.iocoder.yudao.module.et.service.contractsmember.ContractsMemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合同方信息")
@RestController
@RequestMapping("/jy/contracts-member")
@Validated
public class ContractsMemberController {

    @Resource
    private ContractsMemberService contractsMemberService;

    @PostMapping("/create")
    @Operation(summary = "创建合同方信息")
    @PreAuthorize("@ss.hasPermission('jy:contracts-member:create')")
    public CommonResult<Long> createContractsMember(@Valid @RequestBody ContractsMemberSaveReqVO createReqVO) {
        return success(contractsMemberService.createContractsMember(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新合同方信息")
    @PreAuthorize("@ss.hasPermission('jy:contracts-member:update')")
    public CommonResult<Boolean> updateContractsMember(@Valid @RequestBody ContractsMemberSaveReqVO updateReqVO) {
        contractsMemberService.updateContractsMember(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除合同方信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('jy:contracts-member:delete')")
    public CommonResult<Boolean> deleteContractsMember(@RequestParam("id") Long id) {
        contractsMemberService.deleteContractsMember(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得合同方信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('jy:contracts-member:query')")
    public CommonResult<ContractsMemberRespVO> getContractsMember(@RequestParam("id") Long id) {
        ContractsMemberDO contractsMember = contractsMemberService.getContractsMember(id);
        return success(BeanUtils.toBean(contractsMember, ContractsMemberRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得合同方信息分页")
    @PreAuthorize("@ss.hasPermission('jy:contracts-member:query')")
    public CommonResult<PageResult<ContractsMemberRespVO>> getContractsMemberPage(@Valid ContractsMemberPageReqVO pageReqVO) {
        PageResult<ContractsMemberDO> pageResult = contractsMemberService.getContractsMemberPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContractsMemberRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出合同方信息 Excel")
    @PreAuthorize("@ss.hasPermission('jy:contracts-member:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContractsMemberExcel(@Valid ContractsMemberPageReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ContractsMemberDO> list = contractsMemberService.getContractsMemberPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "合同方信息.xls", "数据", ContractsMemberRespVO.class,
                BeanUtils.toBean(list, ContractsMemberRespVO.class));
    }

}