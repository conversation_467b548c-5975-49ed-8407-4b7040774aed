package cn.iocoder.yudao.module.et.dal.mysql.contractsequipment;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.et.controller.admin.contractsequipment.vo.ContractsEquipmentPageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsequipment.ContractsEquipmentDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 合同机组信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("shardingsphereDB")
public interface ContractsEquipmentMapper extends BaseMapperX<ContractsEquipmentDO> {

    default PageResult<ContractsEquipmentDO> selectPage(ContractsEquipmentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContractsEquipmentDO>()
                .likeIfPresent(ContractsEquipmentDO::getContractsRoleName, reqVO.getContractsRoleName())
                .likeIfPresent(ContractsEquipmentDO::getEquipmentName, reqVO.getEquipmentName())
                .eqIfPresent(ContractsEquipmentDO::getEquipmentType, reqVO.getEquipmentType())
                .eqIfPresent(ContractsEquipmentDO::getQuantity, reqVO.getQuantity())
                .eqIfPresent(ContractsEquipmentDO::getApprovedPrice, reqVO.getApprovedPrice())
                .eqIfPresent(ContractsEquipmentDO::getEquipmentGen, reqVO.getEquipmentGen())
                .likeIfPresent(ContractsEquipmentDO::getTimeSlotName, reqVO.getTimeSlotName())
                .eqIfPresent(ContractsEquipmentDO::getTimeSlotRange, reqVO.getTimeSlotRange())
                .eqIfPresent(ContractsEquipmentDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(ContractsEquipmentDO::getOrgCode, reqVO.getOrgCode())
                .betweenIfPresent(ContractsEquipmentDO::getGatherDate, reqVO.getGatherDate())
                .betweenIfPresent(ContractsEquipmentDO::getModificationTime, reqVO.getModificationTime())
                .betweenIfPresent(ContractsEquipmentDO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(ContractsEquipmentDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(ContractsEquipmentDO::getContractsId, reqVO.getContractsId())
                .orderByDesc(ContractsEquipmentDO::getId));
    }

    @Select("select * from jy_contracts_equipment where contracts_id = #{id} and deleted = 0")
    List<ContractsEquipmentDO> selectListByContractsId(String id);
}