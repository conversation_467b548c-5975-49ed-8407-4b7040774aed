package cn.iocoder.yudao.module.et.controller.admin.contracAnalyis;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.hash.Hash;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.contracts.vo.ContractsPageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contracts.ContractsDO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstype.ContractsTypeDO;
import cn.iocoder.yudao.module.et.dal.mysql.contracts.ContractsMapper;
import cn.iocoder.yudao.module.et.dal.mysql.contractstype.ContractsTypeMapper;
import cn.iocoder.yudao.module.et.dal.mysql.tradingUnitOrg.TradingUnitOrgMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/et/contracAnalyis")
@Tag(name = "管理后台 - 合同月统计")
public class ContracAnalyisController {

    @Resource
    private TradingUnitOrgMapper tradingUnitOrgMapper;

    @Resource
    private ContractsMapper contractsMapper;

    @Resource
    private ContractsTypeMapper contractsTypeMapper;

    private String generatePrefixFromName(String name) {
        // 只保留中文字符
        String cleanedName = name.replaceAll("[^\\u4e00-\\u9fa5]", "");

        // 获取每个汉字的拼音首字母
        return cleanedName.chars()
                .mapToObj(ch -> {
                    String pinyin = PinyinUtil.getPinyin(String.valueOf((char) ch), "");
                    return pinyin.isEmpty() ? "" : pinyin.substring(0, 1).toUpperCase();
                })
                .collect(Collectors.joining());
    }

    private Map<Integer, ContractTypeMapping> buildContractTypeMapping(List<ContractsTypeDO> typeList) {
        Map<Integer, ContractTypeMapping> mapping = new HashMap<>();

        for (ContractsTypeDO type : typeList) {
            String typeName = type.getName();
            String prefix = generatePrefixFromName(typeName);  // 使用拼音首字母生成字段前缀

            mapping.put(Math.toIntExact(type.getId()), new ContractTypeMapping(prefix, typeName));
        }

        return mapping;
    }

    private static class ContractTypeMapping {
        private final String prefix;
        private final String label;

        public ContractTypeMapping(String prefix, String label) {
            this.prefix = prefix;
            this.label = label;
        }

        public String getPrefix() {
            return prefix;
        }

        public String getLabel() {
            return label;
        }
    }


    @PostMapping("/page")
    @Operation(summary = "合同月统计列表")
    @TenantIgnore
    public CommonResult contracAnalyisList(@RequestBody @Valid ContractsPageReqVO pageReqVO) {
        // 获取原始数据
        pageReqVO.setPageSize(Integer.MAX_VALUE);
        PageResult<ContractsDO> contractsDOPageResult = contractsMapper.selectPage(pageReqVO);
        List<ContractsDO> list = contractsDOPageResult.getList();

        // 提取所有唯一的合同类型 ID
        Set<Integer> contractTypeIds = list.stream()
                .map(ContractsDO::getContractsTypeId)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(contractTypeIds)) {
            return success(Collections.emptyList());
        }

        // 查询合同类型信息
        List<ContractsTypeDO> typeList = contractsTypeMapper.selectBatchIds(contractTypeIds);

        // 构建 typeId -> ContractTypeMapping(prefix, label)
        Map<Integer, ContractTypeMapping> typeMapping = buildContractTypeMapping(typeList);

        // 按 typeId 查询数据
        Map<Integer, List<ContractsDO>> typeDataMap = new HashMap<>();
        for (Integer typeId : contractTypeIds) {
            List<ContractsDO> dataList = contractsMapper.selectListByTypeId(List.of(pageReqVO.getIds()),Long.valueOf(typeId),pageReqVO.getYearMonth());
            typeDataMap.put(typeId, dataList);
        }

        // 查询所有交易单元，构建unit到fullName的映射
        List<cn.iocoder.yudao.module.et.dal.dataobject.tradingUnitOrg.TradingUnitOrgDO> tradingUnitList = tradingUnitOrgMapper.selectList(null);
        Map<String, String> unitFullNameMap = new HashMap<>();
        for (cn.iocoder.yudao.module.et.dal.dataobject.tradingUnitOrg.TradingUnitOrgDO tradingUnit : tradingUnitList) {
            if (tradingUnit.getCode() != null && tradingUnit.getFullName() != null) {
                unitFullNameMap.put(tradingUnit.getCode(), tradingUnit.getFullName());
            }
        }

        // 合并数据到最终结构
        List<Map<String, Object>> mergedData = mergeDataByMonthAndUnit(typeDataMap, typeMapping);
        mergedData.sort((o1, o2) -> {
            String month1 = (String) o1.get("month");
            String month2 = (String) o2.get("month");
            int monthComparison = month1.compareTo(month2);

            if (monthComparison != 0) {
                return monthComparison;
            }

            String unit1 = (String) o1.get("unit");
            String unit2 = (String) o2.get("unit");
            if (unit1 == null && unit2 == null) {
                return 0;
            } else if (unit1 == null) {
                return -1;
            } else if (unit2 == null) {
                return 1;
            } else {
                return unit1.compareTo(unit2); // 正常比较
            }
        });

        // 替换unit为fullName
        for (Map<String, Object> row : mergedData) {
            Object unitObj = row.get("unit");
            if (unitObj != null) {
                String unitCode = unitObj.toString();
                String fullName = unitFullNameMap.get(unitCode);
                if (fullName != null) {
                    row.put("unit", fullName);
                }
            }
        }

        for (Map<String, Object> row : mergedData) {
            double totalQuantity = 0.0;
            double totalPrice = 0.0;
            for (ContractTypeMapping mapping : typeMapping.values()) {
                String prefix = mapping.getPrefix();
                row.putIfAbsent(prefix + "Quantity", 0.00);
                row.putIfAbsent(prefix + "Price", 0.00);
                row.putIfAbsent(prefix + "TotalPrice", 0.00);
            }

            // 强制格式化所有以 Quantity、Price 结尾的字段为两位小数（无论原始类型，整数如170100也转为170100.00）
            for (Map.Entry<String, Object> entry : row.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if ((key.endsWith("Quantity") || key.endsWith("Price")) ) {
                    double numericValue = 0.00;
                    if (value instanceof Number) {
                        numericValue = ((Number) value).doubleValue();
                    } else if (value != null) {
                        try {
                            numericValue = new BigDecimal(value.toString()).doubleValue();
                        } catch (Exception ignored) {}
                    }
                    double formattedValue = BigDecimal.valueOf(numericValue)
                            .setScale(2, RoundingMode.HALF_UP)
                            .doubleValue();
                    row.put(key, formattedValue);
                    if (key.endsWith("Quantity")) {
                        totalQuantity += formattedValue;
                    } else if (key.endsWith("TotalPrice")) {
                        totalPrice += formattedValue;
                    }
                }
            }
            row.put("totalQuantity", BigDecimal.valueOf(totalQuantity)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue());
            row.put("totalPrice", BigDecimal.valueOf(totalPrice)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue());
        }

        // 构建动态表头
        List<Map<String, Object>> dynamicColumns = buildDynamicColumns(typeMapping);
        Map<String, Object> result = new HashMap<>();
        result.put("columns", dynamicColumns);
        result.put("tableData", mergedData);

        return success(result);
    }

    private List<Map<String, Object>> buildDynamicColumns(Map<Integer, ContractTypeMapping> typeMapping) {
        List<Map<String, Object>> dynamicColumns = new ArrayList<>();

        for (ContractTypeMapping mapping : typeMapping.values()) {
            Map<String, Object> columnGroup = new HashMap<>();
            columnGroup.put("label", mapping.getLabel());

            List<Map<String, String>> children = new ArrayList<>();
            children.add(createChildColumn("合同量(MW)", mapping.getPrefix() + "Quantity"));
            children.add(createChildColumn("合同均价(元/MW)", mapping.getPrefix() + "Price"));
            children.add(createChildColumn("总价(元)", mapping.getPrefix() + "TotalPrice"));

            columnGroup.put("children", children);
            dynamicColumns.add(columnGroup);
        }

        return dynamicColumns;
    }

    private Map<String, String> createChildColumn(String label, String prop) {
        Map<String, String> child = new HashMap<>();
        child.put("label", label);
        child.put("prop", prop);
        return child;
    }

/*    private List<Map<String, Object>> mergeDataByMonthAndUnit(
            Map<Integer, List<ContractsDO>> typeDataMap,
            Map<Integer, ContractTypeMapping> typeMapping) {

        // 使用 month + unit 作为唯一键
        Map<String, Map<String, Object>> mergedMap = new HashMap<>();

        for (Map.Entry<Integer, List<ContractsDO>> entry : typeDataMap.entrySet()) {
            Integer typeId = entry.getKey();
            ContractTypeMapping mapping = typeMapping.get(typeId);
            if (mapping == null) continue;

            String prefix = mapping.getPrefix();

            for (ContractsDO contract : entry.getValue()) {
                String key = contract.getMonth() + "-" + contract.getUnit();
                Map<String, Object> row = mergedMap.computeIfAbsent(key, k -> new HashMap<>());

                // 填充基础字段
                if (!row.containsKey("month")) row.put("month", contract.getMonth());
                if (!row.containsKey("unit")) row.put("unit", contract.getUnit());

                // 填充动态字段
                row.put(prefix + "Quantity", contract.getTotalQuantity());
                row.put(prefix + "Price", contract.getAvgPrice());
                row.put(prefix + "TotalPrice", contract.getTotalPrice());

            }
        }

        return new ArrayList<>(mergedMap.values());
    }*/
private List<Map<String, Object>> mergeDataByMonthAndUnit(
        Map<Integer, List<ContractsDO>> typeDataMap,
        Map<Integer, ContractTypeMapping> typeMapping) {

    // 使用 month + unit 作为唯一键
    Map<String, Map<String, Object>> mergedMap = new HashMap<>();

    for (Map.Entry<Integer, List<ContractsDO>> entry : typeDataMap.entrySet()) {
        Integer typeId = entry.getKey();
        ContractTypeMapping mapping = typeMapping.get(typeId);
        if (mapping == null) continue;

        String prefix = mapping.getPrefix();

        for (ContractsDO contract : entry.getValue()) {
            String month = contract.getMonth();
            String unit = contract.getUnit();

            // 构建唯一键
            String key = month + "-" + unit;
            Map<String, Object> row = mergedMap.computeIfAbsent(key, k -> new HashMap<>());

            // 填充基础字段（仅设置一次）
            row.putIfAbsent("month", month);
            row.putIfAbsent("unit", unit);

            // 直接填充动态字段（允许同一记录包含多个维度的值）
            row.put(prefix + "Quantity", contract.getTotalQuantity());
            row.put(prefix + "Price", contract.getAvgPrice());
            row.put(prefix + "TotalPrice", contract.getTotalPrice());
        }
    }

    return new ArrayList<>(mergedMap.values());
}


}
