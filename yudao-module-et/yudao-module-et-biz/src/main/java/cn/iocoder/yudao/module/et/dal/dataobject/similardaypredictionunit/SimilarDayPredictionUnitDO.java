package cn.iocoder.yudao.module.et.dal.dataobject.similardaypredictionunit;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 相似日表 交易单元级
 * DO
 *
 * <AUTHOR>
 */
@TableName("jy_similar_day_prediction_unit")
@KeySequence("jy_similar_day_prediction_unit_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimilarDayPredictionUnitDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 预测日
     */
    private LocalDateTime forecastDay;
    /**
     * 相似日
     */
    private LocalDateTime similarDay;
    /**
     * 相似度
     */
    private Double similarity;
    /**
     * 相似度排序
     */
    private Integer sort;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 变电站id
     */
    private String substationId;
    /**
     * 数据添加时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
    /*
     * 相似距离
     */
    private Float distance;


}