package cn.iocoder.yudao.module.et.dal.dataobject.contractspowercurve;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 电力曲线 DO
 *
 * <AUTHOR>
 */
@TableName("jy_contracts_power_curve")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractsPowerCurveDO {

    /**
     * 自增主键
     */
    @TableId
    private Long id;
    /**
     * 曲线类型
     */
    private String curveType;

    /**
     * 行号
     */
    private String rowNo;
    /**
     * 主体信息
     */
    private String memberName;
    /**
     * 开始日期
     */
    private LocalDateTime beginTime;
    /**
     * 结束日期
     */
    private LocalDateTime endTime;
    /**
     * 运行日期
     */
    private LocalDateTime runDate;
    /**
     * 日电量
     */
    private BigDecimal quantity;
    /**
     * 曲线点数
     */
    private String points;
    /**
     * 合同id
     */
    private String contractsId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherDate;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;

    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;
}