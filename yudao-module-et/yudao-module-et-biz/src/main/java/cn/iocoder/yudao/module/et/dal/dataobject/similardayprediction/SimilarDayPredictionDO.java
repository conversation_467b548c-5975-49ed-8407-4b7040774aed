package cn.iocoder.yudao.module.et.dal.dataobject.similardayprediction;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 相似日表
 * DO
 *
 * <AUTHOR>
 */
@TableName("jy_similar_day_prediction")
@KeySequence("jy_similar_day_prediction_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimilarDayPredictionDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 预测日
     */
    private LocalDateTime forecastDay;
    /**
     * 相似日
     */
    private LocalDateTime similarDay;
    /**
     * 相似度
     */
    private Double similarity;
    /**
     * 相似度排序
     */
    private Integer sort;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 数据添加时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
    /*
     * 相似距离
     */
    private Float distance;
    /*
     * 交易市场编号
     */
    private String marketCode;

    private Integer tenantId;

}