package cn.iocoder.yudao.module.et.dal.mysql.jyThermalCoalPrice;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.dal.dataobject.thermalCoalPrice.ThermalCoalPrice;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface JyThermalCoalPriceMapper extends BaseMapperX<ThermalCoalPrice> {
    @TenantIgnore
    default List<ThermalCoalPrice> getJyThermalCoalPrice(String mei, String diqu, String startDay, String endDay) {
        return selectList(new LambdaQueryWrapperX<ThermalCoalPrice>()
                .eqIfPresent(ThermalCoalPrice::getProductName, mei)
                .eqIfPresent(ThermalCoalPrice::getProducer, diqu)
                .geIfPresent(ThermalCoalPrice::getBizDate, startDay)
                .leIfPresent(ThermalCoalPrice::getBizDate, endDay));
    }
}
