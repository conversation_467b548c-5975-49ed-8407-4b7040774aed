package cn.iocoder.yudao.module.et.dal.mysql.districtcode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.districtcode.vo.DistrictCodePageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.districtcode.DistrictCodeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 行政区划 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DistrictCodeMapper extends BaseMapperX<DistrictCodeDO> {
    @TenantIgnore
    default PageResult<DistrictCodeDO> selectPage(DistrictCodePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DistrictCodeDO>()
                .likeIfPresent(DistrictCodeDO::getName, reqVO.getName())
                .eqIfPresent(DistrictCodeDO::getLevel, reqVO.getLevel())
                .eqIfPresent(DistrictCodeDO::getType, reqVO.getType())
                .likeIfPresent(DistrictCodeDO::getAbname, reqVO.getAbname())
                .eqIfPresent(DistrictCodeDO::getPid, reqVO.getPid())
                .likeIfPresent(DistrictCodeDO::getPname, reqVO.getPname())
                .eqIfPresent(DistrictCodeDO::getNote, reqVO.getNote())
                .eqIfPresent(DistrictCodeDO::getLat, reqVO.getLat())
                .eqIfPresent(DistrictCodeDO::getLng, reqVO.getLng()));
    }

    @TenantIgnore
    default List<DistrictCodeDO> selectListByLikeCodeId(String districtCode) {
        return selectList(new LambdaQueryWrapperX<DistrictCodeDO>().likeIfPresent(DistrictCodeDO::getCode, districtCode));
    }
}