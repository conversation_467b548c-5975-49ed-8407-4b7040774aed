package cn.iocoder.yudao.module.et.dal.dataobject.clearingprice;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.sql.Timestamp;

/**
 * 日前实时各节点出清类信息超级表 DO
 *
 * <AUTHOR>
 */
@TableName("substation")
@KeySequence("substation") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClearingPriceDO {
    private Timestamp ts;
    private String companyId;
    // 甘肃.雷台变/330kV.330kVⅠ母
    private float hisMarketClearingPriceLt;
    private float marketClearingPriceLt;
    // 甘肃.红沙岗变/330kV.330kVⅠ母
    private float hisMarketClearingPriceHsg;
    private float marketClearingPriceHsg;
    // 甘肃.居延变/330kV.330kVⅠ母
    private float hisMarketClearingPriceJy;
    private float marketClearingPriceJy;
    // 甘肃.昌西变/330kV.330kVⅠ母
    private float hisMarketClearingPriceCx;
    private float marketClearingPriceCx;
    // 甘肃.星光变/330kV.330kVⅠ母
    private float hisMarketClearingPriceXg;
    private float marketClearingPriceXg;
    // 甘肃.芦阳变/330kV.330kVⅠ母
    private float hisMarketClearingPriceLy;
    private float marketClearingPriceLy;
    // 甘肃.成纪变/330kV.330kVⅠ母
    private float hisMarketClearingPriceCj;
    private float marketClearingPriceCj;
    // 甘肃.中川变/330kV.330kVⅠ母
    private float hisMarketClearingPriceZc;
    private float marketClearingPriceZc;
    // 甘肃.永登变/330kV.330kVⅠ母
    private float hisMarketClearingPriceYd;
    private float marketClearingPriceYd;
    // 甘肃.武胜变/330kV.330kVⅢ母
    private float hisMarketClearingPriceWs;
    private float marketClearingPriceWs;
    // 甘肃.柏林变/330kV.330kVⅠ母
    private float hisMarketClearingPriceBl;
    private float marketClearingPriceBl;
    // 甘肃.红乐变/330kV.330kVⅠ母
    private float hisMarketClearingPriceHl;
    private float marketClearingPriceHl;
}
