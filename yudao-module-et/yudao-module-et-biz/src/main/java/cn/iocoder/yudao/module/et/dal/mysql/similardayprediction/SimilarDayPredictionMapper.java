package cn.iocoder.yudao.module.et.dal.mysql.similardayprediction;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.et.controller.admin.similardayprediction.vo.SimilarDayPredictionPageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.similardayprediction.SimilarDayPredictionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 相似日表
 * Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SimilarDayPredictionMapper extends BaseMapperX<SimilarDayPredictionDO> {

    default PageResult<SimilarDayPredictionDO> selectPage(SimilarDayPredictionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SimilarDayPredictionDO>()
                .eqIfPresent(SimilarDayPredictionDO::getForecastDay, reqVO.getForecastDay())
                .eqIfPresent(SimilarDayPredictionDO::getSimilarDay, reqVO.getSimilarDay())
                .eqIfPresent(SimilarDayPredictionDO::getSimilarity, reqVO.getSimilarity())
                .eqIfPresent(SimilarDayPredictionDO::getSort, reqVO.getSort())
                .eqIfPresent(SimilarDayPredictionDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(SimilarDayPredictionDO::getOrgCode, reqVO.getOrgCode())
                .betweenIfPresent(SimilarDayPredictionDO::getModificationTime, reqVO.getModificationTime())
                .orderByDesc(SimilarDayPredictionDO::getId));
    }

}