package cn.iocoder.yudao.module.et.thread;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.et.dal.dataobject.spiderConf.SpiderConfDO;
import cn.iocoder.yudao.module.et.service.dataproc.base.BaseDataProc;
import cn.iocoder.yudao.module.et.service.mongo.MongoService;
import cn.jy.soft.remote.bean.SocketPacket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class SpiderPageAnalyzeThread implements Runnable {

    private MongoService mongoService;
    private ConfigurableApplicationContext ctx;
    private Map<Long, Map<String, SpiderConfDO>> spiderConfDOMap;

    public static final String collectionName = "socket_packet";

    public SpiderPageAnalyzeThread(MongoService mongoService, ConfigurableApplicationContext ctx, Map<Long, Map<String, SpiderConfDO>> spiderConfDOMap) {
        this.mongoService = mongoService;
        this.ctx = ctx;
        this.spiderConfDOMap = spiderConfDOMap;
    }

    @Override
    public void run() {

        while (true) {
            long time = System.currentTimeMillis();

            try {
                Date nowDate = DateUtil.date();
                List<SocketPacket> list = mongoService.findByLimit(100);

                SpiderConfDO confDo = null;
                for(SocketPacket packet : list) {
                    confDo = spiderConfDOMap.get(packet.getTenantId()).get(packet.getBizCode());
                    if(confDo == null) {
                        log.error(StrUtil.format("报文未找到对应解析类. bizCode: {}", packet.getBizCode()));
                        mongoService.move2new(packet, collectionName,
                                SpiderPageAnalyzeThread.collectionName + "_non_" + DateUtil.format(nowDate, "yyyy-MM-dd"));
                    } else {
                        if(StrUtil.isNotBlank(confDo.getClzImpl()) && confDo.getEnabled() && confDo.getDeleted() == false ) {
                            try {
                                BaseDataProc baseDataProc = (BaseDataProc) ctx.getBean(confDo.getClzImpl());
                                boolean status = baseDataProc.execute(packet);
                                if(status) {
                                    mongoService.move2new(packet, collectionName,
                                            collectionName + "_" + DateUtil.format(DateUtil.date(), "yyyy-MM-dd"));
                                }
                            } catch (Exception e) {
                                log.error(e.getMessage());
                            }
                        }
                    }
                }

                try {
                    Thread.sleep(5000L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            System.out.println(StrUtil.format("爬取页面解析执行完成. 用时：{} ms.", (System.currentTimeMillis() - time)));
        }

    }
}
