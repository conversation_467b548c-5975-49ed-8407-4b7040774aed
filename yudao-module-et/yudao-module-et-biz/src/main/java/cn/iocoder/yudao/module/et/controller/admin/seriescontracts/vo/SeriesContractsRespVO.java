package cn.iocoder.yudao.module.et.controller.admin.seriescontracts.vo;

import cn.iocoder.yudao.module.et.controller.admin.contracts.vo.ContractsRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 合同序列 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SeriesContractsRespVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "4961")
    @ExcelProperty("自增主键")
    private Long id;

    @Schema(description = "合同序列名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("合同序列名称")
    private String name;

    @Schema(description = "合同类型id（主库合同类型表id）", example = "14945")
    @ExcelProperty("合同类型id（主库合同类型表id）")
    private Long contractsTypeId;

    @Schema(description = "交易状态", example = "1")
    @ExcelProperty("交易状态")
    private String status;
    @Schema(description = "子合同统计")
    @ExcelProperty("子合同统计")
    private Map<String, Double> childSum;

    @Schema(description = "子合同列表")
    @ExcelProperty("子合同列表")
    private List<ContractsRespVO> child;
}