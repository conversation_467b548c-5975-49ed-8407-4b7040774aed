package cn.iocoder.yudao.module.et.controller.admin.contractsequipment;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.contractsequipment.vo.ContractsEquipmentPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractsequipment.vo.ContractsEquipmentRespVO;
import cn.iocoder.yudao.module.et.controller.admin.contractsequipment.vo.ContractsEquipmentSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsequipment.ContractsEquipmentDO;
import cn.iocoder.yudao.module.et.service.contractsequipment.ContractsEquipmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合同机组信息")
@RestController
@RequestMapping("/jy/contracts-equipment")
@Validated
public class ContractsEquipmentController {

    @Resource
    private ContractsEquipmentService contractsEquipmentService;

    @PostMapping("/create")
    @Operation(summary = "创建合同机组信息")
    @PreAuthorize("@ss.hasPermission('jy:contracts-equipment:create')")
    public CommonResult<Long> createContractsEquipment(@Valid @RequestBody ContractsEquipmentSaveReqVO createReqVO) {
        return success(contractsEquipmentService.createContractsEquipment(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新合同机组信息")
    @PreAuthorize("@ss.hasPermission('jy:contracts-equipment:update')")
    public CommonResult<Boolean> updateContractsEquipment(@Valid @RequestBody ContractsEquipmentSaveReqVO updateReqVO) {
        contractsEquipmentService.updateContractsEquipment(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除合同机组信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('jy:contracts-equipment:delete')")
    public CommonResult<Boolean> deleteContractsEquipment(@RequestParam("id") Long id) {
        contractsEquipmentService.deleteContractsEquipment(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得合同机组信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('jy:contracts-equipment:query')")
    public CommonResult<ContractsEquipmentRespVO> getContractsEquipment(@RequestParam("id") Long id) {
        ContractsEquipmentDO contractsEquipment = contractsEquipmentService.getContractsEquipment(id);
        return success(BeanUtils.toBean(contractsEquipment, ContractsEquipmentRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得合同机组信息分页")
    @PreAuthorize("@ss.hasPermission('jy:contracts-equipment:query')")
    public CommonResult<PageResult<ContractsEquipmentRespVO>> getContractsEquipmentPage(@Valid ContractsEquipmentPageReqVO pageReqVO) {
        PageResult<ContractsEquipmentDO> pageResult = contractsEquipmentService.getContractsEquipmentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContractsEquipmentRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出合同机组信息 Excel")
    @PreAuthorize("@ss.hasPermission('jy:contracts-equipment:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContractsEquipmentExcel(@Valid ContractsEquipmentPageReqVO pageReqVO,
                                              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ContractsEquipmentDO> list = contractsEquipmentService.getContractsEquipmentPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "合同机组信息.xls", "数据", ContractsEquipmentRespVO.class,
                BeanUtils.toBean(list, ContractsEquipmentRespVO.class));
    }

}