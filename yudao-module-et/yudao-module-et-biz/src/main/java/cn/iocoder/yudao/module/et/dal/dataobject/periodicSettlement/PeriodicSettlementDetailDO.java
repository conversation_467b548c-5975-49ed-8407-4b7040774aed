package cn.iocoder.yudao.module.et.dal.dataobject.periodicSettlement;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 市场周期结算_电厂_结算单详情
 *
 * <AUTHOR>
 * @date 2024-11-14
 **/
@Accessors(chain = true)
@NoArgsConstructor
@Data
@TableName("jy_periodic_settlement_detail")
public class PeriodicSettlementDetailDO {
    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 关联主表ID
     */
    private Long pid;
    /**
     * 名称
     */
    private String name;
    /**
     * 数值
     */
    private BigDecimal value;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 调度单元编码（场站编码）
     */
    private String dispatchNodeCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherDate;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
}
