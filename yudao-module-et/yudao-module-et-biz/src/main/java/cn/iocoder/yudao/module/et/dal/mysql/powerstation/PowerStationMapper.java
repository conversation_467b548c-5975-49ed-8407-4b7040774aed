package cn.iocoder.yudao.module.et.dal.mysql.powerstation;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.powerstation.vo.PowerStationPageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 场站信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PowerStationMapper extends BaseMapperX<PowerStationDO> {

    default PageResult<PowerStationDO> selectPage(PowerStationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PowerStationDO>()
                .eqIfPresent(PowerStationDO::getCode, reqVO.getCode())
                .likeIfPresent(PowerStationDO::getName, reqVO.getName())
                .likeIfPresent(PowerStationDO::getFullName, reqVO.getFullName())
                .likeIfPresent(PowerStationDO::getDispatchName, reqVO.getDispatchName())
                .eqIfPresent(PowerStationDO::getSort, reqVO.getSort())
                .eqIfPresent(PowerStationDO::getType, reqVO.getType())
                .eqIfPresent(PowerStationDO::getCapacity, reqVO.getCapacity())
                .eqIfPresent(PowerStationDO::getQuantity, reqVO.getQuantity())
                .eqIfPresent(PowerStationDO::getEnabled, reqVO.getEnabled())
                .betweenIfPresent(PowerStationDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(PowerStationDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(PowerStationDO::getOrgCode, reqVO.getOrgCode())
                .orderByDesc(PowerStationDO::getId));
    }

    @TenantIgnore
    @Select("select * from jy_power_station")
    List<PowerStationDO> selectAllByTenantIgnore();


}