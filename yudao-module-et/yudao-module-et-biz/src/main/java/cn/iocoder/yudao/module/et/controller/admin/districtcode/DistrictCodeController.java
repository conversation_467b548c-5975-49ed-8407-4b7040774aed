package cn.iocoder.yudao.module.et.controller.admin.districtcode;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.et.controller.admin.districtcode.vo.DistrictCodePageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.districtcode.vo.DistrictCodeRespVO;
import cn.iocoder.yudao.module.et.controller.admin.districtcode.vo.DistrictCodeSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.districtcode.DistrictCodeDO;
import cn.iocoder.yudao.module.et.service.districtcode.DistrictCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 行政区划")
@RestController
@RequestMapping("/et/district-code")
@Validated
public class DistrictCodeController {

    @Resource
    private DistrictCodeService districtCodeService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @PostMapping("/create")
    @Operation(summary = "创建行政区划")
    @PreAuthorize("@ss.hasPermission('jy:district-code:create')")

    public CommonResult<Integer> createDistrictCode(@Valid @RequestBody DistrictCodeSaveReqVO createReqVO) {
        return success(districtCodeService.createDistrictCode(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新行政区划")
    @PreAuthorize("@ss.hasPermission('jy:district-code:update')")
    public CommonResult<Boolean> updateDistrictCode(@Valid @RequestBody DistrictCodeSaveReqVO updateReqVO) {
        districtCodeService.updateDistrictCode(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除行政区划")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('jy:district-code:delete')")
    public CommonResult<Boolean> deleteDistrictCode(@RequestParam("id") Integer id) {
        districtCodeService.deleteDistrictCode(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得行政区划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('jy:district-code:query')")
    public CommonResult<DistrictCodeRespVO> getDistrictCode(@RequestParam("id") Integer id) {
        DistrictCodeDO districtCode = districtCodeService.getDistrictCode(id);
        return success(BeanUtils.toBean(districtCode, DistrictCodeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得行政区划分页")
    @PreAuthorize("@ss.hasPermission('jy:district-code:query')")
    public CommonResult<PageResult<DistrictCodeRespVO>> getDistrictCodePage(@Valid DistrictCodePageReqVO pageReqVO) {
        PageResult<DistrictCodeDO> pageResult = districtCodeService.getDistrictCodePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DistrictCodeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出行政区划 Excel")
    @PreAuthorize("@ss.hasPermission('jy:district-code:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDistrictCodeExcel(@Valid DistrictCodePageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DistrictCodeDO> list = districtCodeService.getDistrictCodePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "行政区划.xls", "数据", DistrictCodeRespVO.class,
                BeanUtils.toBean(list, DistrictCodeRespVO.class));
    }

    @GetMapping("/districtCodeTree")
    @Operation(summary = "资源分析 行政区划层级关系")
    public CommonResult<List<Object>> getDistrictCodeTree() throws IOException {

        Map<Integer, List<DistrictCodeDO>> mapList = new HashMap<>();
        Long tenantId = TenantContextHolder.getTenantId();
        String diQuCode;
        if (tenantId == 167) {
            diQuCode = "23";
        } else {
            diQuCode = "";
        }
        List<DistrictCodeDO> list = districtCodeService.getDistrictCodeTree(diQuCode);

        if (!list.isEmpty()) {
            list = list.stream().filter(x -> x.getCode().toString().startsWith(diQuCode)).toList();
        }
        Map<String, Map<String, Object>> shiMapResult = new HashMap<>();
        if (!list.isEmpty()) {
            mapList = list.stream().collect(Collectors.groupingBy(DistrictCodeDO::getPid));


            //遍历市级关系树
            mapList.forEach((k, v) -> {
                if (k.toString().endsWith("0000")) {
                    if (!v.isEmpty()) {
                        v.forEach(x -> {
                            Map<String, Object> shiMap = new HashMap<>();
                            shiMap.put("value", x.getCode());
                            shiMap.put("label", x.getPname() + "-" + x.getName());
                            shiMapResult.put(x.getName(), shiMap);
                        });

                    }

                }
            });
            //遍历县区关系树
            List<DistrictCodeDO> finalList = list;
            mapList.forEach((k, v) -> {

                if (!k.toString().endsWith("0000")) {
                    List<Object> quList = new ArrayList<>();

                    if (!v.isEmpty()) {
                        v.forEach(x -> {
                            if (x.getLevel() == 3) {
                                Map<String, Object> quMap = new HashMap<>();
                                quMap.put("label", x.getName());
                                quMap.put("value", x.getCode());
                                quMap.put("code", x.getCode());
                                quList.add(quMap);
                            }
                        });
                    }
                    if (!quList.isEmpty()) {
                        List<DistrictCodeDO> districtCodeDO = finalList.stream().filter(x -> x.getCode().equals(k)).toList();
                        if (!districtCodeDO.isEmpty()) {
                            Map<String, Object> shiMap1 = shiMapResult.get(districtCodeDO.get(0).getName());
                            if (null != shiMap1) {
                                shiMap1.put("children", quList);
                            }
                        }
                    }
                }
            });
        }
        List<Object> resultList = new ArrayList<>();
        List<Object> allTreeList = new ArrayList<>();
        shiMapResult.forEach((x, v) -> {
            allTreeList.add(v);
        });
        resultList.add(allTreeList);
        //获取当前用户缓存的查询树
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String cacheCodeTree = "[{\"label\":\"黑龙江省-哈尔滨市\",\"value\":\"230100\",\"code\":\"230100\"},{\"label\":\"黑龙江省-齐齐哈尔市\",\"value\":\"230200\",\"code\":\"230200\"},{\"label\":\"黑龙江省-鸡西市\",\"value\":\"230300\",\"code\":\"230300\"},{\"label\":\"黑龙江省-七台河市\",\"value\":\"230900\",\"code\":\"230900\"},{\"label\":\"黑龙江省-牡丹江市\",\"value\":\"231000\",\"code\":\"231000\"},{\"label\":\"黑龙江省-绥化市\",\"value\":\"231200\",\"code\":\"231200\"}]";
        if (null != userId) {
            String cacheCodeTreeTemp = stringRedisTemplate.opsForValue().get("et_resources_" + userId + "");
            if (StrUtil.isNotBlank(cacheCodeTreeTemp)) {
                cacheCodeTree = cacheCodeTreeTemp;
            }
        }
        resultList.add(cacheCodeTree);

        return success(resultList);
    }

}