package cn.iocoder.yudao.module.et.dal.tdengine.dataproc;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 私有（交易单元级）数据处理类
 *
 * <AUTHOR>
 * @date 2024-10-19
 **/
@DS("tdengine")
@Mapper
public interface PrivateDataProcMapper extends BaseMapperX<TradingUnitDO> {
    /**
     * GS-B-512 现货市场结算新-电厂侧结算展示-结算电量_电厂
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO trading_unit_${unitId}",
        "(ts, deviation_rate, tmr_power, scada_power, real_use_power, calibration_power, settlement_power)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.deviationRate}, #{item.tmrPower}, #{item.scadaPower}, #{item.realUsePower}, #{item.calibrationPower}, #{item.settlementPower})",
        "</foreach>",
        "</script>"
    })
    Integer insertClearingQuantity(@Param("unitId") String unitId, List<TradingUnitDO> list);

    /**
     *  中长期持有量
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO trading_unit_${unitId}",
        "(ts, medium_long_term_settlement_curves)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.mediumLongTermSettlementCurves})",
        "</foreach>",
        "</script>"
    })
    Integer insertMediumLongTermSettlementCurves(@Param("unitId") String unitId, List<TradingUnitDO> list);

    /**
     * GS-B-511 现货市场结算新-电厂侧结算展示-结算发布_电厂
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO trading_unit_${unitId}",
        "(ts,his_positive_power,his_negative_power,his_positive_prices,his_negative_prices,his_generate_electricity_plan,medium_long_term_plan,his_province_clearance,his_market_settlement_prices,real_positive_power,real_negative_power,real_positive_prices,real_negative_prices,real_province_clearance,cross_provincial_peak_load_balancing_plan,cross_provincial_peak_load_balancing_return,real_settlement_power,real_market_settlement_prices,emergency_invoke_start_up_compensation_prices,thermal_power_start_up_compensation_prices,deviation_review_prices,recovery_of_excess_profits_from_new_energy_issuance_prices,his_standby_clearance,real_standby_clearance,medium_long_term_blockage_prices,blocking_risk_hedging_prices,modified_compensation_prices,necessary_start_up_compensation_prices)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.hisPositivePower}, #{item.hisNegativePower}, #{item.hisPositivePrices}, #{item.hisNegativePrices}, #{item.hisGenerateElectricityPlan}, #{item.mediumLongTermPlan}, #{item.hisProvinceClearance}, #{item.hisMarketSettlementPrices}, #{item.realPositivePower}, #{item.realNegativePower}, #{item.realPositivePrices}, #{item.realNegativePrices}, #{item.realProvinceClearance}, #{item.crossProvincialPeakLoadBalancingPlan}, #{item.crossProvincialPeakLoadBalancingReturn}, #{item.realSettlementPower}, #{item.realMarketSettlementPrices}, #{item.emergencyInvokeStartUpCompensationPrices}, #{item.thermalPowerStartUpCompensationPrices}, #{item.deviationReviewPrices}, #{item.recoveryOfExcessProfitsFromNewEnergyIssuancePrices}, #{item.hisStandbyClearance}, #{item.realStandbyClearance}, #{item.mediumLongTermBlockagePrices}, #{item.blockingRiskHedgingPrices}, #{item.modifiedCompensationPrices}, #{item.necessaryStartUpCompensationPrices})",
        "</foreach>",
        "</script>"
    })
    Integer insertClearingResultsNew(@Param("unitId") String unitId, List<TradingUnitDO> list);

    /**
     * GS-B-611 现货市场结算-电厂侧结算展示-结算发布_电厂
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO trading_unit_${unitId}",
        "(ts,medium_long_term_fusion,his_positive_power,his_negative_power,his_positive_prices,his_negative_prices,his_settlement_free_power,his_generate_electricity_plan,medium_long_term_plan,his_province_clearance,his_market_settlement_prices,real_positive_power,real_negative_power,real_positive_prices,real_negative_prices,real_settlement_free_power,real_province_clearance,cross_provincial_peak_load_balancing_plan,cross_provincial_peak_load_balancing_return,real_settlement_power,real_market_settlement_prices,jisco_bpo,jisco_bilateral_transactions,review_power,deviation_review_prices,emergency_invoke_start_up_compensation_prices,fmergency_invoke_start_up_on_grid_power,adjustment_frequency_addition_energy_compensation,adjustment_frequency_lessen_energy_compensation,thermal_power_start_up_compensation_prices,unplanned_outage_assessment,his_standby_clearance,real_standby_clearance)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.mediumLongTermFusion}, #{item.hisPositivePower}, #{item.hisNegativePower}, #{item.hisPositivePrices}, #{item.hisNegativePrices}, #{item.hisSettlementFreePower}, #{item.hisGenerateElectricityPlan}, #{item.mediumLongTermPlan}, #{item.hisProvinceClearance}, #{item.hisMarketSettlementPrices}, #{item.realPositivePower}, #{item.realNegativePower}, #{item.realPositivePrices}, #{item.realNegativePrices}, #{item.realSettlementFreePower}, #{item.realProvinceClearance}, #{item.crossProvincialPeakLoadBalancingPlan}, #{item.crossProvincialPeakLoadBalancingReturn}, #{item.realSettlementPower}, #{item.realMarketSettlementPrices}, #{item.jiscoBpo}, #{item.jiscoBilateralTransactions}, #{item.reviewPower}, #{item.deviationReviewPrices}, #{item.emergencyInvokeStartUpCompensationPrices}, #{item.fmergencyInvokeStartUpOnGridPower}, #{item.adjustmentFrequencyAdditionEnergyCompensation}, #{item.adjustmentFrequencyLessenEnergyCompensation}, #{item.thermalPowerStartUpCompensationPrices}, #{item.unplannedOutageAssessment}, #{item.hisStandbyClearance}, #{item.realStandbyClearance})",
        "</foreach>",
        "</script>"
    })
    Integer insertClearingResults(@Param("unitId") String unitId, List<TradingUnitDO> list);

    /**
     * GS-B-251 市场出清监视
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO trading_unit_${unitId}",
        "(ts,medium_long_term_settlement_curves,self_plan,prebalance_plan,reliability_plan,bilateral_clearance,real_plan,real_scada_power,short_term_forecast,ultra_short_term_forecast,cross_provincial_spot_clearing_of_electricity,real_basis_clearance)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.mediumLongTermSettlementCurves}, #{item.selfPlan}, #{item.prebalancePlan}, #{item.reliabilityPlan}, #{item.bilateralClearance}, #{item.realPlan}, #{item.realScadaPower}, #{item.shortTermForecast}, #{item.ultraShortTermForecast}, #{item.crossProvincialSpotClearingOfElectricity}, #{item.realBasisClearance})",
        "</foreach>",
        "</script>"
    })
    Integer insertClearingMonitoring(@Param("unitId") String unitId, List<TradingUnitDO> list);

    /**
     * GS-B-231 日前市场化出清结果-电厂侧日前市场化结算价格限制前后对比展示
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO trading_unit_${unitId}",
        "(ts,his_settlement_prices_limit)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.hisSettlementPricesLimit})",
        "</foreach>",
        "</script>"
    })
    Integer insertDayAheadPriceComparison(@Param("unitId") String unitId, List<TradingUnitDO> list);

    /**
     * GS-B-241 实时市场出清结果-电厂侧实时结算价格
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO trading_unit_${unitId}",
        "(ts,real_settlement_prices)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.realSettlementPrices})",
        "</foreach>",
        "</script>"
    })
    Integer insertRealTimePrice(@Param("unitId") String unitId, List<TradingUnitDO> list);

    /**
     * GS-B-421 中长期计划管理-中长期曲线叠加展示
     **/
    @TenantIgnore
    @Insert({
        "<script>",
        "INSERT INTO trading_unit_${unitId}",
        "(ts,radix_power,delivery_power,bilateral_power,day_scroll_power,long_term_contract_power)",
        " VALUES ",
        "<foreach collection='list' item='item' separator=','> ",
        "(#{item.ts}, #{item.radixPower}, #{item.deliveryPower}, #{item.bilateralPower}, #{item.dayScrollPower}, #{item.longTermContractPower})",
        "</foreach>",
        "</script>"
    })
    Integer insertContractCurveSuperposition(@Param("unitId") String unitId, List<TradingUnitDO> list);
}
