package cn.iocoder.yudao.module.et.controller.admin.contractspowercurve;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.contractspowercurve.vo.ContractsPowerCurvePageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractspowercurve.vo.ContractsPowerCurveRespVO;
import cn.iocoder.yudao.module.et.controller.admin.contractspowercurve.vo.ContractsPowerCurveSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractspowercurve.ContractsPowerCurveDO;
import cn.iocoder.yudao.module.et.service.contractspowercurve.ContractsPowerCurveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 电力曲线")
@RestController
@RequestMapping("/jy/contracts-power-curve")
@Validated
public class ContractsPowerCurveController {

    @Resource
    private ContractsPowerCurveService contractsPowerCurveService;

    @PostMapping("/create")
    @Operation(summary = "创建电力曲线")
    @PreAuthorize("@ss.hasPermission('jy:contracts-power-curve:create')")
    public CommonResult<Long> createContractsPowerCurve(@Valid @RequestBody ContractsPowerCurveSaveReqVO createReqVO) {
        return success(contractsPowerCurveService.createContractsPowerCurve(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新电力曲线")
    @PreAuthorize("@ss.hasPermission('jy:contracts-power-curve:update')")
    public CommonResult<Boolean> updateContractsPowerCurve(@Valid @RequestBody ContractsPowerCurveSaveReqVO updateReqVO) {
        contractsPowerCurveService.updateContractsPowerCurve(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除电力曲线")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('jy:contracts-power-curve:delete')")
    public CommonResult<Boolean> deleteContractsPowerCurve(@RequestParam("id") Long id) {
        contractsPowerCurveService.deleteContractsPowerCurve(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得电力曲线")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('jy:contracts-power-curve:query')")
    public CommonResult<ContractsPowerCurveRespVO> getContractsPowerCurve(@RequestParam("id") Long id) {
        ContractsPowerCurveDO contractsPowerCurve = contractsPowerCurveService.getContractsPowerCurve(id);
        return success(BeanUtils.toBean(contractsPowerCurve, ContractsPowerCurveRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得电力曲线分页")
    @PreAuthorize("@ss.hasPermission('jy:contracts-power-curve:query')")
    public CommonResult<PageResult<ContractsPowerCurveRespVO>> getContractsPowerCurvePage(@Valid ContractsPowerCurvePageReqVO pageReqVO) {
        PageResult<ContractsPowerCurveDO> pageResult = contractsPowerCurveService.getContractsPowerCurvePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContractsPowerCurveRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出电力曲线 Excel")
    @PreAuthorize("@ss.hasPermission('jy:contracts-power-curve:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContractsPowerCurveExcel(@Valid ContractsPowerCurvePageReqVO pageReqVO,
                                               HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ContractsPowerCurveDO> list = contractsPowerCurveService.getContractsPowerCurvePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "电力曲线.xls", "数据", ContractsPowerCurveRespVO.class,
                BeanUtils.toBean(list, ContractsPowerCurveRespVO.class));
    }

}