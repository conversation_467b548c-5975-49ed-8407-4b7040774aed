package cn.iocoder.yudao.module.et.controller.admin.dispatchnode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 交易单元新增/修改 Request VO")
@Data
public class DispatchNodeSaveReqVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "7060")
    private Integer id;

    @Schema(description = "调度单元编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "调度单元编码不能为空")
    private String code;

    @Schema(description = "调度单元名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "调度单元名称不能为空")
    private String name;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "场站装机容量")
    private Double capacity;

    @Schema(description = "场站设备台数")
    private Integer quantity;

    @Schema(description = "二区现货系统账号")
    private String sysUser;

    @Schema(description = "二区现货系统密码")
    private String sysPwd;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否禁用不能为空")
    private Boolean enabled;

    @Schema(description = "部门ID", example = "26902")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    private String orgCode;

}