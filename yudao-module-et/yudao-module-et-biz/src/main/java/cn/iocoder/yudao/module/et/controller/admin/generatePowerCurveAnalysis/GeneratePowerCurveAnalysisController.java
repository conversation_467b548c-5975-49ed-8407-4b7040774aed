package cn.iocoder.yudao.module.et.controller.admin.generatePowerCurveAnalysis;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.et.controller.admin.tradingunit.vo.TradingUnitRespVO;
import cn.iocoder.yudao.module.et.service.generatePowerCurveAnalysis.GeneratePowerCurveAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 发电能力曲线分析")
@RestController
@RequestMapping("/et/generatePowerCurveAnalysis")
@Validated
public class GeneratePowerCurveAnalysisController {

    @Resource
    private GeneratePowerCurveAnalysisService generatePowerCurveAnalysisService;


    @PostMapping("/getGeneratePowerCurveAnalysis")
    @Operation(summary = "市场动态 发电能力曲线分析")
    public CommonResult<Map<String, Object>> getGeneratePowerCurveAnalysis(@RequestBody TradingUnitRespVO tradingUnitRespVO) {
        return success(generatePowerCurveAnalysisService.getGeneratePowerCurveAnalysis(tradingUnitRespVO));
    }

}
