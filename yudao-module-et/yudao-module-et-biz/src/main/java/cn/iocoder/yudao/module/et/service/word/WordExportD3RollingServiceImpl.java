package cn.iocoder.yudao.module.et.service.word;

import cn.iocoder.yudao.module.et.controller.admin.reviewinfo.vo.ReviewInfoVO;
import cn.iocoder.yudao.module.et.service.reviewd3details.ReviewD3DetailsService;
import cn.iocoder.yudao.module.et.util.WordUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * word导出 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WordExportD3RollingServiceImpl implements WordExportD3RollingService {
    private static final String TEMPLATE_PATH = "templates/日滚动模版.docx";
    private static final String OUTPUT_FILENAME = "D+3日滚动交易运行日志.docx";
    private static final String WEATHER_TABLE_PLACEHOLDER = "${weatherTable}";
    private static final String CHARTS_PLACEHOLDER = "${chartData}";
    private static final String STRATEGY_TABLE_PLACEHOLDER = "${strategyTable}";
    @Resource
    ReviewD3DetailsService reviewD3DetailsService;

    @Override
    public void exportD3RollingLog(HttpServletResponse response, ReviewInfoVO reviewInfoVO) throws Exception {
        Map<String, Object> dataMap = reviewD3DetailsService.getDailyRollingReview(reviewInfoVO.getReviewInfoId());
        try (InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(TEMPLATE_PATH);
             XWPFDocument document = new XWPFDocument(inputStream);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            //文字替换
            processTables(document, dataMap);
            //模版原因 不知道为什么节点电价情况文本 交易策略文本 今日运行人员文本无法替换 单独手动替换
            replaceText(document, dataMap);
            //天气情况表格
            updateWeatherTable(document, dataMap);
            //节点电价曲线图
            updateCharts(document, dataMap);
            //交易策略表格
            buildStrategyTable(document, dataMap);

            document.write(outputStream);
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=" + OUTPUT_FILENAME);
            response.getOutputStream().write(outputStream.toByteArray());
        }
    }

    private void replaceText(XWPFDocument document, Map<String, Object> dataMap) {
        //节点电价文本
        XWPFTableCell targetCell = findTableCellByPlaceholder(document, "${priceText}");
        if (targetCell == null) {
            System.err.println("未找到节点电价文本占位符：" + "${priceText}");
            return;
        }
        // 插入新表格前彻底清空占位符
        clearCellContent(targetCell);
        XWPFParagraph paragraph = targetCell.addParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText(dataMap.get("priceText").toString());

        //交易策略文本
        XWPFTableCell strategyTextCell = findTableCellByPlaceholder(document, "${strategyText}");
        if (strategyTextCell == null) {
            System.err.println("未找到交易策略文本占位符：" + "${strategyText}");
            return;
        }
        // 插入新表格前彻底清空占位符
        clearCellContent(strategyTextCell);
        XWPFParagraph strategyTextParagraph = strategyTextCell.addParagraph();
        XWPFRun strategyTextRun = strategyTextParagraph.createRun();
        strategyTextRun.setText(dataMap.get("strategyText").toString());

        //今日运行人员文本     这个不在表格里不能用寻找方法
        // 获取目标段落（假设是文档的第二个段落）
        XWPFParagraph peopleParagraph = document.getParagraphs().get(1);

        // 1. 合并段落中所有 Run 的文本
        StringBuilder mergedText = new StringBuilder();
        for (XWPFRun peopleParagraphRun : peopleParagraph.getRuns()) {
            String text = peopleParagraphRun.getText(0);
            if (text != null) {
                mergedText.append(text);
            }
        }
        String fullText = mergedText.toString();
        String placeholder = "${operatingDayPersons}";
        if (fullText.contains(placeholder)) {
            // 3. 替换占位符为实际值
            String replacement = dataMap.get("operatingDayPersons").toString();
            String newText = fullText.replace(placeholder, replacement);

            // 4. 清空原有所有 Runs
            for (int i = peopleParagraph.getRuns().size() - 1; i >= 0; i--) {
                peopleParagraph.removeRun(i);
            }

            // 5. 创建新 Run 并插入替换后的文本
            XWPFRun newRun = peopleParagraph.createRun();
            newRun.setText(newText);
        }
    }


    private void processTables(XWPFDocument document, Map<String, Object> dataMap) {
        // 文本替换逻辑不变（无 POI 版本相关代码）
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    List<XWPFParagraph> paragraphs = cell.getParagraphs();
                    boolean hasPlaceholders = false;

                    for (XWPFParagraph para : paragraphs) {
                        for (XWPFRun run : para.getRuns()) {
                            String text = run.getText(0);
                            if (text != null && text.contains("${")) {
                                hasPlaceholders = true;
                                break;
                            }
                        }
                        if (hasPlaceholders) break;
                    }

                    if (!hasPlaceholders) continue;

                    for (XWPFParagraph para : paragraphs) {
                        List<XWPFRun> runs = para.getRuns();
                        for (int i = 0; i < runs.size(); i++) {
                            XWPFRun run = runs.get(i);
                            String originalText = run.getText(0);
                            if (originalText == null || !originalText.contains("${")) continue;

                            Map<String, String> stringDataMap = new HashMap<>();
                            dataMap.forEach((key, value) ->
                                    stringDataMap.put(key, value != null ? value.toString() : "")
                            );
                            String replacedText = WordUtil.replacePlaceholders(originalText, stringDataMap);
                            run.setText(replacedText, 0);
                        }
                    }
                }
            }
        }
    }

    private void updateWeatherTable(XWPFDocument document, Map<String, Object> dataMap) {
        XWPFTableCell targetCell = findTableCellByPlaceholder(document, WEATHER_TABLE_PLACEHOLDER);
        if (targetCell == null) {
            System.err.println("未找到天气表格占位符：" + WEATHER_TABLE_PLACEHOLDER);
            return;
        }

        // 插入新表格前彻底清空占位符
        clearCellContent(targetCell);
        buildWeatherTable(document, targetCell, (Map<String, Object>) dataMap.get("weatherTable"), (List<String>) dataMap.get("loadForecast"));
    }

    private XWPFTableCell findTableCellByPlaceholder(XWPFDocument document, String placeholder) {
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph para : cell.getParagraphs()) {
                        String text = para.getText();
                        if (text != null && text.trim().equals(placeholder.trim())) {
                            // 找到占位符后，直接清空该段落
                            for (XWPFRun run : para.getRuns()) {
                                run.setText("", 0); // 清空所有运行的文本
                            }
                            return cell;
                        }
                    }
                }
            }
        }
        return null;
    }

    private void clearCellContent(XWPFTableCell cell) {
        // 1. 清空所有段落（包括底层 XML 节点）
        List<XWPFParagraph> paragraphs = cell.getParagraphs();
        for (int i = paragraphs.size() - 1; i >= 0; i--) {
            cell.removeParagraph(i);
        }

        // 2. 清空所有子表格（通过底层 XML 操作）
        CTTc ctTc = cell.getCTTc();
        for (int i = ctTc.sizeOfTblArray() - 1; i >= 0; i--) {
            ctTc.removeTbl(i);
        }

        // 3. 强制刷新单元格内容（确保 XML 结构更新）
        cell.getCTTc().setPArray(new CTP[0]);
    }

    // 辅助方法：设置单个边框属性
    private void setBorder(CTBorder border, STBorder.Enum style, int sizeEighthPoints, String color) {
        border.setVal(style); // 边框类型
        border.setSz(BigInteger.valueOf(sizeEighthPoints)); // 宽度（单位：1/8点）
        border.setColor(color); // 颜色
    }

    private void buildWeatherTable(XWPFDocument document, XWPFTableCell cell, Map<String, Object> weatherData, List<String> loadForecast) {
        // 清空单元格内容
        clearCellContent(cell);

        // 获取当前日期（使用 Hutool）
        String title = weatherData.get("title").toString();

        // 创建底层表格结构
        CTTc ctTc = cell.getCTTc();
        CTTbl ctTbl = ctTc.addNewTbl();

        // =============== 1. 标题行（合并所有列） ===============
        {
            CTRow ctTitleRow = ctTbl.addNewTr();
            CTTc titleCell = ctTitleRow.addNewTc();

            // 合并列（总列数 = 1 + 2 * showDays）
            CTTcPr titleCellPr = titleCell.addNewTcPr();
            titleCellPr.addNewGridSpan().setVal(BigInteger.valueOf(15));

            // 设置标题内容
            CTP ctP = titleCell.addNewP();
            CTR ctR = ctP.addNewR();
            ctR.addNewT().setStringValue(title);

            // 样式：居中+加粗+小六号字
            CTPPr pPr = ctP.addNewPPr();
            pPr.addNewWidowControl().setVal(STOnOff.FALSE); // 关闭段落换行控制
            pPr.addNewJc().setVal(STJc.CENTER);
            CTRPr rPr = ctR.addNewRPr();
            rPr.addNewB().setVal(STOnOff.TRUE);
            rPr.addNewSz().setVal(BigInteger.valueOf(16)); // 小六号字（14半磅）
        }

        // =============== 2. 第一表头行（日期行） ===============
        {
            List<String> header1 = (List<String>) weatherData.get("header1");
            CTRow ctHeaderRow1 = ctTbl.addNewTr();

            // 第一列：固定"日期"
            CTTc dateHeaderCell = ctHeaderRow1.addNewTc();
            CTP dateP = dateHeaderCell.addNewP();
            dateP.addNewPPr().addNewJc().setVal(STJc.CENTER); // 居中
            CTR dateR = dateP.addNewR();
            dateR.addNewT().setStringValue(header1.get(0));
            dateR.addNewRPr().addNewSz().setVal(BigInteger.valueOf(12));
            // 动态生成日期列（每日期占2列）
            for (int i = 1; i <= header1.size() - 1; i++) {

                // 创建合并单元格（占2列）
                CTTc dateCell = ctHeaderRow1.addNewTc();
                CTTcPr cellPr = dateCell.addNewTcPr();
                cellPr.addNewGridSpan().setVal(BigInteger.valueOf(2)); // 合并2列

                // 设置日期内容
                CTP ctP = dateCell.addNewP();
                ctP.addNewPPr().addNewJc().setVal(STJc.CENTER);
                CTR ctR = ctP.addNewR();
                ctR.addNewT().setStringValue(header1.get(i));
                ctR.addNewRPr().addNewSz().setVal(BigInteger.valueOf(12)); // 小八号字
            }
        }

        // =============== 3. 第二表头行（天气/风速） ===============
        {
            CTRow ctHeaderRow2 = ctTbl.addNewTr();
            List<String> header2 = (List<String>) weatherData.get("header2");
            // 第一列：固定"项目"
            CTTc projectHeaderCell = ctHeaderRow2.addNewTc();
            projectHeaderCell.addNewTcPr().addNewShd().setFill("D3D3D3");
            CTP projectP = projectHeaderCell.addNewP();
            projectP.addNewPPr().addNewJc().setVal(STJc.CENTER);
            CTR projectR = projectP.addNewR();
            projectR.addNewT().setStringValue(header2.get(0));
            projectR.addNewRPr().addNewSz().setVal(BigInteger.valueOf(12));
            // 动态生成天气/风速列
            for (int i = 1; i <= header2.size() - 1; i++) {
                // 天气列
                CTTc weatherCell = ctHeaderRow2.addNewTc();
                weatherCell.addNewTcPr().addNewShd().setFill("D3D3D3"); // 灰色背景
                CTP weatherP = weatherCell.addNewP();
                weatherP.addNewPPr().addNewJc().setVal(STJc.CENTER);
                CTR weatherR = weatherP.addNewR();
                weatherR.addNewT().setStringValue(header2.get(i));
                weatherR.addNewRPr().addNewSz().setVal(BigInteger.valueOf(12));
            }
        }
        // =============== 4. 设置列宽 & 表格布局 ===============
        CTTblGrid grid = ctTbl.addNewTblGrid();
        grid.addNewGridCol().setW(BigInteger.valueOf(920)); // 第一列
        for (int i = 0; i < 14; i++) {
            grid.addNewGridCol().setW(BigInteger.valueOf(590)); // 其他列
        }

        // 关键点1：全局设置表格固定布局（只执行一次）
        CTTblPr tblPr = ctTbl.getTblPr() != null ? ctTbl.getTblPr() : ctTbl.addNewTblPr();
        CTTblLayoutType layout = tblPr.isSetTblLayout() ? tblPr.getTblLayout() : tblPr.addNewTblLayout();
        layout.setType(STTblLayoutType.FIXED); // 固定布局
        // =============== 5. 设置表格边框 ===============
        CTTblBorders borders = tblPr.addNewTblBorders();

        // 统一设置边框样式（POI 4.1.2 兼容写法）
        STBorder.Enum borderStyle = STBorder.SINGLE; // 单线边框
        String borderColor = "000000"; // 黑色

        // 设置各方向边框
        setBorder(borders.addNewTop(), borderStyle, 4, borderColor);
        setBorder(borders.addNewBottom(), borderStyle, 4, borderColor);
        setBorder(borders.addNewLeft(), borderStyle, 4, borderColor);
        setBorder(borders.addNewRight(), borderStyle, 4, borderColor);
        setBorder(borders.addNewInsideH(), borderStyle, 4, borderColor);
        setBorder(borders.addNewInsideV(), borderStyle, 4, borderColor);


        // =============== 6. 填充数据行（禁止换行+固定列宽） ===============
        String[] projects = {"武威", "民勤", "临泽", "玉门", "敦煌"};
        for (int rowIdx = 0; rowIdx < projects.length; rowIdx++) {
            CTRow ctDataRow = ctTbl.addNewTr();
            boolean needGray = (rowIdx % 2) == 0;

            // 第一列：项目名称
            CTTc projectCell = ctDataRow.addNewTc();
            CTTcPr projectTcPr = projectCell.addNewTcPr();
            projectTcPr.addNewNoWrap(); // 关键点2：仅保留禁止换行


            // 设置背景色（修复Shd错误）
            if (!needGray) {
                CTShd shd = projectTcPr.addNewShd();
                shd.setFill("D3D3D3");
                shd.setVal(STShd.CLEAR); // 正确设置图案类型
            }

            // 设置单元格内容
            CTP projectP = projectCell.addNewP();
            projectP.addNewPPr().addNewJc().setVal(STJc.CENTER);
            CTR projectR = projectP.addNewR();
            projectR.addNewT().setStringValue(projects[rowIdx].replaceAll("[\\n\\t]", ""));

            // 设置字体样式
            CTRPr projectRPr = projectR.addNewRPr();
            projectRPr.addNewSz().setVal(BigInteger.valueOf(12));
            projectRPr.addNewColor().setVal("000000");

            // 填充其他列
            for (int i = 0; i < 14; i++) {
                CTTc dataCell = ctDataRow.addNewTc();
                CTTcPr dataTcPr = dataCell.addNewTcPr();

                // 新增1：强制固定列宽（必须与tblGrid中定义的宽度完全一致）
                dataTcPr.addNewTcW().setW(BigInteger.valueOf(590)); // 590对应动态列的宽度
                dataTcPr.addNewTcW().setType(STTblWidth.DXA);
                dataTcPr.addNewNoWrap(); // 禁止换行
                // 新增：垂直居中
                dataTcPr.addNewVAlign().setVal(STVerticalJc.CENTER);
                dataTcPr.addNewNoWrap().setVal(STOnOff.TRUE);
                // 修复背景色设置
                if (!needGray) {
                    CTShd dataShd = dataTcPr.addNewShd();
                    dataShd.setFill("D3D3D3");
                    dataShd.setVal(STShd.CLEAR);
                }

                // 设置内容（处理空格）
                List<Object> data = (List<Object>) weatherData.get(projects[rowIdx]);
                String rawValue = (i < data.size()) ? data.get(i).toString() : "N/A";
                String cellValue = rawValue.replace(" ", "\u00A0").replaceAll("[\\n\\t]", "");


                // 在设置单元格内容的代码段中，增加段落级控制
                CTP dataP = dataCell.addNewP();
                CTPPr pPr = dataP.addNewPPr();
                pPr.addNewWidowControl().setVal(STOnOff.FALSE);  // 关闭段落换行控制
                pPr.addNewJc().setVal(STJc.CENTER);             // 保持居中

                // 新增段落级禁止换行（关键）
                CTSpacing spacing = pPr.addNewSpacing();
                spacing.setLineRule(STLineSpacingRule.EXACT);
                spacing.setLine(BigInteger.valueOf(115));       // 固定行高（根据字体大小调整）

                CTR dataR = dataP.addNewR();
                dataR.addNewT().setStringValue(cellValue);

                // 设置字体样式
                CTRPr dataRPr = dataR.addNewRPr();
                dataRPr.addNewSz().setVal(BigInteger.valueOf(12));
                dataRPr.addNewColor().setVal("000000");
            }
        }

        // =============== 7. 最后一行新能源负荷预测行（日期行） ===============
        {
            CTRow ctHeaderRow1 = ctTbl.addNewTr();

            // 第一列：固定"日期"
            CTTc dateHeaderCell = ctHeaderRow1.addNewTc();
            CTP dateP = dateHeaderCell.addNewP();
            CTPPr pPr = dateP.addNewPPr();
            pPr.addNewJc().setVal(STJc.CENTER); // 居中

            CTTcPr dateHeaderCellPr = dateHeaderCell.addNewTcPr();
            // 新增：垂直居中
            dateHeaderCellPr.addNewVAlign().setVal(STVerticalJc.CENTER);
            dateHeaderCellPr.addNewNoWrap().setVal(STOnOff.TRUE);
            //背景颜色
            CTShd dateHeaderShd = dateHeaderCellPr.addNewShd();
            dateHeaderShd.setFill("D3D3D3");
            // 新增段落级禁止换行（关键）
            CTSpacing spacing = pPr.addNewSpacing();
            spacing.setLineRule(STLineSpacingRule.EXACT);
            spacing.setLine(BigInteger.valueOf(115));       // 固定行高（根据字体大小调整）
            CTR dateR = dateP.addNewR();
            dateR.addNewT().setStringValue("新能源负荷预测");
            dateR.addNewRPr().addNewSz().setVal(BigInteger.valueOf(12));
            // 动态生成日期列（每日期占2列）
            for (int i = 0; i < 7; i++) {
                // 创建合并单元格（占2列）
                CTTc dateCell = ctHeaderRow1.addNewTc();
                CTTcPr cellPr = dateCell.addNewTcPr();
                cellPr.addNewGridSpan().setVal(BigInteger.valueOf(2)); // 合并2列
                // 新增：垂直居中
                cellPr.addNewVAlign().setVal(STVerticalJc.CENTER);
                cellPr.addNewNoWrap().setVal(STOnOff.TRUE);
                //背景颜色
                CTShd dataShd = cellPr.addNewShd();
                dataShd.setFill("D3D3D3");
                // 设置日期内容
                CTP ctP = dateCell.addNewP();

                CTPPr dataPPr = ctP.addNewPPr();
                dataPPr.addNewJc().setVal(STJc.CENTER);
                CTR ctR = ctP.addNewR();
                ctR.addNewT().setStringValue(loadForecast.get(i));
                ctR.addNewRPr().addNewSz().setVal(BigInteger.valueOf(12)); // 小八号字


            }
        }
    }

    private void updateCharts(XWPFDocument document, Map<String, Object> dataMap) throws Exception {
        Map<String, List<Float>> chartData = (Map<String, List<Float>>) dataMap.get("chartData");
        if (chartData == null || chartData.isEmpty()) return;

        XWPFTableCell chartCell = findTableCellByPlaceholder(document, CHARTS_PLACEHOLDER);
        if (chartCell == null) {
            System.err.println("未找到图表占位符：" + CHARTS_PLACEHOLDER);
            return;
        }

        clearCellContent(chartCell);
        insertChartIntoCell(chartCell, chartData, 462, 180);
    }


    private void insertChartIntoCell(XWPFTableCell cell, Map<String, List<Float>> chartData, int width, int hight) throws Exception {
        // 1. 生成图表图片
        File chartImage = WordUtil.generatePriceChart(chartData);

        // 2. 插入图片到单元格
        try (FileInputStream fis = new FileInputStream(chartImage)) {
            cell.addParagraph().createRun().addPicture(
                    fis,
                    XWPFDocument.PICTURE_TYPE_PNG,
                    "chart.png",
                    Units.toEMU(width),  // 宽度（400像素）
                    Units.toEMU(hight)    // 高度（250像素）
            );
        }

        // 3. 删除临时图片
        chartImage.delete();
    }


    // 新增策略表格构建方法（修复列合并问题）
    private void buildStrategyTable(XWPFDocument document, Map<String, Object> strategyData1) {
        Map<String, Object> strategyData = (Map<String, Object>) strategyData1.get("strategyTable");
        XWPFTableCell cell = findTableCellByPlaceholder(document, STRATEGY_TABLE_PLACEHOLDER);
        if (cell == null) {
            System.err.println("未找到策略表格占位符：" + STRATEGY_TABLE_PLACEHOLDER);
            return;
        }
        clearCellContent(cell);

        // 获取数据
        String fullTitle = (String) strategyData.get("title");
        List<String> header1 = (List<String>) strategyData.get("header");
        List<String> header2 = (List<String>) strategyData.get("header2");
        List<List<String>> data = (List<List<String>>) strategyData.get("dataToInsert");

        // 创建表格结构
        CTTc ctTc = cell.getCTTc();
        CTTbl ctTbl = ctTc.addNewTbl();

        // =============== 1. 标题行（合并所有列） ===============
        {
            CTRow titleRow = ctTbl.addNewTr();
            CTTc titleCell = titleRow.addNewTc();
            CTTcPr titlePr = titleCell.addNewTcPr();
            titlePr.addNewGridSpan().setVal(BigInteger.valueOf(13)); // 合并13列

            CTP titleP = titleCell.addNewP();
            titleP.addNewPPr().addNewJc().setVal(STJc.CENTER);
            setTitleUnderline(titleP, fullTitle, fullTitle.split("日滚动交易策略")[0]); // 调用下划线方法
        }

        // =============== 2. 第一表头行（5列跨13列） ===============
        {
            CTRow headerRow1 = ctTbl.addNewTr();

            // 第一列：合并5列
            addMergedHeaderCell(headerRow1, header1.get(0), 1, "FFFFFF");

            // 后续四列各合并2列（5 + 2*4 = 13）
            for (int i = 1; i < header1.size(); i++) {
                addMergedHeaderCell(headerRow1, header1.get(i), 3, "FFFFFF");
            }
        }

        // =============== 3. 第二表头行（13个独立列） ===============
        {
            CTRow headerRow2 = ctTbl.addNewTr();
            for (String header : header2) {
                addHeaderCell(headerRow2, header, 1, "D3D3D3");
            }
        }

        // =============== 4. 列宽设置 ===============
        CTTblGrid grid = ctTbl.addNewTblGrid();
        // 项目名称列（宽3cm）
        grid.addNewGridCol().setW(BigInteger.valueOf(1800));
        // 时间点列（12列，每列1cm）
        for (int i = 0; i < 12; i++) {
            grid.addNewGridCol().setW(BigInteger.valueOf(600));
        }

        // =============== 5. 表格样式 ===============
        CTTblPr tblPr = ctTbl.addNewTblPr();
        // 固定布局
        CTTblLayoutType layout = tblPr.addNewTblLayout();
        layout.setType(STTblLayoutType.FIXED);
        // 边框
        CTTblBorders borders = tblPr.addNewTblBorders();
        setBorder(borders.addNewTop(), STBorder.SINGLE, 4, "000000");
        setBorder(borders.addNewLeft(), STBorder.SINGLE, 4, "000000");
        setBorder(borders.addNewBottom(), STBorder.SINGLE, 4, "000000");
        setBorder(borders.addNewRight(), STBorder.SINGLE, 4, "000000");
        setBorder(borders.addNewInsideH(), STBorder.SINGLE, 2, "000000");
        setBorder(borders.addNewInsideV(), STBorder.SINGLE, 2, "000000");

        // =============== 6. 填充数据 ===============
        for (int rowIndex = 0; rowIndex < data.size(); rowIndex++) {
            List<String> rowData = data.get(rowIndex);
            CTRow dataRow = ctTbl.addNewTr();

            // 交替行背景色
            String bgColor = (rowIndex % 2 == 0) ? "FFFFFF" : "D3D3D3";

            for (int colIndex = 0; colIndex < rowData.size(); colIndex++) {
                CTTc dataCell = dataRow.addNewTc();
                CTTcPr cellPr = dataCell.addNewTcPr();

                // 背景色
                CTShd shd = cellPr.addNewShd();
                shd.setFill(bgColor);
                shd.setVal(STShd.CLEAR);

                // 内容
                CTP p = dataCell.addNewP();
                p.addNewPPr().addNewJc().setVal(STJc.CENTER);
                CTR r = p.addNewR();
                r.addNewT().setStringValue(rowData.get(colIndex));
            }
        }
    }

    // 修改后的下划线设置方法
    private void setTitleUnderline(CTP paragraph, String fullTitle, String variable) {
        // 清空段落原有内容
        paragraph.getRList().clear();

        // 按变量拆分
        int variableStart = fullTitle.indexOf(variable);
        int variableEnd = variableStart + variable.length();

        // 变量部分（加粗+下划线）
        CTR variableRun = paragraph.addNewR();
        CTRPr variablePr = variableRun.addNewRPr();
        variablePr.addNewB().setVal(STOnOff.TRUE);
        variablePr.addNewU().setVal(STUnderline.SINGLE);
        variablePr.addNewSz().setVal(BigInteger.valueOf(22));
        variableRun.addNewT().setStringValue(variable); // 仅设置变量部分

        // 后缀部分（加粗无下划线）
        if (variableEnd < fullTitle.length()) {
            CTR suffixRun = paragraph.addNewR();
            CTRPr suffixPr = suffixRun.addNewRPr();
            suffixPr.addNewB().setVal(STOnOff.TRUE);
            suffixPr.addNewSz().setVal(BigInteger.valueOf(22));
            suffixRun.addNewT().setStringValue(fullTitle.substring(variableEnd));
        }
    }

    // 修改后的合并表头单元格方法
    private void addMergedHeaderCell(CTRow row, String text, int colspan, String bgColor) {
        CTTc cell = row.addNewTc();
        CTTcPr cellPr = cell.addNewTcPr();
        cellPr.addNewGridSpan().setVal(BigInteger.valueOf(colspan));

        // 样式设置
        CTShd shd = cellPr.addNewShd();
        shd.setFill(bgColor);
        cellPr.addNewVAlign().setVal(STVerticalJc.CENTER);

        CTP p = cell.addNewP();
        p.addNewPPr().addNewJc().setVal(STJc.CENTER);

        // 处理数值下划线
        String[] parts = text.split(" ", 2);
        if (parts.length == 2) {
            // 文字部分
            CTR textRun = p.addNewR();
            textRun.addNewT().setStringValue(parts[0] + " ");

            // 数值部分（带下划线）
            CTR numRun = p.addNewR();
            CTRPr numPr = numRun.addNewRPr();
            numPr.addNewU().setVal(STUnderline.SINGLE);
            numRun.addNewT().setStringValue(parts[1]);
        } else {
            // 无空格情况
            CTR normalRun = p.addNewR();
            normalRun.addNewT().setStringValue(text);
        }
    }

    // 添加普通表头单元格
    private void addHeaderCell(CTRow row, String text, int colspan, String bgColor) {
        CTTc cell = row.addNewTc();
        CTTcPr cellPr = cell.addNewTcPr();
        cellPr.addNewGridSpan().setVal(BigInteger.valueOf(colspan));

        CTShd shd = cellPr.addNewShd();
        shd.setFill(bgColor);
        cellPr.addNewVAlign().setVal(STVerticalJc.CENTER);

        CTP p = cell.addNewP();
        p.addNewPPr().addNewJc().setVal(STJc.CENTER);
        CTR r = p.addNewR();
        r.addNewRPr().addNewSz().setVal(BigInteger.valueOf(20));
        r.addNewT().setStringValue(text);
    }


    private BigDecimal parseNumber(String number) {
        return number.equals("--") ? BigDecimal.ZERO : new BigDecimal(number).setScale(4, RoundingMode.HALF_UP);
    }
}