package cn.iocoder.yudao.module.et.service.contractsmonth12;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.et.controller.admin.contractsmonth12.vo.ContractsMonth12PageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractsmonth12.vo.ContractsMonth12SaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractsmonth12.ContractsMonth12DO;
import cn.iocoder.yudao.module.et.dal.mysql.contractsmonth12.ContractsMonth12Mapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.et.enums.ErrorCodeConstants.CONTRACTS_MONTH12_NOT_EXISTS;

/**
 * 合同分月信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ContractsMonth12ServiceImpl implements ContractsMonth12Service {

    @Resource
    private ContractsMonth12Mapper contractsMonth12Mapper;

    @Override
    public Long createContractsMonth12(ContractsMonth12SaveReqVO createReqVO) {
        // 插入
        ContractsMonth12DO contractsMonth12 = BeanUtils.toBean(createReqVO, ContractsMonth12DO.class);
        contractsMonth12Mapper.insert(contractsMonth12);
        // 返回
        return contractsMonth12.getId();
    }

    @Override
    public void updateContractsMonth12(ContractsMonth12SaveReqVO updateReqVO) {
        // 校验存在
        validateContractsMonth12Exists(updateReqVO.getId());
        // 更新
        ContractsMonth12DO updateObj = BeanUtils.toBean(updateReqVO, ContractsMonth12DO.class);
        contractsMonth12Mapper.updateById(updateObj);
    }

    @Override
    public void deleteContractsMonth12(Long id) {
        // 校验存在
        validateContractsMonth12Exists(id);
        // 删除
        contractsMonth12Mapper.deleteById(id);
    }

    private void validateContractsMonth12Exists(Long id) {
        if (contractsMonth12Mapper.selectById(id) == null) {
            throw exception(CONTRACTS_MONTH12_NOT_EXISTS);
        }
    }

    @Override
    public ContractsMonth12DO getContractsMonth12(Long id) {
        return contractsMonth12Mapper.selectById(id);
    }

    @Override
    public PageResult<ContractsMonth12DO> getContractsMonth12Page(ContractsMonth12PageReqVO pageReqVO) {
        return contractsMonth12Mapper.selectPage(pageReqVO);
    }

    @Override
    public List<ContractsMonth12DO> getContractsMonth12sByContractsId(String id) {
        return contractsMonth12Mapper.selectListByContractsId(id);
    }

}