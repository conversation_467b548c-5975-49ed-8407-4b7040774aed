package cn.iocoder.yudao.module.et.dal.dataobject.contractSummary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 中长期合同表（月度量价）
 **/
@TableName("jy_contract_monthly")
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ContractMonthlyDO {
    /**
     * 所属年月
     */
    @TableId(type = IdType.NONE)
    private String bizMonth;
    /**
     * 所属合同序列
     */
    private String sequenceName;
    /**
     * 合同名称
     */
    private String contractName;
    /**
     * 电量（MWh）
     */
    private BigDecimal quantity;
    /**
     * 电价（元/MWh）
     */
    private BigDecimal price;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 调度单元编码（场站编码）
     */
    private String dispatchNodeCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherTime;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
}
