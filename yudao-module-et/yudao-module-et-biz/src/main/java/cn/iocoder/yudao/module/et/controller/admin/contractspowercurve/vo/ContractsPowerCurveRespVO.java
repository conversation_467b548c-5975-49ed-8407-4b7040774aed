package cn.iocoder.yudao.module.et.controller.admin.contractspowercurve.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 电力曲线 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContractsPowerCurveRespVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19534")
    @ExcelProperty("自增主键")
    private Long id;

    @Schema(description = "曲线类型", example = "1")
    @ExcelProperty("曲线类型")
    private String curveType;

    @Schema(description = "主体信息", example = "芋艿")
    @ExcelProperty("主体信息")
    private String memberName;

    @Schema(description = "开始日期")
    @ExcelProperty("开始日期")
    private LocalDateTime beginTime;

    @Schema(description = "结束日期")
    @ExcelProperty("结束日期")
    private LocalDateTime endTime;

    @Schema(description = "运行日期")
    @ExcelProperty("运行日期")
    private LocalDateTime runDate;

    @Schema(description = "日电量")
    @ExcelProperty("日电量")
    private BigDecimal quantity;

    @Schema(description = "曲线点数")
    @ExcelProperty("曲线点数")
    private String points;

    @Schema(description = "合同id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31435")
    @ExcelProperty("合同id")
    private String contractsId;

    @Schema(description = "部门ID", example = "15821")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    @ExcelProperty("上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    @ExcelProperty("数据爬取时间")
    private LocalDateTime gatherDate;

    @Schema(description = "修改时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("修改时间")
    private LocalDateTime modificationTime;

}