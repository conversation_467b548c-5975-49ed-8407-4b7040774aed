package cn.iocoder.yudao.module.et.job;

import cn.iocoder.yudao.module.et.util.TradingUnitStatMComplementsService;
import cn.iocoder.yudao.module.et.util.TradingUnitStatMService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@EnableScheduling
@Component
@Slf4j
public class TradingUnitStatMJob {
    @Resource
    private TradingUnitStatMService tradingUnitStatMService;
    @Resource
    private TradingUnitStatMComplementsService tradingUnitStatMComplementsService;

    /**
     * 每天3点统计交易单元数据
     */
    //@Scheduled(fixedDelay = 1000000)
    @Scheduled(cron = "0 45 3 * * ?")
    public void stat() {
        log.info("交易单元数据月统计,开始");
        tradingUnitStatMService.tradingUnitStatM();
        //补数方法
        //tradingUnitStatMComplementsService.tradingUnitStatM();
        log.info("交易单元数据月统计,结束");
    }

    // 1.mysql表结构改成竖表 字段是 id 统计类型 统计周期 数据聚合模式
    // 2.根据mysql里的统计类型在统计表里生成字段 short_term_forecast。统计周期在校验表里生成字段 short_term_forecast_days
    // 3.查数据 根据统计周期表里的最大统计周期查数据,根据各个字段的统计洲周期筛选
    // 4.插入数据的时候两个表都插入 统计表插入统计数据。统计周期表插入数据的周期(天数)
}
