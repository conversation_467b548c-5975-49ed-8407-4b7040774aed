package cn.iocoder.yudao.module.et.dal.dataobject.contractstype;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 合同类型 DO
 *
 * <AUTHOR>
 */
@TableName("jy_contracts_type")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractsTypeDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId
    private Long id;
    /**
     * 合同类型名称
     */
    private String name;
    /**
     * 合同类型代码
     */
    private String code;
    /**
     * 父节点id
     */
    private Integer parentId;

}