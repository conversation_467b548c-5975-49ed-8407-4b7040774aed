package cn.iocoder.yudao.module.et.dal.mysql.rollingMatchingTransactions;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.et.dal.dataobject.periodicSettlement.PeriodicSettlementDetailDO;
import cn.iocoder.yudao.module.et.dal.dataobject.rollingMatchingTransactions.JyMarketTradingInfoDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * PeriodicSettlementDetailMapper
 **/
@DS("shardingsphereDB")
@Mapper
public interface JyMarketTradingInfoMapper extends BaseMapper<JyMarketTradingInfoDO> {
}
