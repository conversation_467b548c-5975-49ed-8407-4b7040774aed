package cn.iocoder.yudao.module.et.dal.mysql.spotTradingFiling;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.spiderconf.vo.SpiderConfPageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.spiderConf.SpiderConfDO;
import cn.iocoder.yudao.module.et.dal.dataobject.spotTradingFiling.SpotTradingFilingDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 爬取页面解析类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
@DS("shardingsphereDB")
public interface SpotTradingFilingMapper extends BaseMapperX<SpotTradingFilingDO> {




    @TenantIgnore
    List<SpotTradingFilingDO> findAllByUse();


}
