package cn.iocoder.yudao.module.et.service.powerstation;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.et.controller.admin.powerstation.vo.PowerStationPageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.powerstation.vo.PowerStationSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.DispatchNodeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.MysqlTradingUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import cn.iocoder.yudao.module.et.dal.mysql.dispatchnode.DispatchNodeMapper;
import cn.iocoder.yudao.module.et.dal.mysql.powerstation.PowerStationMapper;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.et.enums.ErrorCodeConstants.POWER_STATION_NOT_EXISTS;

/**
 * 场站信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PowerStationServiceImpl implements PowerStationService {

    @Resource
    private PowerStationMapper powerStationMapper;
    @Resource
    private DispatchNodeMapper dispatchNodeMapper;
    @Resource
    private DeptApi deptApi;

    @Override
    public Integer createPowerStation(PowerStationSaveReqVO createReqVO) {
        // 插入
        PowerStationDO powerStation = BeanUtils.toBean(createReqVO, PowerStationDO.class);
        powerStationMapper.insert(powerStation);
        // 返回
        return powerStation.getId();
    }

    @Override
    public void updatePowerStation(PowerStationSaveReqVO updateReqVO) {
        // 校验存在
        validatePowerStationExists(updateReqVO.getId());
        // 更新
        PowerStationDO updateObj = BeanUtils.toBean(updateReqVO, PowerStationDO.class);
        powerStationMapper.updateById(updateObj);
    }

    @Override
    public void deletePowerStation(Integer id) {
        // 校验存在
        validatePowerStationExists(id);
        // 删除
        powerStationMapper.deleteById(id);
    }

    private void validatePowerStationExists(Integer id) {
        if (powerStationMapper.selectById(id) == null) {
            throw exception(POWER_STATION_NOT_EXISTS);
        }
    }

    @Override
    public PowerStationDO getPowerStation(Integer id) {
        return powerStationMapper.selectById(id);
    }

    @Override
    public PageResult<PowerStationDO> getPowerStationPage(PowerStationPageReqVO pageReqVO) {
        return powerStationMapper.selectPage(pageReqVO);
    }

    public String getCurrentGroupOrgCode() {
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        DeptRespDTO deptRespDTO = deptApi.getDept(deptId);
        if (deptRespDTO == null) {
            deptRespDTO = deptApi.getDept(deptId);
        }
        return deptRespDTO.getOrgCode();
    }

    /**
     * 根据当前登陆的租户获取所属的所有交易单元id
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getTradingUnit() {
        String companyId = this.getCurrentGroupOrgCode();
        LambdaQueryWrapper<DispatchNodeDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DispatchNodeDO::getOrgCode, companyId);
        List<DispatchNodeDO> dispatchNodeDOS = dispatchNodeMapper.selectList(queryWrapper);
        List<Map<String, Object>> listMap = new ArrayList<>();
        for (DispatchNodeDO dispatchNodeDO : dispatchNodeDOS) {
            Map<String, Object> map = new HashMap<>();
            map.put("value", dispatchNodeDO.getCode());
            map.put("label", dispatchNodeDO.getName());
            listMap.add(map);
        }
        return listMap;
    }

    @Override
    public List<PowerStationDO> selectAll() {
        return powerStationMapper.selectList(null);
    }

    @Override
    public List<PowerStationDO> getPowerStationByOrgCode(String orgCode) {
        LambdaQueryWrapper<PowerStationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PowerStationDO::getOrgCode, orgCode);
        return powerStationMapper.selectList(null);
    }


    /**
     * 根据当前登录的用户判断是分公司还是区域公司 获取下属的交易单元id
     * 分公司/区域公司 获取 所属的交易单元id/分公司及分公司下属的交易单元id
     * <p>
     * 市场动态菜单用
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getGroup() {
        List<Map<String, Object>> listMap = new ArrayList<>();
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        DeptRespDTO deptRespDTO = deptApi.getDept(deptId);
        if (deptRespDTO == null) {
            deptRespDTO = deptApi.getDept(deptId);
        }
        List<DispatchNodeDO> dispatchNodeDOS = dispatchNodeMapper.selectList();
        // 递归构建部门树结构并添加到列表
        listMap.add(buildDeptTree(deptRespDTO, dispatchNodeDOS));
        return listMap;
    }

    /**
     * 根据当前登录的用户判断是分公司还是区域公司 获取下属的交易单元id
     * 分公司/区域公司 获取 所属的交易单元id/分公司及分公司下属的交易单元id
     * <p>
     * 市场动态菜单用
     * <p>
     * mysql tradingunit表
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getGroupTradingUnit() {
        List<Map<String, Object>> listMap = new ArrayList<>();
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        DeptRespDTO deptRespDTO = deptApi.getDept(deptId);
        if (deptRespDTO == null) {
            deptRespDTO = deptApi.getDept(deptId);
        }
        List<MysqlTradingUnitDO> dispatchNodeDOS = dispatchNodeMapper.selectAllTradingUint();
        // 递归构建部门树结构并添加到列表
        listMap.add(buildDeptTreeMysqlTradingUnit(deptRespDTO, dispatchNodeDOS));
        return listMap;
    }

    /**
     * 根据当前登录的用户判断是分公司还是区域公司 获取下属的交易单元id
     * 分公司/区域公司 获取 所属的交易单元id/分公司及分公司下属的交易单元id
     * <p>
     * 市场动态菜单用
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getGroupOffCompany() {
        List<Map<String, Object>> listMap = new ArrayList<>();
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        DeptRespDTO deptRespDTO = deptApi.getDept(deptId);
        if (deptRespDTO == null) {
            deptRespDTO = deptApi.getDept(deptId);
        }
        List<DispatchNodeDO> dispatchNodeDOS = dispatchNodeMapper.selectList();
        // 递归构建部门树结构并添加到列表
        listMap.add(buildCompanyTree(deptRespDTO, dispatchNodeDOS));
        return listMap;
    }

    /**
     * 通过传入的交易单元code获取场站信息
     *
     * @param code 交易单元code
     * @return
     */
    @Override
    public PowerStationDO getPowerStationByCode(String code) {
        LambdaQueryWrapper<PowerStationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PowerStationDO::getCode, code);
        PowerStationDO powerStationDO = powerStationMapper.selectOne(queryWrapper);
        return powerStationDO;
    }

    private Map<String, Object> buildDeptTree(DeptRespDTO dept, List<DispatchNodeDO> dispatchNodeDOS) {
        Map<String, Object> map = new HashMap<>();
        map.put("value", dept.getOrgCode() == null ? dept.getName() : dept.getOrgCode());
        map.put("label", dept.getName());
        // 判断是否是场站
        if (dept.getPowerStation() != null && dept.getPowerStation() == 1) {
            List<DispatchNodeDO> collect = dispatchNodeDOS.stream().filter(dispatchNodeDO -> Objects.equals(dispatchNodeDO.getOrgCode(), dept.getOrgCode())).toList();
            List<Map<String, Object>> list = new ArrayList<>();
            for (DispatchNodeDO dispatchNodeDO : collect) {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("value", dispatchNodeDO.getCode());
                map1.put("label", dispatchNodeDO.getName());
                list.add(map1);
            }
            // 递归获取子部门并构建子部门的Map结构
            List<DeptRespDTO> childDeptList = deptApi.getByParentId(dept.getId());
            for (DeptRespDTO childDept : childDeptList) {
                Map<String, Object> childMap = buildDeptTree(childDept, dispatchNodeDOS);
                list.add(childMap);
            }
            map.put("children", list);
        } else {
            // 递归获取子部门并构建子部门的Map结构
            List<DeptRespDTO> childDeptList = deptApi.getByParentId(dept.getId());
            List<Map<String, Object>> childrenMaps = new ArrayList<>();
            for (DeptRespDTO childDept : childDeptList) {
                Map<String, Object> childMap = buildDeptTree(childDept, dispatchNodeDOS);
                childrenMaps.add(childMap);
            }
            map.put("children", childrenMaps);
        }

        return map;
    }

    /**
     * mysql tradingunit表
     *
     * @param dept
     * @param dispatchNodeDOS
     * @return
     */
    private Map<String, Object> buildDeptTreeMysqlTradingUnit(DeptRespDTO dept, List<MysqlTradingUnitDO> dispatchNodeDOS) {
        Map<String, Object> map = new HashMap<>();
        map.put("value", dept.getOrgCode() == null ? dept.getName() : dept.getOrgCode());
        map.put("label", dept.getName());
        // 判断是否是场站
        if (dept.getPowerStation() != null && dept.getPowerStation() == 1) {
            List<MysqlTradingUnitDO> collect = dispatchNodeDOS.stream().filter(dispatchNodeDO -> Objects.equals(dispatchNodeDO.getOrgCode(), dept.getOrgCode())).toList();
            List<Map<String, Object>> list = new ArrayList<>();
            for (MysqlTradingUnitDO dispatchNodeDO : collect) {
                Map<String, Object> map1 = new HashMap<>();
                map1.put("value", dispatchNodeDO.getCode());
                map1.put("label", dispatchNodeDO.getName());
                list.add(map1);
            }
            // 递归获取子部门并构建子部门的Map结构
            List<DeptRespDTO> childDeptList = deptApi.getByParentId(dept.getId());
            for (DeptRespDTO childDept : childDeptList) {
                Map<String, Object> childMap = buildDeptTreeMysqlTradingUnit(childDept, dispatchNodeDOS);
                list.add(childMap);
            }
            map.put("children", list);
        } else {
            // 递归获取子部门并构建子部门的Map结构
            List<DeptRespDTO> childDeptList = deptApi.getByParentId(dept.getId());
            List<Map<String, Object>> childrenMaps = new ArrayList<>();
            for (DeptRespDTO childDept : childDeptList) {
                Map<String, Object> childMap = buildDeptTreeMysqlTradingUnit(childDept, dispatchNodeDOS);
                childrenMaps.add(childMap);
            }
            map.put("children", childrenMaps);
        }

        return map;
    }

    /**
     * 递归到公司
     *
     * @param dept
     * @param dispatchNodeDOS
     * @return
     */
    private Map<String, Object> buildCompanyTree(DeptRespDTO dept, List<DispatchNodeDO> dispatchNodeDOS) {
        Map<String, Object> map = new HashMap<>();
        map.put("value", dept.getOrgCode() == null ? dept.getName() : dept.getOrgCode());
        map.put("label", dept.getName());
        // 判断是否是场站
        if (dept.getPowerStation() != null && dept.getPowerStation() == 1) {
            List<Map<String, Object>> list = new ArrayList<>();
            // 递归获取子部门并构建子部门的Map结构
            List<DeptRespDTO> childDeptList = deptApi.getByParentId(dept.getId());
            for (DeptRespDTO childDept : childDeptList) {
                Map<String, Object> childMap = buildCompanyTree(childDept, dispatchNodeDOS);
                list.add(childMap);
            }
            map.put("children", list);
        } else {
            // 递归获取子部门并构建子部门的Map结构
            List<DeptRespDTO> childDeptList = deptApi.getByParentId(dept.getId());
            List<Map<String, Object>> childrenMaps = new ArrayList<>();
            for (DeptRespDTO childDept : childDeptList) {
                Map<String, Object> childMap = buildCompanyTree(childDept, dispatchNodeDOS);
                childrenMaps.add(childMap);
            }
            map.put("children", childrenMaps);
        }

        return map;
    }


}