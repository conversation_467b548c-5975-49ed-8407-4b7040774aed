package cn.iocoder.yudao.module.et.dal.mysql.tradingCalendar;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingCalendar.JyTradingAnnouncementDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DS("shardingsphereDB")
@Mapper
public interface TradingCalendarMapper extends BaseMapperX<JyTradingAnnouncementDO> {

    @TenantIgnore
    @Select("SELECT * from jy_trading_announcement  where (date_begin like #{monthStr} " +
            "or date_end like #{monthStr}) and status like #{statusStr}")
    List<JyTradingAnnouncementDO> selectListByDateAndStr(@Param("monthStr") String monthStr, @Param("statusStr") String statusStr);
}
