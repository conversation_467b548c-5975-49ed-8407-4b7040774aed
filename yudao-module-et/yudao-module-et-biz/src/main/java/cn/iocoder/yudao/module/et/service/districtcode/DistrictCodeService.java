package cn.iocoder.yudao.module.et.service.districtcode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.et.controller.admin.districtcode.vo.DistrictCodePageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.districtcode.vo.DistrictCodeSaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.districtcode.DistrictCodeDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 行政区划 Service 接口
 *
 * <AUTHOR>
 */
public interface DistrictCodeService {

    /**
     * 创建行政区划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createDistrictCode(@Valid DistrictCodeSaveReqVO createReqVO);

    /**
     * 更新行政区划
     *
     * @param updateReqVO 更新信息
     */
    void updateDistrictCode(@Valid DistrictCodeSaveReqVO updateReqVO);

    /**
     * 删除行政区划
     *
     * @param id 编号
     */
    void deleteDistrictCode(Integer id);

    /**
     * 获得行政区划
     *
     * @param id 编号
     * @return 行政区划
     */
    DistrictCodeDO getDistrictCode(Integer id);

    /**
     * 获得行政区划分页
     *
     * @param pageReqVO 分页查询
     * @return 行政区划分页
     */
    PageResult<DistrictCodeDO> getDistrictCodePage(DistrictCodePageReqVO pageReqVO);

    List<DistrictCodeDO> getDistrictCodeTree(String number);

    List<DistrictCodeDO> getDistrictCodeByPid(Integer pid);

}