package cn.iocoder.yudao.module.et.dal.dataobject.contractstime24;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合同分时段信息 DO
 *
 * <AUTHOR>
 */
@TableName("jy_contracts_time24")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractsTime24DO {

    /**
     * 自增主键
     */
    @TableId
    private Long id;
    /**
     * 时段编号
     */
    private String timeSlotCoding;
    /**
     * 时段名称
     */
    private String timeSlotName;
    /**
     * 购方电量
     */
    private BigDecimal purchaserQuantity;
    /**
     * 售方电量
     */
    private BigDecimal sellerQuantity;
    /**
     * 购方电价
     */
    private BigDecimal purchaserPrice;
    /**
     * 售方电价
     */
    private BigDecimal sellerPrice;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private String orgCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherDate;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
    /**
     * 时段区间
     */
    private String timeSlotsRange;
    /**
     * 时间段
     */
    private String timeSlots;
    /**
     * 时间段开始日期
     */
    private LocalDateTime beginTime;
    /**
     * 时间段结束日期
     */
    private LocalDateTime endTime;
    /**
     * 合同id
     */
    private String contractsId;

    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;
}