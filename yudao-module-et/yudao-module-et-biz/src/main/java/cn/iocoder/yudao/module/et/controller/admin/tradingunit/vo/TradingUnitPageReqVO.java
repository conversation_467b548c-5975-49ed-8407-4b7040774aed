package cn.iocoder.yudao.module.et.controller.admin.tradingunit.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.net.ntp.TimeStamp;

@Schema(description = "管理后台 - 交易单元分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TradingUnitPageReqVO extends PageParam {
    /**
     * 时间
     */
    private TimeStamp ts;

    /**
     * 场站id
     */
    private String unitId;
    
    /**
     * 电厂侧日前市场化结算价格限制
     */
    private float hisSettlementPricesLimit;

    /**
     * 电厂侧实时结算价格
     */
    private float realSettlementPrices;

    /**
     * 中长期结算曲线
     */
    private float mediumLongTermSettlementCurves;

    /**
     * 自计划
     */
    private float selfPlan;

    /**
     * 预平衡计划
     */
    private float prebalancePlan;

    /**
     * 可靠性计划
     */
    private float reliabilityPlan;

    /**
     * 双边出清
     */
    private float bilateralClearance;

    /**
     * 实时计划
     */
    private float realPlan;

    /**
     * 实时上网scada电力
     */
    private float realScadaPower;

    /**
     * 短期预测
     */
    private float shortTermForecast;

    /**
     * 超短期预测
     */
    private float ultraShortTermForecast;

    /**
     * 跨省区现货出清电力
     */
    private float crossProvincialSpotClearingOfElectricity;

    /**
     * 实时出清依据
     */
    private float realBasisClearance;

    /**
     * 日前正现货电量(MW.h)
     */
    private float hisPositivePower;

    /**
     * 日前负现货电量(MW.h)
     */
    private float hisNegativePower;

    /**
     * 日前正现货费用(元)
     */
    private float hisPositivePrices;

    /**
     * 日前负现货费用(元)
     */
    private float hisNegativePrices;

    /**
     * 日前发电计划(MW)
     */
    private float hisGenerateElectricityPlan;

    /**
     * 中长期计划（上网）(MW)
     */
    private float mediumLongTermPlan;

    /**
     * 日前省间出清(MW)
     */
    private float hisProvinceClearance;

    /**
     * 日前市场结算价格(元/MW.h)
     */
    private float hisSettlementPrices;

    /**
     * 实时正现货电量(MW.h)
     */
    private float realPositivePower;

    /**
     * 实时负现货电量(MW.h)
     */
    private float realNegativePower;

    /**
     * 实时正现货费用(元)
     */
    private float realPositivePrices;

    /**
     * 实时负现货费用(元)
     */
    private float realNegativePrices;

    /**
     * 实时省间出清(MW)
     */
    private float realProvinceClearance;

    /**
     * 跨省调峰计划(MW)
     */
    private float crossProvincialPeakLoadBalancingPlan;

    /**
     * 跨省调峰返还(MW)
     */
    private float crossProvincialPeakLoadBalancingReturn;

    /**
     * 实际结算电量(MW.h)
     */
    private float realSettlementPower;

    /**
     * 紧急调用开机补偿费用(元)
     */
    private float emergencyInvokeStartUpCompensationPrices;

    /**
     * 火电开机补偿费用(元)
     */
    private float thermalPowerStartUpCompensationPrices;

    /**
     * 偏差考核费用(元)
     */
    private float deviationReviewPrices;

    /**
     * 新能源增发超额获利回收费用
     */
    private float recoveryOfExcessProfitsFromNewEnergyIssuancePrices;

    /**
     * 日前备用出清(MW)
     */
    private float hisStandbyClearance;

    /**
     * 实时备用出清(MW)
     */
    private float realStandbyClearance;

    /**
     * 中长期阻塞费用(元)
     */
    private float mediumLongTermBlockagePrices;

    /**
     * 阻塞风险对冲费用(元)
     */
    private float blockingRiskHedgingPrices;

    /**
     * 修正补偿费用(元)
     */
    private float modifiedCompensationPrices;

    /**
     * 必开机组补偿费用(元)
     */
    private float necessaryStartUpCompensationPrices;

    /**
     * 叠加后中长期(MW.h)
     */
    private float mediumLongTermFusion;

    /**
     * 日前免结算电量(MW.h)
     */
    private float hisSettlementFreePower;

    /**
     * 实时免结算电量(MW.h)
     */
    private float realSettlementFreePower;

    /**
     * 酒钢开口合同(MW)
     */
    private float jiscoBpo;

    /**
     * 酒钢双边交易(MW)
     */
    private float jiscoBilateralTransactions;

    /**
     * 考核电量(MW.h)
     */
    private float reviewPower;

    /**
     * 紧急调用开机上网电量(MW.h)
     */
    private float fmergencyInvokeStartUpOnGridPower;

    /**
     * 调频增发能量补偿(元)
     */
    private float adjustmentFrequencyAdditionEnergyCompensation;

    /**
     * 调频减发能量补偿(元)
     */
    private float adjustmentFrequencyLessenEnergyCompensation;

    /**
     * 非计划停运考核(元)
     */
    private float unplannedOutageAssessment;

    /**
     * 是否允许替换
     */
    private float whetherSubstitutionIsAllowed;

    /**
     * 偏差率
     */
    private float deviationRate;

    /**
     * TMR电量
     */
    private float tmrPower;

    /**
     * SCADA电量
     */
    private float scadaPower;

    /**
     * 新能源实时消纳电量
     */
    private float realUsePower;

    /**
     * 校正电量
     */
    private float calibrationPower;

    /**
     * 结算电量
     */
    private float settlementPower;
}