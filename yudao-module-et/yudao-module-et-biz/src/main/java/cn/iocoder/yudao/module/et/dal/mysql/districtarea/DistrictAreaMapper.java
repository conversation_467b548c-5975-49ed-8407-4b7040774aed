package cn.iocoder.yudao.module.et.dal.mysql.districtarea;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.et.controller.admin.districtarea.vo.DistrictAreaPageReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.districtarea.DistrictAreaDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 省份对应算法类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DistrictAreaMapper extends BaseMapperX<DistrictAreaDO> {
    @TenantIgnore
    default PageResult<DistrictAreaDO> selectPage(DistrictAreaPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DistrictAreaDO>()
                .eqIfPresent(DistrictAreaDO::getCode, reqVO.getCode())
                .likeIfPresent(DistrictAreaDO::getName, reqVO.getName())
                .likeIfPresent(DistrictAreaDO::getFullName, reqVO.getFullName())
                .eqIfPresent(DistrictAreaDO::getDistrictCode, reqVO.getDistrictCode())
                .eqIfPresent(DistrictAreaDO::getPredictionModel, reqVO.getPredictionModel())
                .orderByDesc(DistrictAreaDO::getCode));
    }

    @TenantIgnore
    default DistrictAreaDO selectByCode(String code) {
        return selectOne(new LambdaQueryWrapperX<DistrictAreaDO>().likeIfPresent(DistrictAreaDO::getCode, code));
    }
}