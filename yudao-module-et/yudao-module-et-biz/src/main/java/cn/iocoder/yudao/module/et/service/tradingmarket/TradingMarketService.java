package cn.iocoder.yudao.module.et.service.tradingmarket;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.et.controller.admin.tradingmarket.vo.*;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingmarket.TradingMarketDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 交易 Service 接口
 *
 * <AUTHOR>
 */
public interface TradingMarketService {

    /**
     * 创建交易
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTradingMarket(@Valid TradingMarketSaveReqVO createReqVO);

    /**
     * 更新交易
     *
     * @param updateReqVO 更新信息
     */
    void updateTradingMarket(@Valid TradingMarketSaveReqVO updateReqVO);

    /**
     * 删除交易
     *
     * @param id 编号
     */
    void deleteTradingMarket(Long id);

    /**
     * 获得交易
     *
     * @param id 编号
     * @return 交易
     */
    TradingMarketDO getTradingMarket(Long id);

    List<TradingMarketDO> getAll();

    /**
     * 获得交易分页
     *
     * @param pageReqVO 分页查询
     * @return 交易分页
     */
    PageResult<TradingMarketDO> getTradingMarketPage(TradingMarketPageReqVO pageReqVO);




}