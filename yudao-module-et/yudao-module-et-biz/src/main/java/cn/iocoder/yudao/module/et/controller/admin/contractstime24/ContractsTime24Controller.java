package cn.iocoder.yudao.module.et.controller.admin.contractstime24;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.et.controller.admin.contractstime24.vo.ContractsTime24PageReqVO;
import cn.iocoder.yudao.module.et.controller.admin.contractstime24.vo.ContractsTime24RespVO;
import cn.iocoder.yudao.module.et.controller.admin.contractstime24.vo.ContractsTime24SaveReqVO;
import cn.iocoder.yudao.module.et.dal.dataobject.contractstime24.ContractsTime24DO;
import cn.iocoder.yudao.module.et.service.contractstime24.ContractsTime24Service;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合同分时段信息")
@RestController
@RequestMapping("/jy/contracts-time24")
@Validated
public class ContractsTime24Controller {

    @Resource
    private ContractsTime24Service contractsTime24Service;

    @PostMapping("/create")
    @Operation(summary = "创建合同分时段信息")
    @PreAuthorize("@ss.hasPermission('jy:contracts-time24:create')")
    public CommonResult<Long> createContractsTime24(@Valid @RequestBody ContractsTime24SaveReqVO createReqVO) {
        return success(contractsTime24Service.createContractsTime24(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新合同分时段信息")
    @PreAuthorize("@ss.hasPermission('jy:contracts-time24:update')")
    public CommonResult<Boolean> updateContractsTime24(@Valid @RequestBody ContractsTime24SaveReqVO updateReqVO) {
        contractsTime24Service.updateContractsTime24(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除合同分时段信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('jy:contracts-time24:delete')")
    public CommonResult<Boolean> deleteContractsTime24(@RequestParam("id") Long id) {
        contractsTime24Service.deleteContractsTime24(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得合同分时段信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('jy:contracts-time24:query')")
    public CommonResult<ContractsTime24RespVO> getContractsTime24(@RequestParam("id") Long id) {
        ContractsTime24DO contractsTime24 = contractsTime24Service.getContractsTime24(id);
        return success(BeanUtils.toBean(contractsTime24, ContractsTime24RespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得合同分时段信息分页")
    @PreAuthorize("@ss.hasPermission('jy:contracts-time24:query')")
    public CommonResult<PageResult<ContractsTime24RespVO>> getContractsTime24Page(@Valid ContractsTime24PageReqVO pageReqVO) {
        PageResult<ContractsTime24DO> pageResult = contractsTime24Service.getContractsTime24Page(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContractsTime24RespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出合同分时段信息 Excel")
    @PreAuthorize("@ss.hasPermission('jy:contracts-time24:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContractsTime24Excel(@Valid ContractsTime24PageReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ContractsTime24DO> list = contractsTime24Service.getContractsTime24Page(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "合同分时段信息.xls", "数据", ContractsTime24RespVO.class,
                BeanUtils.toBean(list, ContractsTime24RespVO.class));
    }

}