package cn.iocoder.yudao.module.et.dal.dataobject.generatePowerCurveAnalysis;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.sql.Timestamp;

/**
 * 交易单元 DO
 *
 * <AUTHOR>
 */
@TableName("trading_unit")
@KeySequence("trading_unit_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GeneratePowerCurveAnalysisDO {

    /**
     * 时间
     */
    private Timestamp ts;

    /**
     * 场站id
     */
    private String unitId;
    /**
     * 电厂侧日前市场化结算价格限制
     */
    private Float realScadaPower;
    /**
     * 短期预测
     */
    private Float shortTermForecast;

}