package cn.iocoder.yudao.module.et.controller.admin.priceforecast.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.net.ntp.TimeStamp;

@Schema(description = "管理后台 - 市场预测 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PriceForecastRespVO {

    /**
     * 时间
     */
    private TimeStamp ts;
    /**
     * 公司及单元id
     */
    private String[] ids;

    /**
     * 时间
     */
    private Long[] date;
    /**
     * 相似日时间
     */
    private Long[] similarDate;
}