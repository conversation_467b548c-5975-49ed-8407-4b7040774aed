package cn.iocoder.yudao.module.et.dal.dataobject.tradingUnitOrg;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 交易单元组织机构 DO
 *
 * <AUTHOR>
 */
@Data
@TableName("jy_trading_unit")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradingUnitOrgDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId
    private Long id;
    /**
     * 交易单元编码
     */
    private String code;
    private String officialName;
    /**
     * 交易单元名称
     */
    private String name;
    /**
     * 交易单元全称
     */
    private String fullName;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 交易单元装机容量
     */
    private Float capacity;
    /**
     * 交易单元设备台数
     */
    private Integer quantity;
    /**
     * 电力交易系统-省内账号
     */
    private String sysUser;
    /**
     * 电力交易系统-省内密码
     */
    private String sysPwd;
    /**
     * 电力交易系统-省间账号
     */
    private String sysUserBj;
    /**
     * 电力交易系统-省间密码
     */
    private String sysPwdBj;
    /**
     * ukey序列号
     */
    private String ukeySeq;
    private String orgCode;
    /**
     * 是否禁用
     */
    private Boolean enabled;
    /**
     * 租户编号
     */
    private Boolean tenantId;
    /**
     * 部门ID
     */
    private Boolean dept_id;
    /**
     * 上级机构唯一编码（dept扩展字段）
     */
    private Boolean org_code;
}
