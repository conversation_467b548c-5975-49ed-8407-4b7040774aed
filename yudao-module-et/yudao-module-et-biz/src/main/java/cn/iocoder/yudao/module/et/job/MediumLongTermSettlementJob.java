package cn.iocoder.yudao.module.et.job;

import cn.iocoder.yudao.framework.common.util.string.StrUtils;
import cn.iocoder.yudao.module.et.controller.admin.contracts.vo.ConstructsMongoVO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.MysqlTradingUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import cn.iocoder.yudao.module.et.dal.mysql.contracts.ContractsMapper;
import cn.iocoder.yudao.module.et.dal.mysql.powerstation.PowerStationMapper;
import cn.iocoder.yudao.module.et.service.powerstation.PowerStationService;
import cn.iocoder.yudao.module.et.service.tradingunit.TradingUnitService;
import cn.iocoder.yudao.module.et.util.TradingUnitStatMComplementsService;
import cn.iocoder.yudao.module.et.util.TradingUnitStatMService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@EnableScheduling
@Component
@Slf4j
/**
 * 中长期持有量任务
 */
public class MediumLongTermSettlementJob {


    @Resource
    private ContractsMapper contractsMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Autowired
    private PowerStationService powerStationService;


    @Resource
    private TradingUnitService tradingUnitService;

    /**
     * 每天3点统计        trading_unit_${unitId}
     */
//    @Scheduled(cron = "0 45 3 * * ?")
    @Scheduled(cron = "*/10 * * * * ?")
    public void mediumLongTermSettlement() {
        log.info("------------中长期持有量结算------------");
        List<Map> maps = contractsMapper.selectContractsIdsAndRowNos();
        //场站
        List<PowerStationDO> powerStationDOS = powerStationService.selectAllByTenantIgnore();
        //交易单元
        List<MysqlTradingUnitDO> mysqlTradingUnitDOS = powerStationService.selectAllTradingUnitByTenantIgnore();

        for (Map map : maps) {
            String orgCode = String.valueOf(map.get("orgCode"));
            String ids = String.valueOf(map.get("ids"));
            String rowNos = String.valueOf(map.get("rowNos"));
            List<MysqlTradingUnitDO> list = mysqlTradingUnitDOS
                    .stream()
                    .filter(mysqlTradingUnitDO -> orgCode.equals(mysqlTradingUnitDO.getCode()))
                    .toList();
            PowerStationDO powerStationDO = powerStationDOS.stream()
                    .filter(station -> list.get(0).getOrgCode().equals(station.getCode()))
                    .toList().get(0);
            String collectionName = "constructs_" + powerStationDO.getOrgCode().toLowerCase();
//            Query query = new Query();
//            if (StringUtil.isNotBlank(ids) && ids.contains(","))
//                query.addCriteria(Criteria.where("contractsId").in(Arrays.asList(ids.split(","))));
//            if (StringUtil.isNotBlank(rowNos) && rowNos.contains(","))
//                query.addCriteria(Criteria.where("rowNo").in(Arrays.asList(rowNos.split(","))));
            Query query = new Query().addCriteria(Criteria.where("rowNo").is(Integer.parseInt(rowNos.split(",")[0]))).addCriteria(Criteria.where("contractsId").is(ids.split(",")[0]));
            List<ConstructsMongoVO> constructsMongoVOS = mongoTemplate.find(query, ConstructsMongoVO.class, collectionName);
            if (constructsMongoVOS != null && !constructsMongoVOS.isEmpty())
                System.out.println(constructsMongoVOS.get(0).getPowerCurve() + "------------------");
        }
        log.info("------------中长期持有量结算------------");

    }
}
