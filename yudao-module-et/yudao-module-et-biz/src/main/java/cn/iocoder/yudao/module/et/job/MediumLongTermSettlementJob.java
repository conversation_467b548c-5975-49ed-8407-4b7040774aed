package cn.iocoder.yudao.module.et.job;

import cn.iocoder.yudao.framework.common.util.string.StrUtils;
import cn.iocoder.yudao.module.et.controller.admin.contracts.vo.ConstructsMongoVO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.MysqlTradingUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import cn.iocoder.yudao.module.et.dal.mysql.contracts.ContractsMapper;
import cn.iocoder.yudao.module.et.dal.mysql.powerstation.PowerStationMapper;
import cn.iocoder.yudao.module.et.service.powerstation.PowerStationService;
import cn.iocoder.yudao.module.et.service.tradingunit.TradingUnitService;
import cn.iocoder.yudao.module.et.util.TradingUnitStatMComplementsService;
import cn.iocoder.yudao.module.et.util.TradingUnitStatMService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@EnableScheduling
@Component
@Slf4j
/**
 * 中长期持有量任务
 */
public class MediumLongTermSettlementJob {


    @Resource
    private ContractsMapper contractsMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Autowired
    private PowerStationService powerStationService;


    @Resource
    private TradingUnitService tradingUnitService;

    /**
     * 每天3点统计        trading_unit_${unitId}
     */
//    @Scheduled(cron = "0 45 3 * * ?")
    @Scheduled(cron = "*/10 * * * * ?")
    public void mediumLongTermSettlement() {
        log.info("------------中长期持有量结算------------");
        List<Map> maps = contractsMapper.selectContractsIdsAndRowNos();
        //场站
        List<PowerStationDO> powerStationDOS = powerStationService.selectAllByTenantIgnore();
        //交易单元
        List<MysqlTradingUnitDO> mysqlTradingUnitDOS = powerStationService.selectAllTradingUnitByTenantIgnore();

        for (Map map : maps) {
            String orgCode = String.valueOf(map.get("orgCode"));
            String ids = String.valueOf(map.get("ids"));
            String rowNos = String.valueOf(map.get("rowNos"));

            log.info("orgCode: {}, ids: {}, rowNos: {}", orgCode, ids, rowNos);

            List<MysqlTradingUnitDO> list = mysqlTradingUnitDOS
                    .stream()
                    .filter(mysqlTradingUnitDO -> orgCode.equals(mysqlTradingUnitDO.getCode()))
                    .toList();

            if (list.isEmpty()) {
                log.warn("未找到匹配的交易单元，orgCode: {}", orgCode);
                continue;
            }

            List<PowerStationDO> matchingStations = powerStationDOS.stream()
                    .filter(station -> list.get(0).getOrgCode().equals(station.getCode()))
                    .toList();

            if (matchingStations.isEmpty()) {
                log.warn("未找到匹配的场站，orgCode: {}", list.get(0).getOrgCode());
                continue;
            }

            PowerStationDO powerStationDO = matchingStations.get(0);
            String collectionName = "constructs_" + powerStationDO.getOrgCode().toLowerCase();


            // 重构MongoDB查询：使用AND逻辑，同时满足两个条件
            List<ConstructsMongoVO> constructsMongoVOS = new ArrayList<>();

            boolean hasIds = StringUtil.isNotBlank(ids) && !"null".equals(ids);
            boolean hasRowNos = StringUtil.isNotBlank(rowNos) && !"null".equals(rowNos);

            Query query = new Query();

            // 添加contractsId条件
            if (hasIds) {
                List<String> idsList;
                if (ids.contains(",")) {
                    idsList = Arrays.asList(ids.split(","));
                } else {
                    idsList = Collections.singletonList(ids);
                }
                query.addCriteria(Criteria.where("contractsId").in(idsList));
                log.info("添加contractsId IN条件: {}", idsList);
            }

            // 添加rowNo条件
            if (hasRowNos) {
                List<String> rowNosList;
                if (rowNos.contains(",")) {
                    rowNosList = Arrays.asList(rowNos.split(","));
                } else {
                    rowNosList = Collections.singletonList(rowNos);
                }
                query.addCriteria(Criteria.where("rowNo").in(rowNosList));
                log.info("添加rowNo IN条件: {}", rowNosList);
            }

            // 执行查询
            if (hasIds || hasRowNos) {
                log.info("执行MongoDB查询，集合: {}, 条件: {}", collectionName, query);
                constructsMongoVOS = mongoTemplate.find(query, ConstructsMongoVO.class, collectionName);
                log.info("查询结果数量: {}", constructsMongoVOS.size());

                // 如果没有结果，记录详细信息
                if (constructsMongoVOS.isEmpty()) {
                    log.warn("未查询到数据，检查集合是否存在");
                    try {
                        boolean collectionExists = mongoTemplate.collectionExists(collectionName);
                        log.info("集合 {} {}", collectionName, collectionExists ? "存在" : "不存在");

                        if (collectionExists) {
                            // 检查集合中是否有数据
                            long count = mongoTemplate.count(new Query(), collectionName);
                            log.info("集合 {} 中共有 {} 条数据", collectionName, count);

                            // 如果有数据但查询无结果，检查字段是否匹配
                            if (count > 0) {
                                log.info("集合中有数据但查询无结果，可能是字段名不匹配或数据不匹配条件");
                                // 获取一条示例数据
                                ConstructsMongoVO sample = mongoTemplate.findOne(new Query().limit(1), ConstructsMongoVO.class, collectionName);
                                if (sample != null) {
                                    log.info("示例数据: contractsId={}, rowNo={}", sample.getConstructsId(), sample.getRowNo());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("检查集合时发生错误", e);
                    }
                }
            } else {
                // 没有查询条件，查询所有数据
                log.info("没有查询条件，查询集合中所有数据");
                constructsMongoVOS = mongoTemplate.findAll(ConstructsMongoVO.class, collectionName);
            }
            if (constructsMongoVOS != null && !constructsMongoVOS.isEmpty()) {
                System.out.println(constructsMongoVOS.get(0).getPowerCurve() + "------------------");
                log.info("找到数据，powerCurve: {}", constructsMongoVOS.get(0).getPowerCurve());
            } else {
                log.warn("未查询到数据，集合: {}, 查询条件: ids={}, rowNos={}", collectionName, ids, rowNos);
            }
        }
        log.info("------------中长期持有量结算------------");

    }
}
