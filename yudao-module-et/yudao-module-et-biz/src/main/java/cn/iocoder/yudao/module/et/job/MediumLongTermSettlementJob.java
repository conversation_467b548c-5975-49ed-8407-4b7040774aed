package cn.iocoder.yudao.module.et.job;

import cn.iocoder.yudao.framework.common.util.string.StrUtils;
import cn.iocoder.yudao.module.et.controller.admin.contracts.vo.ConstructsMongoVO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.MysqlTradingUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import cn.iocoder.yudao.module.et.dal.dataobject.tradingunit.TradingUnitDO;
import cn.iocoder.yudao.module.et.dal.mysql.contracts.ContractsMapper;
import cn.iocoder.yudao.module.et.dal.mysql.powerstation.PowerStationMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.dataproc.PrivateDataProcMapper;
import cn.iocoder.yudao.module.et.service.powerstation.PowerStationService;
import cn.iocoder.yudao.module.et.service.tradingunit.TradingUnitService;
import cn.iocoder.yudao.module.et.util.TradingUnitStatMComplementsService;
import cn.iocoder.yudao.module.et.util.TradingUnitStatMService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.sql.Array;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@EnableScheduling
@Component
@Slf4j
/**
 * 中长期持有量任务
 */
public class MediumLongTermSettlementJob {


    @Resource
    private ContractsMapper contractsMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Autowired
    private PowerStationService powerStationService;


    @Resource
    private PrivateDataProcMapper privateDataProcMapper;

    /**
     * 每天3点统计        trading_unit_${unitId} medium_long_term_settlement_curves
     */
//    @Scheduled(cron = "0 45 3 * * ?")
    @Scheduled(cron = "*/10 * * * * ?")
    public void mediumLongTermSettlement() {
        log.info("------------中长期持有量结算------------");
        List<Map> maps = contractsMapper.selectContractsIdsAndRowNos();
        //场站
        List<PowerStationDO> powerStationDOS = powerStationService.selectAllByTenantIgnore();
        //交易单元
        List<MysqlTradingUnitDO> mysqlTradingUnitDOS = powerStationService.selectAllTradingUnitByTenantIgnore();

        for (Map map : maps) {
            String orgCode = String.valueOf(map.get("orgCode"));
            String ids = String.valueOf(map.get("ids"));
            String rowNos = String.valueOf(map.get("rowNos"));
            String runDate = String.valueOf(map.get("runDate"));
            log.info("orgCode: {}, ids: {}, rowNos: {}", orgCode, ids, rowNos);
            List<MysqlTradingUnitDO> list = mysqlTradingUnitDOS
                    .stream()
                    .filter(mysqlTradingUnitDO -> orgCode.equals(mysqlTradingUnitDO.getCode()))
                    .toList();

            if (list.isEmpty()) {
                log.warn("未找到匹配的交易单元，orgCode: {}", orgCode);
                continue;
            }

            List<PowerStationDO> matchingStations = powerStationDOS.stream()
                    .filter(station -> list.get(0).getOrgCode().equals(station.getCode()))
                    .toList();

            if (matchingStations.isEmpty()) {
                log.warn("未找到匹配的场站，orgCode: {}", list.get(0).getOrgCode());
                continue;
            }

            PowerStationDO powerStationDO = matchingStations.get(0);
            String collectionName = "constructs_" + powerStationDO.getOrgCode().toLowerCase();


            List<ConstructsMongoVO> constructsMongoVOS = new ArrayList<>();

            boolean hasIds = StringUtil.isNotBlank(ids) && !"null".equals(ids);
            boolean hasRowNos = StringUtil.isNotBlank(rowNos) && !"null".equals(rowNos);
            if (hasIds && hasRowNos) {
                Query query = new Query();
                List<String> idsList = ids.contains(",")
                        ? Arrays.asList(ids.split(","))
                        : Collections.singletonList(ids);
                query.addCriteria(Criteria.where("contractsId").in(idsList));
                List<Integer> rowNosList = new ArrayList<>();
                if (rowNos.contains(",")) {
                    for (String rowNo : rowNos.split(",")) {
                        rowNosList.add(Integer.parseInt(rowNo.trim()));
                    }
                } else {
                    rowNosList.add(Integer.parseInt(rowNos.trim()));
                }
                query.addCriteria(Criteria.where("rowNo").in(rowNosList));
                log.info("中长期持有量查询条件 - contractsId: {}, rowNo: {}", idsList, rowNosList);
                constructsMongoVOS = mongoTemplate.find(query, ConstructsMongoVO.class, collectionName);
                log.info("查询结果数量: {}", constructsMongoVOS.size());
            }
            if (constructsMongoVOS != null && !constructsMongoVOS.isEmpty()) {
                // 聚合powerCurve
                ArrayList<TradingUnitDO> ins = new ArrayList<>();
                double[] aggregatedPowerCurve = new double[96];
                for (ConstructsMongoVO vo : constructsMongoVOS) {
                    String powerCurve = vo.getPowerCurve();
                    if (StringUtil.isNotBlank(powerCurve)) {
                        String[] values = powerCurve.split(",");
                        for (int i = 0; i < values.length && i < 96; i++) {
                            String value1 = values[i];
                            if (value1 == null || "".equals(value1) || "null".equals(value1)) value1 = "0";
                            double value = Double.parseDouble(value1);
                            //聚合每个时段的值
                            aggregatedPowerCurve[i] += value;
                        }
                    }
                }
                // 根据runDate生成时间段并拼接数据
                StringBuilder aggregatedResult = new StringBuilder();

                // 解析runDate，如果为null或解析失败，使用当前日期
                LocalDate baseDate;
                try {
                    if (StringUtil.isNotBlank(runDate) && !"null".equals(runDate)) {
                        baseDate = LocalDate.parse(runDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    } else {
                        baseDate = LocalDate.now();
                    }
                } catch (Exception e) {
                    log.warn("解析runDate失败: {}, 使用当前日期", runDate);
                    baseDate = LocalDate.now();
                }

                log.info("基准日期: {}", baseDate);

                // 生成96个时间段：从00:15:00开始，每15分钟一个间隔，到第二天00:00:00
                for (int i = 0; i < aggregatedPowerCurve.length; i++) {
                    // 计算时间：从00:15开始，每15分钟递增
                    int totalMinutes = 15 + (i * 15); // 第一个是00:15，然后每15分钟递增
                    int hours = (totalMinutes / 60) % 24;
                    int minutes = totalMinutes % 60;

                    // 如果超过24小时，说明是第二天
                    LocalDateTime dateTime;
                    if (totalMinutes >= 1440) { // 1440分钟 = 24小时
                        dateTime = baseDate.plusDays(1).atTime(hours, minutes);
                    } else {
                        dateTime = baseDate.atTime(hours, minutes);
                    }

                    String timeStr = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    double value = aggregatedPowerCurve[i];

                    if (i > 0) {
                        aggregatedResult.append("\n");
                    }
                    aggregatedResult.append(String.format("%s: %.6f", timeStr, value));
                    TradingUnitDO tradingUnitDO = new TradingUnitDO();
                    tradingUnitDO.setTs(Timestamp.valueOf(timeStr));
                    tradingUnitDO.setMediumLongTermSettlementCurves((float) value);
                    ins.add(tradingUnitDO);
                    System.out.println(String.format("时间段[%d]: %s -> %.6f", i + 1, timeStr, value));
                }
                privateDataProcMapper.insertMediumLongTermSettlementCurves(orgCode, ins);
                log.info("聚合后的时间段数据:\n{}", aggregatedResult);
                log.info("聚合后的数据:\n{}", aggregatedPowerCurve);
            }
        }
        log.info("------------中长期持有量结算------------");

    }
}
