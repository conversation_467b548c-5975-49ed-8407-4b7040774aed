package cn.iocoder.yudao.module.et.job;

import cn.iocoder.yudao.framework.common.util.string.StrUtils;
import cn.iocoder.yudao.module.et.controller.admin.contracts.vo.ConstructsMongoVO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.MysqlTradingUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import cn.iocoder.yudao.module.et.dal.mysql.contracts.ContractsMapper;
import cn.iocoder.yudao.module.et.dal.mysql.powerstation.PowerStationMapper;
import cn.iocoder.yudao.module.et.service.powerstation.PowerStationService;
import cn.iocoder.yudao.module.et.service.tradingunit.TradingUnitService;
import cn.iocoder.yudao.module.et.util.TradingUnitStatMComplementsService;
import cn.iocoder.yudao.module.et.util.TradingUnitStatMService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@EnableScheduling
@Component
@Slf4j
/**
 * 中长期持有量任务
 */
public class MediumLongTermSettlementJob {


    @Resource
    private ContractsMapper contractsMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Autowired
    private PowerStationService powerStationService;


    @Resource
    private TradingUnitService tradingUnitService;

    /**
     * 每天3点统计        trading_unit_${unitId}
     */
//    @Scheduled(cron = "0 45 3 * * ?")
    @Scheduled(cron = "*/10 * * * * ?")
    public void mediumLongTermSettlement() {
        log.info("------------中长期持有量结算------------");
        List<Map> maps = contractsMapper.selectContractsIdsAndRowNos();
        //场站
        List<PowerStationDO> powerStationDOS = powerStationService.selectAllByTenantIgnore();
        //交易单元
        List<MysqlTradingUnitDO> mysqlTradingUnitDOS = powerStationService.selectAllTradingUnitByTenantIgnore();

        for (Map map : maps) {
            String orgCode = String.valueOf(map.get("orgCode"));
            String ids = String.valueOf(map.get("ids"));
            String rowNos = String.valueOf(map.get("rowNos"));

            log.info("orgCode: {}, ids: {}, rowNos: {}", orgCode, ids, rowNos);

            List<MysqlTradingUnitDO> list = mysqlTradingUnitDOS
                    .stream()
                    .filter(mysqlTradingUnitDO -> orgCode.equals(mysqlTradingUnitDO.getCode()))
                    .toList();

            if (list.isEmpty()) {
                log.warn("未找到匹配的交易单元，orgCode: {}", orgCode);
                continue;
            }

            List<PowerStationDO> matchingStations = powerStationDOS.stream()
                    .filter(station -> list.get(0).getOrgCode().equals(station.getCode()))
                    .toList();

            if (matchingStations.isEmpty()) {
                log.warn("未找到匹配的场站，orgCode: {}", list.get(0).getOrgCode());
                continue;
            }

            PowerStationDO powerStationDO = matchingStations.get(0);
            String collectionName = "constructs_" + powerStationDO.getOrgCode().toLowerCase();


            // 重构MongoDB查询：使用OR逻辑或分别查询
            List<ConstructsMongoVO> constructsMongoVOS = new ArrayList<>();

            boolean hasIds = StringUtil.isNotBlank(ids) && !"null".equals(ids);
            boolean hasRowNos = StringUtil.isNotBlank(rowNos) && !"null".equals(rowNos);

            if (hasIds && hasRowNos) {
                // 方案1：使用OR逻辑查询 (满足任一条件即可)
                Query orQuery = new Query();
                Criteria idsCriteria = ids.contains(",")
                    ? Criteria.where("contractsId").in(Arrays.asList(ids.split(",")))
                    : Criteria.where("contractsId").is(ids);

                Criteria rowNosCriteria = rowNos.contains(",")
                    ? Criteria.where("rowNo").in(Arrays.asList(rowNos.split(",")))
                    : Criteria.where("rowNo").is(rowNos);

                orQuery.addCriteria(new Criteria().orOperator(idsCriteria, rowNosCriteria));
                log.info("使用OR查询条件: contractsId={} OR rowNo={}", ids, rowNos);
                constructsMongoVOS = mongoTemplate.find(orQuery, ConstructsMongoVO.class, collectionName);

                // 如果OR查询没有结果，尝试AND查询
                if (constructsMongoVOS.isEmpty()) {
                    Query andQuery = new Query();
                    andQuery.addCriteria(idsCriteria);
                    andQuery.addCriteria(rowNosCriteria);
                    log.info("OR查询无结果，尝试AND查询条件: contractsId={} AND rowNo={}", ids, rowNos);
                    constructsMongoVOS = mongoTemplate.find(andQuery, ConstructsMongoVO.class, collectionName);
                }

                // 如果还是没有结果，分别查询
                if (constructsMongoVOS.isEmpty()) {
                    log.info("AND查询也无结果，分别查询...");

                    // 只按contractsId查询
                    Query idsOnlyQuery = new Query();
                    idsOnlyQuery.addCriteria(idsCriteria);
                    List<ConstructsMongoVO> idResults = mongoTemplate.find(idsOnlyQuery, ConstructsMongoVO.class, collectionName);
                    log.info("仅按contractsId查询结果数量: {}", idResults.size());

                    // 只按rowNo查询
                    Query rowNosOnlyQuery = new Query();
                    rowNosOnlyQuery.addCriteria(rowNosCriteria);
                    List<ConstructsMongoVO> rowNoResults = mongoTemplate.find(rowNosOnlyQuery, ConstructsMongoVO.class, collectionName);
                    log.info("仅按rowNo查询结果数量: {}", rowNoResults.size());

                    // 合并结果并去重
                    constructsMongoVOS.addAll(idResults);
                    constructsMongoVOS.addAll(rowNoResults);
                    constructsMongoVOS = constructsMongoVOS.stream().distinct().collect(Collectors.toList());
                }

            } else if (hasIds) {
                // 只有contractsId条件
                Query query = new Query();
                if (ids.contains(",")) {
                    query.addCriteria(Criteria.where("contractsId").in(Arrays.asList(ids.split(","))));
                } else {
                    query.addCriteria(Criteria.where("contractsId").is(ids));
                }
                log.info("仅添加contractsId查询条件: {}", ids);
                constructsMongoVOS = mongoTemplate.find(query, ConstructsMongoVO.class, collectionName);

            } else if (hasRowNos) {
                // 只有rowNo条件
                Query query = new Query();
                if (rowNos.contains(",")) {
                    query.addCriteria(Criteria.where("rowNo").in(Arrays.asList(rowNos.split(","))));
                } else {
                    query.addCriteria(Criteria.where("rowNo").is(rowNos));
                }
                log.info("仅添加rowNo查询条件: {}", rowNos);
                constructsMongoVOS = mongoTemplate.find(query, ConstructsMongoVO.class, collectionName);

            } else {
                // 没有查询条件，查询所有数据
                log.info("没有查询条件，查询集合中所有数据");
                constructsMongoVOS = mongoTemplate.findAll(ConstructsMongoVO.class, collectionName);
            }
            if (constructsMongoVOS != null && !constructsMongoVOS.isEmpty()) {
                System.out.println(constructsMongoVOS.get(0).getPowerCurve() + "------------------");
                log.info("找到数据，powerCurve: {}", constructsMongoVOS.get(0).getPowerCurve());
            } else {
                log.warn("未查询到数据，集合: {}, 查询条件: ids={}, rowNos={}", collectionName, ids, rowNos);
            }
        }
        log.info("------------中长期持有量结算------------");

    }
}
