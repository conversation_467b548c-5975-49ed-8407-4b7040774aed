package cn.iocoder.yudao.module.et.job;

import cn.iocoder.yudao.framework.common.util.string.StrUtils;
import cn.iocoder.yudao.module.et.controller.admin.contracts.vo.ConstructsMongoVO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.MysqlTradingUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import cn.iocoder.yudao.module.et.dal.mysql.contracts.ContractsMapper;
import cn.iocoder.yudao.module.et.dal.mysql.powerstation.PowerStationMapper;
import cn.iocoder.yudao.module.et.service.powerstation.PowerStationService;
import cn.iocoder.yudao.module.et.service.tradingunit.TradingUnitService;
import cn.iocoder.yudao.module.et.util.TradingUnitStatMComplementsService;
import cn.iocoder.yudao.module.et.util.TradingUnitStatMService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@EnableScheduling
@Component
@Slf4j
/**
 * 中长期持有量任务
 */
public class MediumLongTermSettlementJob {


    @Resource
    private ContractsMapper contractsMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Autowired
    private PowerStationService powerStationService;


    @Resource
    private TradingUnitService tradingUnitService;

    /**
     * 每天3点统计        trading_unit_${unitId}
     */
//    @Scheduled(cron = "0 45 3 * * ?")
    @Scheduled(cron = "*/10 * * * * ?")
    public void mediumLongTermSettlement() {
        log.info("------------中长期持有量结算------------");
        List<Map> maps = contractsMapper.selectContractsIdsAndRowNos();
        //场站
        List<PowerStationDO> powerStationDOS = powerStationService.selectAllByTenantIgnore();
        //交易单元
        List<MysqlTradingUnitDO> mysqlTradingUnitDOS = powerStationService.selectAllTradingUnitByTenantIgnore();

        for (Map map : maps) {
            String orgCode = String.valueOf(map.get("orgCode"));
            String ids = String.valueOf(map.get("ids"));
            String rowNos = String.valueOf(map.get("rowNos"));

            log.info("orgCode: {}, ids: {}, rowNos: {}", orgCode, ids, rowNos);

            List<MysqlTradingUnitDO> list = mysqlTradingUnitDOS
                    .stream()
                    .filter(mysqlTradingUnitDO -> orgCode.equals(mysqlTradingUnitDO.getCode()))
                    .toList();

            if (list.isEmpty()) {
                log.warn("未找到匹配的交易单元，orgCode: {}", orgCode);
                continue;
            }

            List<PowerStationDO> matchingStations = powerStationDOS.stream()
                    .filter(station -> list.get(0).getOrgCode().equals(station.getCode()))
                    .toList();

            if (matchingStations.isEmpty()) {
                log.warn("未找到匹配的场站，orgCode: {}", list.get(0).getOrgCode());
                continue;
            }

            PowerStationDO powerStationDO = matchingStations.get(0);
            String collectionName = "constructs_" + powerStationDO.getOrgCode().toLowerCase();


            // 只查询同时满足两个条件的数据
            List<ConstructsMongoVO> constructsMongoVOS = new ArrayList<>();

            boolean hasIds = StringUtil.isNotBlank(ids) && !"null".equals(ids);
            boolean hasRowNos = StringUtil.isNotBlank(rowNos) && !"null".equals(rowNos);

            // 必须同时有两个条件才查询
            if (hasIds && hasRowNos) {
                Query query = new Query();

                // 添加contractsId条件（字符串类型）
                List<String> idsList = ids.contains(",")
                    ? Arrays.asList(ids.split(","))
                    : Collections.singletonList(ids);
                query.addCriteria(Criteria.where("contractsId").in(idsList));

                // 添加rowNo条件（转换为数值类型）
                List<Integer> rowNosList = new ArrayList<>();
                try {
                    if (rowNos.contains(",")) {
                        for (String rowNo : rowNos.split(",")) {
                            rowNosList.add(Integer.parseInt(rowNo.trim()));
                        }
                    } else {
                        rowNosList.add(Integer.parseInt(rowNos.trim()));
                    }
                    query.addCriteria(Criteria.where("rowNo").in(rowNosList));

                    log.info("查询条件 - contractsId: {}, rowNo: {}", idsList, rowNosList);
                    constructsMongoVOS = mongoTemplate.find(query, ConstructsMongoVO.class, collectionName);
                    log.info("查询结果数量: {}", constructsMongoVOS.size());

                } catch (NumberFormatException e) {
                    log.error("rowNo转换为数字失败: {}", rowNos, e);
                }
            } else {
                log.warn("缺少查询条件 - hasIds: {}, hasRowNos: {}", hasIds, hasRowNos);
            }
            if (constructsMongoVOS != null && !constructsMongoVOS.isEmpty()) {
                System.out.println(constructsMongoVOS.get(0).getPowerCurve() + "------------------");
                log.info("找到数据，powerCurve: {}", constructsMongoVOS.get(0).getPowerCurve());
            } else {
                log.warn("未查询到数据，集合: {}, 查询条件: ids={}, rowNos={}", collectionName, ids, rowNos);
            }
        }
        log.info("------------中长期持有量结算------------");

    }
}
