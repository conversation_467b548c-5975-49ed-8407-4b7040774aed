package cn.iocoder.yudao.module.et.controller.admin.contractstime24.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 合同分时段信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContractsTime24RespVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2143")
    @ExcelProperty("自增主键")
    private Long id;

    @Schema(description = "时段编号")
    @ExcelProperty("时段编号")
    private String timeSlotCoding;

    @Schema(description = "时段名称", example = "张三")
    @ExcelProperty("时段名称")
    private String timeSlotName;

    @Schema(description = "购方电量")
    @ExcelProperty("购方电量")
    private BigDecimal purchaserQuantity;

    @Schema(description = "售方电量")
    @ExcelProperty("售方电量")
    private BigDecimal sellerQuantity;

    @Schema(description = "购方电价", example = "9432")
    @ExcelProperty("购方电价")
    private BigDecimal purchaserPrice;

    @Schema(description = "售方电价", example = "20518")
    @ExcelProperty("售方电价")
    private BigDecimal sellerPrice;

    @Schema(description = "部门ID", example = "8005")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    @ExcelProperty("上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    @ExcelProperty("数据爬取时间")
    private LocalDateTime gatherDate;

    @Schema(description = "修改时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("修改时间")
    private LocalDateTime modificationTime;

    @Schema(description = "时段区间")
    @ExcelProperty("时段区间")
    private String timeSlotsRange;

    @Schema(description = "时间段")
    @ExcelProperty("时间段")
    private String timeSlots;

    @Schema(description = "时间段开始日期")
    @ExcelProperty("时间段开始日期")
    private LocalDateTime beginTime;

    @Schema(description = "时间段结束日期")
    @ExcelProperty("时间段结束日期")
    private LocalDateTime endTime;

    @Schema(description = "合同id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25739")
    @ExcelProperty("合同id")
    private String contractsId;

}