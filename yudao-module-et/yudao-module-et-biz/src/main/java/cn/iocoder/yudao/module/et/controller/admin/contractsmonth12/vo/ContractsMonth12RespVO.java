package cn.iocoder.yudao.module.et.controller.admin.contractsmonth12.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 合同分月信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ContractsMonth12RespVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10044")
    @ExcelProperty("自增主键")
    private Long id;

    @Schema(description = "分月电量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分月电量")
    private BigDecimal quantity;

    @Schema(description = "分月电价", requiredMode = Schema.RequiredMode.REQUIRED, example = "29264")
    @ExcelProperty("分月电价")
    private BigDecimal price;

    @Schema(description = "部门ID", example = "4645")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "上级机构唯一编码（dept扩展字段）")
    @ExcelProperty("上级机构唯一编码（dept扩展字段）")
    private String orgCode;

    @Schema(description = "数据爬取时间")
    @ExcelProperty("数据爬取时间")
    private LocalDateTime gatherDate;

    @Schema(description = "修改时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("修改时间")
    private LocalDateTime modificationTime;

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开始日期")
    private LocalDateTime beginTime;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("结束日期")
    private LocalDateTime endTime;

    @Schema(description = "合同id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20843")
    @ExcelProperty("合同id")
    private String contractsId;

}