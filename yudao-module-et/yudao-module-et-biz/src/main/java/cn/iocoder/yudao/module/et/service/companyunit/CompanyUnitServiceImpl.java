package cn.iocoder.yudao.module.et.service.companyunit;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.et.controller.admin.companyunit.vo.CompanyUnitRespVO;
import cn.iocoder.yudao.module.et.controller.admin.startupanddownunitdetail.vo.StartupAndDownUnitDetailRespVO;
import cn.iocoder.yudao.module.et.dal.dataobject.HefengDay.HeFengDay;
import cn.iocoder.yudao.module.et.dal.dataobject.companyunit.CompanyUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.dispatchnode.DispatchNodeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.districtarea.DistrictAreaDO;
import cn.iocoder.yudao.module.et.dal.dataobject.districtcode.DistrictCodeDO;
import cn.iocoder.yudao.module.et.dal.dataobject.mustOpenAndStopUnit.StartupAndDownUnitDO;
import cn.iocoder.yudao.module.et.dal.dataobject.powerstation.PowerStationDO;
import cn.iocoder.yudao.module.et.dal.dataobject.priceforecasting.PriceForecastingDO;
import cn.iocoder.yudao.module.et.dal.dataobject.startupanddownunitdetail.StartupAndDownUnitDetailDO;
import cn.iocoder.yudao.module.et.dal.dataobject.thermalCoalPrice.ThermalCoalPrice;
import cn.iocoder.yudao.module.et.dal.dataobject.timeslots.TimeSlotsDO;
import cn.iocoder.yudao.module.et.dal.mysql.dispatchnode.DispatchNodeMapper;
import cn.iocoder.yudao.module.et.dal.mysql.districtcode.DistrictCodeMapper;
import cn.iocoder.yudao.module.et.dal.mysql.jyThermalCoalPrice.JyThermalCoalPriceMapper;
import cn.iocoder.yudao.module.et.dal.mysql.mustOpenAndStopUnit.MustOpenAndStopUnitMapper;
import cn.iocoder.yudao.module.et.dal.mysql.startupanddownunitdetail.StartupAndDownUnitDetailMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.companyunit.CompanyUnitMapper;
import cn.iocoder.yudao.module.et.dal.tdengine.hefeng.HeFengDayMapper;
import cn.iocoder.yudao.module.et.service.districtarea.DistrictAreaService;
import cn.iocoder.yudao.module.et.service.powerstation.PowerStationService;
import cn.iocoder.yudao.module.et.service.timeslots.TimeSlotsService;
import cn.iocoder.yudao.module.et.util.JyDateTimeUtil;
import cn.iocoder.yudao.module.et.util.MapSortUtils;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 区域公司 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CompanyUnitServiceImpl implements CompanyUnitService {

    private final String[] str = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24"};
    @Resource
    private CompanyUnitMapper companyUnitMapper;
    @Resource
    private DeptApi deptApi;
    @Resource
    private TenantApi tenantApi;
    @Resource
    private JyThermalCoalPriceMapper jyThermalCoalPriceMapper;
    @Resource
    private MustOpenAndStopUnitMapper mustOpenAndStopUnitMapper;
    @Resource
    private StartupAndDownUnitDetailMapper startupAndDownUnitDetailMapper;
    @Resource
    private DistrictAreaService districtAreaService;
    @Resource
    private HeFengDayMapper heFengDayMapper;
    @Resource
    private DistrictCodeMapper districtCodeMapper;
    @Resource
    private PowerStationService powerStationService;
    @Resource
    private DispatchNodeMapper dispatchNodeMapper;
    @Resource
    private TimeSlotsService timeSlotsService;
    @Resource
    private TenantService tenantService;

    /**
     * 从给定的tradingUnitDOList中提取每15分钟的数据
     */
    public static List<CompanyUnitDO> extractFifteenMinuteData(List<CompanyUnitDO> companyUnitDOList) {
        List<CompanyUnitDO> filteredList = new ArrayList<>();

        // 过滤出每15分钟的数据
        for (CompanyUnitDO companyUnitDO : companyUnitDOList) {
            long timestampMillis = companyUnitDO.getTs().getTime();
            Instant instant = Instant.ofEpochMilli(timestampMillis);
            LocalDateTime timeOfDay = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalTime time = timeOfDay.toLocalTime();

            // 计算是否为15分钟的倍数
            int minutes = time.getMinute();
            if (minutes % 15 == 0) {
                filteredList.add(companyUnitDO);
            }
        }

        return filteredList;
    }

    public static Map<String, List<CompanyUnitDO>> groupDataByDate(List<CompanyUnitDO> extractList) {
        Map<String, List<CompanyUnitDO>> groupedData = new HashMap<>();
        // 分组数据
        for (CompanyUnitDO companyUnitDO : extractList) {
            long timestampMillis = companyUnitDO.getTs().getTime() - 900000;
            companyUnitDO.setHisUnifiedSettlementPointPrice(companyUnitDO.getHisUnifiedSettlementPointPrice().equals("--") ? null : new BigDecimal(companyUnitDO.getHisUnifiedSettlementPointPrice()).setScale(2, RoundingMode.HALF_UP).floatValue());
            companyUnitDO.setRealUnifiedSettlementPointPrice(companyUnitDO.getRealUnifiedSettlementPointPrice().equals("--") ? null : new BigDecimal(companyUnitDO.getRealUnifiedSettlementPointPrice()).setScale(2, RoundingMode.HALF_UP).floatValue());
            Instant instant = Instant.ofEpochMilli(timestampMillis);
            LocalDateTime timeOfDay = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
            String dateKey = timeOfDay.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
            groupedData.computeIfAbsent(dateKey, k -> new ArrayList<>()).add(companyUnitDO);
        }
        return groupedData;
    }

    public static Map<String, List<CompanyUnitDO>> sortMapByDate(Map<String, List<CompanyUnitDO>> originalMap) {
        // 将原始的 Map 转换为一个包含日期字符串和对应列表的列表
        List<Map.Entry<String, List<CompanyUnitDO>>> entryList = new ArrayList<>(originalMap.entrySet());

        // 使用自定义比较器对列表进行排序
        Collections.sort(entryList, new Comparator<Map.Entry<String, List<CompanyUnitDO>>>() {
            @Override
            public int compare(Map.Entry<String, List<CompanyUnitDO>> e1, Map.Entry<String, List<CompanyUnitDO>> e2) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
                LocalDate date1 = LocalDate.parse(e1.getKey(), formatter);
                LocalDate date2 = LocalDate.parse(e2.getKey(), formatter);
                return date1.compareTo(date2);
            }
        });

        // 将排序后的列表转换回 Map
        return entryList.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                (oldValue, newValue) -> oldValue, LinkedHashMap::new));
    }

    private static double calculateQuantile(double[] data, double percentile) {
        double index = (data.length - 1) * percentile;
        int floor = (int) Math.floor(index);
        int ceil = (int) Math.ceil(index);

        if (floor == ceil) {
            return data[floor];
        } else {
            double lowerValue = data[floor] * (ceil - index);
            double upperValue = data[ceil] * (index - floor);
            return lowerValue + upperValue;
        }
    }

    public static double[] plot(double[] data) {
        Arrays.sort(data);
        double q1 = calculateQuantile(data, 0.25);
        double q2 = calculateQuantile(data, 0.5);
        double q3 = calculateQuantile(data, 0.75);
        double iqr = q3 - q1;

        //最大观测值
        double maxInRegion = q3 + 1.5 * iqr;
        //最小观测值
        double mixInRegion = q1 - 1.5 * iqr;

        return new double[]{BigDecimal.valueOf(mixInRegion).setScale(2, RoundingMode.HALF_UP).doubleValue(),
                BigDecimal.valueOf(q1).setScale(2, RoundingMode.HALF_UP).doubleValue(),
                BigDecimal.valueOf(q2).setScale(2, RoundingMode.HALF_UP).doubleValue(),
                BigDecimal.valueOf(q3).setScale(2, RoundingMode.HALF_UP).doubleValue(),
                BigDecimal.valueOf(maxInRegion).setScale(2, RoundingMode.HALF_UP).doubleValue()};

    }

    private BigDecimal parseNumber(String number) {
        return number.equals("--") ? BigDecimal.ZERO : new BigDecimal(number).setScale(2, RoundingMode.HALF_UP);
    }

    public Float getFloatValueOrZero(String value) {
        return "--".equals(value) ? 0 : Convert.toFloat(value);
    }

    /**
     * 查询省级披露数据
     *
     * @param companyId 区域公司ID
     * @param startTs   ts 开始区间
     * @param endTs     ts 结束区间
     */
    @Override
    public HashMap<String, Object> queryProvincialLevelData(String companyId, Timestamp startTs, Timestamp endTs) {
        HashMap<String, Object> dayHeadDataMap = new HashMap<>();
        List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.queryProvincialLevelData(companyId, startTs, endTs);
        if (companyUnitDOList.isEmpty()) {
            log.error("查询省级披露数据 为空，入参：{}，ts:{} ", companyId, startTs + "-" + endTs);
            return dayHeadDataMap;
        }
        companyUnitDOList.forEach(x -> x.setTs(JyDateTimeUtil.minusTimestamp15Minutes(x.getTs())));

        Map<String, CompanyUnitDO> companyUnitDOListMinutesMap = companyUnitDOList.stream().collect(Collectors.toMap(x -> JyDateTimeUtil.getFormatDateStr2(x.getTs().getTime()), x -> x, (x1, x2) -> x1));
        //按时间正序排列
        companyUnitDOListMinutesMap = MapSortUtils.sortByKey(companyUnitDOListMinutesMap, false);

        //获取该省份需要使用正常算法还是交叉算法的数据
        String districtAreaCode = getCurrentUser();
        DistrictAreaDO districtAreaDO = districtAreaService.selectByCode(districtAreaCode);
        //类型 normal或者cross
        String model = districtAreaDO.getPredictionModel();
        //日电价预测
        //预测日的值是由维国提供的数据(根据页面时间查询数据) 相似日是由维国提供的相似日日期查询数据库存储该日期的电价    偏差就是预测-相似
        //预测日电价数据 相似日电价数据是从库里查询也就是从similarDOList里拿
        List<PriceForecastingDO> priceForecastingDOList = companyUnitMapper.getPriceForecast1(districtAreaCode, startTs, endTs);
        priceForecastingDOList.forEach(x -> x.setTs(JyDateTimeUtil.minusTimestamp15Minutes(x.getTs())));
        Map<String, PriceForecastingDO> priceForecastingDOListMap = priceForecastingDOList.stream().collect(Collectors.toMap(x -> JyDateTimeUtil.getFormatDateStr2(x.getTs().getTime()), x -> x, (x1, x2) -> x1));

        LinkedHashSet<String> timeLine = new LinkedHashSet<>();

        //1 构造 日前市场供需情况 曲线：竞价空间 省调负荷  外送电 新能源负荷
        //2 构造 市场价格趋势 曲线：统一出清价格-日前  统一出清价格-实时  预测价格
        //竞价空间
        List<Float> jingJia = new ArrayList<>();
        //省调负荷
        List<Float> shengDiaoFuHe = new ArrayList<>();
        //外送电
        List<Float> waiSongDian = new ArrayList<>();
        //新能源负荷
        List<Float> xinNengYuanFuHe = new ArrayList<>();
        //统一出清价格-日前
        List<Float> chuQingRiQian = new ArrayList<>();
        //统一出清价格-实时
        List<Float> chuQingShiShi = new ArrayList<>();
        //预测价格
        List<Float> jiaGeYuCe = new ArrayList<>();
        companyUnitDOListMinutesMap.forEach((day, companyUnitDO) -> {

            timeLine.add(JyDateTimeUtil.getAdd15MinPoint(day));

            jingJia.add(getFloatValueOrZero(companyUnitDO.getBiddingSpace()));
            shengDiaoFuHe.add(getFloatValueOrZero(companyUnitDO.getSystemForecastB59()));
            waiSongDian.add(companyUnitDO.getTieLineForecast());
            xinNengYuanFuHe.add(getFloatValueOrZero(companyUnitDO.getLoadForecast()));

            chuQingRiQian.add(getFloatValueOrZero(companyUnitDO.getHisUnifiedSettlementPointPrice()));
            chuQingShiShi.add(getFloatValueOrZero(companyUnitDO.getRealUnifiedSettlementPointPrice()));
            PriceForecastingDO priceForecastingDO = priceForecastingDOListMap.get(day);
            if (null != priceForecastingDO) {
                if ("normal".equals(model)) {
                    jiaGeYuCe.add(getFloatValueOrZero(priceForecastingDO.getNormal()));
                } else {
                    jiaGeYuCe.add(getFloatValueOrZero(priceForecastingDO.getCross()));
                }

            } else {
                jiaGeYuCe.add(0f);
            }

        });
        dayHeadDataMap.put("timeLine", timeLine);
        dayHeadDataMap.put("jingJia", jingJia);
        dayHeadDataMap.put("shengDiaoFuHe", shengDiaoFuHe);
        dayHeadDataMap.put("waiSongDian", waiSongDian);
        dayHeadDataMap.put("xinNengYuanFuHe", xinNengYuanFuHe);
        dayHeadDataMap.put("chuQingRiQian", chuQingRiQian);
        dayHeadDataMap.put("chuQingShiShi", chuQingShiShi);
        dayHeadDataMap.put("jiaGeYuCe", jiaGeYuCe);

        return dayHeadDataMap;
    }

    /**
     * 价格与竞价空间趋势
     *
     * @param date
     * @return
     */
    @Override
    public Map<String, Object> getPriceBiddingSpaceRends(Long[] date) {
        String companyId = this.getCurrentUserOrgCode();
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }
        List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getPriceBiddingSpaceRends(companyId, timestamps[0], timestamps[timestamps.length - 1]);
        //竞价空间List
        List<String> biddingSpaceList;
        //新能源预测List
        List<String> loadForecastList;
        //日前价格List
        List<String> hisUnifiedSettlementPointPriceList;
        biddingSpaceList = companyUnitDOList.stream().map(CompanyUnitDO::getBiddingSpace).toList();
        loadForecastList = companyUnitDOList.stream().map(CompanyUnitDO::getLoadForecast).toList();
        hisUnifiedSettlementPointPriceList = companyUnitDOList.stream().map(CompanyUnitDO::getHisUnifiedSettlementPointPrice).toList();
        //竞价空间/开机容量
        List<Float> differences = companyUnitDOList.stream()
                .map(data -> parseNumber(data.getThermalOpenCapacity()).compareTo(BigDecimal.ZERO) == 0 ? 0f : parseNumber(data.getBiddingSpace()).divide(parseNumber(data.getThermalOpenCapacity()), 2, RoundingMode.HALF_UP).floatValue())
                .toList();
        Map<String, Object> map = new HashMap<>();
        map.put("biddingSpaceList", biddingSpaceList);
        map.put("loadForecastList", loadForecastList);
        map.put("hisUnifiedSettlementPointPriceList", hisUnifiedSettlementPointPriceList);
        map.put("openCapacityList", differences);
        return map;
    }

    /**
     * 现货价差 价差表现
     *
     * @param date 开始结束时间
     * @return
     */
    @Override
    public Map<String, Object> getNodePriceDifference(Long[] date) {
        String companyId = this.getCurrentUserOrgCode();
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }
        List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getNodePriceDifference(companyId, timestamps[0], timestamps[timestamps.length - 1]);
        List<Map<String, Object>> tableList = new ArrayList<>();
        //表格数据
        for (long startTime = date[0]; startTime <= date[1]; startTime = startTime + 86400000L) {
            Date start = new Date(startTime);
            Date end = new Date(startTime + 24 * 60 * 60 * 1000L);
            List<CompanyUnitDO> collect = companyUnitDOList.stream().filter(item -> item.getTs().getTime() >= start.getTime() && item.getTs().getTime() < end.getTime()).toList();
            Map<String, Object> differenceMap = new LinkedHashMap<>();
            differenceMap.put("date", start.getTime());
            Map<String, Double> collectHis = collect.stream()
                    .collect(Collectors.groupingBy(
                            item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                            Collectors.averagingDouble(item -> parseNumber(item.getHisUnifiedSettlementPointPrice()).doubleValue()) // 计算每组的和
                    ));
            Map<String, Double> collectReal = collect.stream()
                    .collect(Collectors.groupingBy(
                            item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                            Collectors.averagingDouble(item -> parseNumber(item.getRealUnifiedSettlementPointPrice()).doubleValue()) // 计算每组的和
                    ));
            for (String s : str) {
                differenceMap.put(s, BigDecimal.valueOf(collectHis.get(s) == null ? 0 : collectHis.get(s)).subtract(BigDecimal.valueOf(collectReal.get(s) == null ? 0 : collectReal.get(s))).setScale(2, RoundingMode.HALF_UP));
            }
            tableList.add(differenceMap);
        }
        Map<String, Object> avg = new LinkedHashMap<>();
        avg.put("title", "日平均");
        Map<String, Object> zheng = new LinkedHashMap<>();
        zheng.put("title", "正价差比例");
        Map<String, Object> zero = new LinkedHashMap<>();
        zero.put("title", "0价差比例");
        Map<String, Object> fu = new LinkedHashMap<>();
        fu.put("title", "负价差比例");
        for (Map<String, Object> map : tableList) {
            for (String s : str) {
                if (Double.parseDouble(String.valueOf(map.get(s))) > 0d) {
                    zheng.put(s, new BigDecimal(zheng.get(s) == null ? "0" : zheng.get(s).toString()).add(BigDecimal.valueOf(1)).doubleValue());
                } else if (Double.parseDouble(String.valueOf(map.get(s))) == 0d) {
                    zero.put(s, new BigDecimal(zero.get(s) == null ? "0" : zero.get(s).toString()).add(BigDecimal.valueOf(1)).doubleValue());
                } else {
                    fu.put(s, new BigDecimal(fu.get(s) == null ? "0" : fu.get(s).toString()).add(BigDecimal.valueOf(1)).doubleValue());
                }
                BigDecimal cal = new BigDecimal(avg.get(s) == null ? "0" : avg.get(s).toString()).add(BigDecimal.valueOf(Double.parseDouble(String.valueOf(map.get(s)))));
                avg.put(s, cal.setScale(2, RoundingMode.HALF_UP).doubleValue());
            }
        }
        for (String s : str) {
            avg.put(s, new BigDecimal(avg.get(s) == null ? "0" : avg.get(s).toString()).divide(BigDecimal.valueOf(tableList.size()), 2, RoundingMode.HALF_UP).doubleValue());
        }
        for (String s : str) {
            zheng.put(s, zheng.get(s) == null ? 0 : new BigDecimal(zheng.get(s).toString()).divide(BigDecimal.valueOf(tableList.size()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            zero.put(s, zero.get(s) == null ? 0 : new BigDecimal(zero.get(s).toString()).divide(BigDecimal.valueOf(tableList.size()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            fu.put(s, fu.get(s) == null ? 0 : new BigDecimal(fu.get(s).toString()).divide(BigDecimal.valueOf(tableList.size()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
        }
        return Map.of("tableList", tableList,
                "avg", avg.entrySet().stream().sorted(Map.Entry.comparingByKey(Comparator.naturalOrder()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                                (oldValue, newValue) -> oldValue, LinkedHashMap::new))
                , "zheng", zheng.entrySet().stream().sorted(Map.Entry.comparingByKey(Comparator.naturalOrder()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                                (oldValue, newValue) -> oldValue, LinkedHashMap::new)),
                "zero", zero.entrySet().stream().sorted(Map.Entry.comparingByKey(Comparator.naturalOrder()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                                (oldValue, newValue) -> oldValue, LinkedHashMap::new)),
                "fu", fu.entrySet().stream().sorted(Map.Entry.comparingByKey(Comparator.naturalOrder()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                                (oldValue, newValue) -> oldValue, LinkedHashMap::new)));
    }

    /**
     * 现货价差-价差解释
     *
     * @param date 日期
     * @return
     */
    @Override
    public Map<String, Object> getPriceDifferenceExplanation(Long[] date) {
        String companyId = this.getCurrentUserOrgCode();
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }
        List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getPriceDifferenceExplanation(companyId, timestamps[0], timestamps[timestamps.length - 1]);
        // 按小时分组并计算平均值
        //日前价格
        Map<String, Double> collectHis = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.averagingDouble(item -> parseNumber(item.getHisUnifiedSettlementPointPrice()).doubleValue()) // 计算每组的和
                ));
        //实时价格
        Map<String, Double> collectReal = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.averagingDouble(item -> parseNumber(item.getRealUnifiedSettlementPointPrice()).doubleValue()) // 计算每组的和
                ));
        //日前风电
        Map<String, Double> collectHisWind = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> parseNumber(item.getHisWindPower()).doubleValue()) // 计算每组的和
                ));
        //实时风电
        Map<String, Double> collectRealWind = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> parseNumber(item.getRealWindPower()).doubleValue()) // 计算每组的和
                ));
        //日前光电
        Map<String, Double> collectHisP = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> parseNumber(item.getHisPhotovoltaicPower()).doubleValue()) // 计算每组的和
                ));
        //实时光电
        Map<String, Double> collectRealP = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> parseNumber(item.getRealPhotovoltaicPower()).doubleValue()) // 计算每组的和
                ));
        //日前负载率
        Map<String, Double> collectHisFZL = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(CompanyUnitDO::getHisAverageLoadRate) // 计算每组的和
                ));
        //实时负载率
        Map<String, Double> collectFZL = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(CompanyUnitDO::getAverageLoadRate) // 计算每组的和
                ));
        //日前省调负荷
        Map<String, Double> collectHisFH = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> parseNumber(item.getLoadSystemForecastShortTerm()).doubleValue()) // 计算每组的和
                ));
        //实时省调负荷
        Map<String, Double> collectRealFH = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> parseNumber(item.getRealLoad()).doubleValue()) // 计算每组的和
                ));
        //日前竞价空间
        Map<String, Double> collectHisBiddingSpace = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> item.getHisBiddingSpaceSubtractWaisong().doubleValue()) // 计算每组的和
                ));
        //实时竞价空间
        Map<String, Double> collectRealBiddingSpace = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> item.getRealBiddingSpaceSubtractWaisong().doubleValue()) // 计算每组的和
                ));
        //日前实时价差
        List<Double> otherDay = new ArrayList<>();
        //风电偏差
        List<Double> windPower = new ArrayList<>();
        //光电偏差
        List<Double> photoElectricity = new ArrayList<>();
        //负荷偏差
        List<Double> load = new ArrayList<>();
        //竞价空间偏差=负荷偏差+风电偏差+光电偏差
        List<Double> biddingSpace = new ArrayList<>();
        //日前负载率=日前火电出力/日前火电开机容量
        List<Double> hisLoadFactor = new ArrayList<>();
        //实时负载率=实时火电出力/实时火电开机容量
        List<Double> realLoadFactor = new ArrayList<>();
        for (String s : str) {
            //日前实时价差 日前-实时
            BigDecimal other = BigDecimal.valueOf(collectHis.get(s) == null ? 0 : collectHis.get(s)).subtract(BigDecimal.valueOf(collectReal.get(s) == null ? 0 : collectReal.get(s))).setScale(2, RoundingMode.HALF_UP);
            //风电偏差 日前-实时
            BigDecimal wind = BigDecimal.valueOf(collectHisWind.get(s) == null ? 0 : collectHisWind.get(s)).subtract(BigDecimal.valueOf(collectRealWind.get(s) == null ? 0 : collectRealWind.get(s))).setScale(2, RoundingMode.HALF_UP);
            //光电偏差 日前-实时
            BigDecimal p = BigDecimal.valueOf(collectHisP.get(s) == null ? 0 : collectHisP.get(s)).subtract(BigDecimal.valueOf(collectRealP.get(s) == null ? 0 : collectRealP.get(s))).setScale(2, RoundingMode.HALF_UP);
            //负荷偏差 日前-实时
            BigDecimal l = BigDecimal.valueOf(collectHisFH.get(s) == null ? 0 : collectHisFH.get(s)).subtract(BigDecimal.valueOf(collectRealFH.get(s) == null ? 0 : collectRealFH.get(s))).setScale(2, RoundingMode.HALF_UP);
            //竞价空间偏差 日前-实时
            BigDecimal bidding = BigDecimal.valueOf(collectHisBiddingSpace.get(s) == null ? 0 : collectHisBiddingSpace.get(s)).subtract(BigDecimal.valueOf(collectRealBiddingSpace.get(s) == null ? 0 : collectRealBiddingSpace.get(s))).setScale(2, RoundingMode.HALF_UP);
            otherDay.add(other.doubleValue());
            windPower.add(wind.doubleValue());
            photoElectricity.add(p.doubleValue());
            load.add(l.doubleValue());
            biddingSpace.add(bidding.doubleValue());
            hisLoadFactor.add(BigDecimal.valueOf(collectHisFZL.get(s) == null ? 0 : collectHisFZL.get(s)).setScale(2, RoundingMode.HALF_UP).doubleValue());
            realLoadFactor.add(BigDecimal.valueOf(collectFZL.get(s) == null ? 0 : collectFZL.get(s)).setScale(2, RoundingMode.HALF_UP).doubleValue());
        }

        return Map.of("otherDay", otherDay,
                "windPower", windPower,
                "photoElectricity", photoElectricity,
                "load", load,
                "biddingSpace", biddingSpace,
                "hisLoadFactor", hisLoadFactor,
                "realLoadFactor", realLoadFactor);
    }

    /**
     * 现货价差-价差原因
     *
     * @param date 日期
     * @return
     */
    @Override
    public List<Map<String, Double>> getPriceDifferenceReason(Long[] date) {
        String companyId = this.getCurrentUserOrgCode();
        date[1] = date[1] + 86400000L - 1000;
        Timestamp[] timestamps = new Timestamp[date.length];
        for (int i = 0; i < date.length; i++) {
            timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
        }
        List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getPriceDifferenceReason(companyId, timestamps[0], timestamps[timestamps.length - 1]);
        // 按小时分组并计算平均值
        //日前-实时差价
        Map<String, Double> collectHis = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.averagingDouble(item -> parseNumber(item.getHisUnifiedSettlementPointPrice()).doubleValue()) // 计算每组的和
                ));
        Map<String, Double> collectReal = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 90000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.averagingDouble(item -> parseNumber(item.getRealUnifiedSettlementPointPrice()).doubleValue()) // 计算每组的和
                ));
        //新能源风实际-日前
        Map<String, Double> collectHisWind = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> parseNumber(item.getHisWindPower()).doubleValue()) // 计算每组的和
                ));
        Map<String, Double> collectRealWind = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> parseNumber(item.getRealWindPower()).doubleValue()) // 计算每组的和
                ));
        //新能源光实际-日前
        Map<String, Double> collectHisP = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> parseNumber(item.getHisPhotovoltaicPower()).doubleValue()) // 计算每组的和
                ));
        Map<String, Double> collectRealP = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> parseNumber(item.getRealPhotovoltaicPower()).doubleValue()) // 计算每组的和
                ));
        // 负荷预测(省调负荷日前)-直调用电  没有直调用电 不要了表格里取消了
        /*Map<String, Double> collectB59 = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime()), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(CompanyUnitDO::getSystemForecastB59) // 计算每组的和
                ));*/
        //竞价空间日前-实际
        Map<String, Double> collectBiddingSpace = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> item.getHisBiddingSpace().doubleValue()) // 计算每组的和
                ));
        //竞价空间实际=省调负荷实际+(外送电实际-受电负荷实际=五个联络线的和)-(新能源负荷实际=实时风电+实时光电)；
        //竞价空间实际
        Map<String, Double> collectRealBiddingSpace = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(CompanyUnitDO::getRealBiddingSpace) // 计算每组的和
                ));
        // 竞价空间日前-实际的值-外送
        //外送实际
        Map<String, Double> collectRealWaiSong = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(CompanyUnitDO::getRealWaiSong) // 计算每组的和
                ));
        //日前外送
        Map<String, Double> collectHisWaisong = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> item.getHisWaiSong().doubleValue()) // 计算每组的和
                ));
        //竞价空间日前(不包含外送)
        Map<String, Double> collectBiddingSpaceSubtractWaisong = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(item -> item.getHisBiddingSpaceSubtractWaisong().doubleValue()) // 计算每组的和
                ));
        //竞价空间实时(不包含外送)
        Map<String, Double> collectRealBiddingSpaceSubtractWaisong = companyUnitDOList.stream()
                .collect(Collectors.groupingBy(
                        item -> new BigDecimal(DateUtil.format(new Date(item.getTs().getTime() - 900000), "HH")).add(BigDecimal.valueOf(1)).toString(), // 按小时分组
                        Collectors.summingDouble(CompanyUnitDO::getRealBiddingSpaceSubtractWaisong) // 计算每组的和
                ));
        List<Map<String, Double>> listMap = new ArrayList<>();
        for (String s : str) {
            Map<String, Double> dataMap = new HashMap<>();
            dataMap.put("time", Double.valueOf(s));
            dataMap.put("otherdayprice", BigDecimal.valueOf(collectHis.get(s)).subtract(BigDecimal.valueOf(collectReal.get(s))).setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataMap.put("biddingspace-all", BigDecimal.valueOf(collectBiddingSpace.get(s)).subtract(BigDecimal.valueOf(collectRealBiddingSpace.get(s))).setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataMap.put("biddingspace-part", BigDecimal.valueOf(collectBiddingSpaceSubtractWaisong.get(s)).subtract(BigDecimal.valueOf(collectRealBiddingSpaceSubtractWaisong.get(s))).setScale(2, RoundingMode.HALF_UP).doubleValue());
            /* dataMap.put("loadforecasting", BigDecimal.valueOf(collectB59.get(s) == null ? 0 : collectB59.get(s)).subtract(BigDecimal.valueOf(i)).setScale(2, RoundingMode.HALF_UP).doubleValue());*/
            BigDecimal real = BigDecimal.valueOf(collectRealWind.get(s)).add(BigDecimal.valueOf(collectRealP.get(s)));
            BigDecimal his = BigDecimal.valueOf(collectHisWind.get(s)).add(BigDecimal.valueOf(collectHisP.get(s)));
            dataMap.put("newenergy", real.subtract(his).doubleValue());
            dataMap.put("newenergy-light", BigDecimal.valueOf(collectRealP.get(s)).subtract(BigDecimal.valueOf(collectHisP.get(s))).setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataMap.put("newenergy-wind", BigDecimal.valueOf(collectRealWind.get(s)).subtract(BigDecimal.valueOf(collectHisWind.get(s))).setScale(2, RoundingMode.HALF_UP).doubleValue());
            dataMap.put("deliveryoutside", BigDecimal.valueOf(collectHisWaisong.get(s)).subtract(BigDecimal.valueOf(collectRealWaiSong.get(s))).setScale(2, RoundingMode.HALF_UP).doubleValue());
            listMap.add(dataMap);
        }
        return listMap;
    }

    /**
     * 市场动态 新能源总出力
     *
     * @param date
     * @return
     */
    @Override
    public Map<String, List<String>> getTotalOutputOfNewEnergy(Long[] date) {
        String companyId = this.getCurrentUserOrgCode();
        if (date.length > 0) {
            date[1] = date[1] + 86400000L - 1000;
            Timestamp[] timestamps = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getTotalOutputOfNewEnergy(companyId, timestamps[0], timestamps[timestamps.length - 1]);
            List<String> realWindPowerList = new ArrayList<>();
            List<String> realPhotovoltaicPowerList = new ArrayList<>();
            List<String> sum = new ArrayList<>();
            List<String> hisWindPowerList = new ArrayList<>();
            List<String> hisPhotovoltaicPowerList = new ArrayList<>();
            List<String> hisSum = new ArrayList<>();
            for (CompanyUnitDO c : companyUnitDOList) {
                realWindPowerList.add(c.getRealWindPower());
                realPhotovoltaicPowerList.add(c.getRealPhotovoltaicPower());
                hisWindPowerList.add(c.getHisWindPower());
                hisPhotovoltaicPowerList.add(c.getHisPhotovoltaicPower());
                sum.add(parseNumber(c.getRealWindPower()).add(parseNumber(c.getRealPhotovoltaicPower())).toString());
                hisSum.add(parseNumber(c.getHisWindPower()).add(parseNumber(c.getHisPhotovoltaicPower())).toString());
            }
            return Map.of("realWindPower", realWindPowerList, "realPhotovoltaicPower", realPhotovoltaicPowerList, "sum", sum, "hisWindPower", hisWindPowerList, "hisPhotovoltaicPower", hisPhotovoltaicPowerList, "hisSum", hisSum);
        } else {
            return Map.of();
        }
    }

    /**
     * 市场动态 实时负荷及系统备用
     *
     * @param date
     * @return
     */
    @Override
    public Map<String, List<String>> getUpwardRotation(Long[] date, String dataSelectTypeOption) {
        String companyId = this.getCurrentUserOrgCode();
        if (date.length > 0) {
            date[1] = date[1] + 86400000L - 1000;
            Timestamp[] timestamps = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getUpwardRotation(companyId, timestamps[0], timestamps[timestamps.length - 1]);
            if (dataSelectTypeOption.equals("0")) {
                //实时频率
                List<String> realFrequencyList = companyUnitDOList.stream().map(m -> m.getRealFrequency()).toList();
                return Map.of("realFrequencyList", realFrequencyList);
            } else if (dataSelectTypeOption.equals("1")) {
                //省水火
                List<String> provincialThermalPowerUpwardRotationList = companyUnitDOList.stream().map(m -> m.getProvincialThermalPowerUpwardRotation()).toList();
                List<String> hydroelectricPowerUpwardRotationList = companyUnitDOList.stream().map(m -> m.getHydroelectricPowerUpwardRotation()).toList();
                return Map.of("provincialThermalPowerUpwardRotationList", provincialThermalPowerUpwardRotationList, "hydroelectricPowerUpwardRotationList", hydroelectricPowerUpwardRotationList);
            } else {
                //负荷
                List<String> realLoadList = companyUnitDOList.stream().map(m -> m.getRealLoad()).toList();
                return Map.of("realLoadList", realLoadList);
            }
        } else {
            return Map.of();
        }
    }

    /**
     * 市场动态 日前系统间联络线总输电曲线预测
     *
     * @param date
     * @return
     */
    @Override
    public Map<String, List<Float>> getLlx(Long[] date, String dataSelectTypeOption) {
        String companyId = this.getCurrentUserOrgCode();
        if (date.length > 0) {
            date[1] = date[1] + 86400000L - 1000;
            Timestamp[] timestamps = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getLlx(companyId, timestamps[0], timestamps[timestamps.length - 1]);
            if (dataSelectTypeOption.equals("0")) {
                //省间联络线输电曲线预测
                List<Float> tieLineForecastList = companyUnitDOList.stream().map(m -> m.getTieLineForecast()).toList();
                return Map.of("hisHN", tieLineForecastList);
            } else if (dataSelectTypeOption.equals("1")) {
                //省间联络线输电情况
                List<Float> tieLineForecastList = companyUnitDOList.stream().map(m -> m.getTieLine()).toList();
                return Map.of("realHN", tieLineForecastList);
            } else {
                //省间联络线输电曲线预测日内
                List<Float> tieLineForecastList = companyUnitDOList.stream().map(m -> m.getRealTieLine()).toList();
                return Map.of("realT", tieLineForecastList);
            }
        } else {
            return Map.of();
        }
    }

    @Override
    public List<DispatchNodeDO> getDispatchNodeList() {
        //当前用户的orgcode
        String companyId = this.getCurrentGroupOrgCode();
        //当前用户所属集团的orgcode
        String bloc = this.getCurrentUserOrgCode();
        //判断当前用户是否是集团
        List<DispatchNodeDO> dispatchNodeDOS = new ArrayList<>();
        if (bloc.equals(companyId)) {
            //获取该集团下所有公司及公司所属的交易单元
            List<PowerStationDO> powerStationDOS = powerStationService.getPowerStationByOrgCode(companyId);
            if (!powerStationDOS.isEmpty()) {
                List<String> ids = powerStationDOS.stream().map(PowerStationDO::getCode).toList();
                LambdaQueryWrapper<DispatchNodeDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(DispatchNodeDO::getOrgCode, ids);
                dispatchNodeDOS = dispatchNodeMapper.selectList(queryWrapper);
            }
        } else {
            //获取该公司下所有交易单元
            LambdaQueryWrapper<DispatchNodeDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DispatchNodeDO::getOrgCode, companyId);
            dispatchNodeDOS = dispatchNodeMapper.selectList(queryWrapper);
        }
        return dispatchNodeDOS;
    }

    /**
     * 获取区域公司id及所有的交易单元id
     *
     * @return
     */
    @Override
    public String getCurrentGroupOrgCode() {
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        DeptRespDTO deptRespDTO = deptApi.getDept(deptId);
        return deptRespDTO.getOrgCode();
    }

    /**
     * 获取当前租户所属的集团id
     *
     * @return
     */
    @Override
    public String getCurrentUserOrgCode() {
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        DeptRespDTO deptRespDTO = deptApi.getDept(deptId);
        DeptRespDTO d = deptRespDTO;
        if (d != null) {
            if (deptRespDTO.getParentId() != 0) {
                d = deptApi.getDept(deptRespDTO.getParentId());
            }
            while (deptRespDTO.getParentId() != 0) {
                if (d.getParentId() == 0) {
                    break;
                } else {
                    d = deptApi.getDept(d.getParentId());
                }
            }
            return d.getOrgCode();
        } else {
            return null;
        }
    }

    @Override
    public Long getTenantId() {
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        DeptRespDTO deptRespDTO = deptApi.getDept(deptId);
        return deptRespDTO.getTenantId();
    }

    @Override
    public String getCurrentUser() {
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        DeptRespDTO deptRespDTO = deptApi.getDept(deptId);
        return tenantApi.getTradingMarketCode(deptRespDTO.getTenantId());
    }

    @Override
    public HashMap<String, Object> getDongLiMeiPrice(HashMap<String, Object> resultMap, String companyId, String[] timeArr) {
        //动力煤地区设置
        String diqu = "";
        if (companyId.contains("ZJN")) {
            //中节能使用兰州
            diqu = "兰州";
        } else if (companyId.contains("dt")) {
            diqu = "赤峰";
        }
        List<ThermalCoalPrice> thermalCoalPriceList = jyThermalCoalPriceMapper.getJyThermalCoalPrice("沫煤", diqu, timeArr[0], timeArr[1]);

        Map<String, ThermalCoalPrice> thermalCoalPriceMap = thermalCoalPriceList.stream().collect(Collectors.toMap(x -> x.getBizDate() + "", x -> x, (x1, x2) -> x1));
        //按时间正序排列
        thermalCoalPriceMap = MapSortUtils.sortByKey(thermalCoalPriceMap, false);

        List<String> dongLiMeiDayLine = new ArrayList<>();
        List<Float> dongLiMeiPriceLine = new ArrayList<>();

        // 定义起始日期和结束日期

        LocalDate startDate = LocalDate.parse(timeArr[0]);
        LocalDate endDate = LocalDate.parse(timeArr[1]);

        // 按天遍历日期
        for (LocalDate date = startDate; date.isBefore(endDate.plusDays(1)); date = date.plusDays(1)) {
            ThermalCoalPrice thermalCoalPrice = thermalCoalPriceMap.get(date.toString());
            dongLiMeiDayLine.add(date.toString());
            if (null != thermalCoalPrice) {
                dongLiMeiPriceLine.add(thermalCoalPrice.getPriceForPage().floatValue());
            } else {
                dongLiMeiPriceLine.add(0f);
            }

        }

        resultMap.put("dongLiMeiDayLine", dongLiMeiDayLine);
        resultMap.put("dongLiMeiPriceLine", dongLiMeiPriceLine);
        return resultMap;
    }

    /**
     * 获取必停必开机组表格(市场概览)
     *
     * @param resultMap
     * @param companyId
     * @param timeArr
     * @return
     */
    @Override
    public HashMap<String, Object> getMustOpenAndStopUnit(HashMap<String, Object> resultMap, String companyId, String[] timeArr) {
        List<StartupAndDownUnitDO> mustOpenAndStopUnit = mustOpenAndStopUnitMapper.getMustOpenAndStopUnit(timeArr[0], timeArr[1]);
        // 按日期分组
        Map<String, List<StartupAndDownUnitDO>> grouped = mustOpenAndStopUnit.stream()
                .collect(Collectors.groupingBy(x -> ((LocalDateTime) x.getTs()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
        ArrayList<Map> objects = new ArrayList<>();
        grouped.forEach((date, list) -> {
            int open = list.stream().mapToInt(x -> Integer.parseInt(x.getHeatAdditionMustOpenUnitNumber()) + Integer.parseInt(x.getNonHeatAdditionMustOpenUnitNumber())).sum();
            int close = list.stream().mapToInt(x -> Integer.parseInt(x.getHeatAdditionMustStopUnitNumber()) + Integer.parseInt(x.getNonHeatAdditionMustStopUnitNumber())).sum();
            HashMap<String, Object> map = new HashMap<>();
            map.put("date", date);
            map.put("open", open);
            map.put("close", close);
            objects.add(map);
        });
        objects.sort((o1, o2) -> {
            String dateStr1 = (String) o1.get("date");
            String dateStr2 = (String) o2.get("date");
            LocalDate date1 = LocalDate.parse(dateStr1, DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDate date2 = LocalDate.parse(dateStr2, DateTimeFormatter.ISO_LOCAL_DATE);
            return date1.compareTo(date2);
        });
        resultMap.put("mustOpenAndStopUnitDayLine", objects);
        return resultMap;
    }

    /**
     * 现货价差-价差分布
     *
     * @param date
     * @return
     */
    @Override
    public Map<String, Object> getPriceDifferenceDistribution(Long[] date) {
        String companyId = this.getCurrentUserOrgCode();
        if (date.length > 0) {
            date[1] = date[1] + 86400000L - 1000;
            Timestamp[] timestamps = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getNodePriceDifference(companyId, timestamps[0], timestamps[timestamps.length - 1]);
            //根据时分分组
            Map<String, List<CompanyUnitDO>> groupedMap = companyUnitDOList.stream()
                    .collect(Collectors.groupingBy(
                            companyUnitDO -> {
                                LocalDateTime localDateTime = companyUnitDO.getTs().toLocalDateTime().minusMinutes(15);
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                                return localDateTime.format(formatter);
                            }
                    ));
            //排序 升序
            Map<String, List<CompanyUnitDO>> sortedMap = groupedMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey(Comparator.naturalOrder()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

            List<List<Double>> boxplotList = new ArrayList<>();
            Map<String, List<Map<String, Object>>> mapListMap = new LinkedHashMap<>();
            List<Map<String, Object>> mapList = new ArrayList<>();
            List<Double> lineList = new ArrayList<>();
            sortedMap.forEach((key, value) -> {
                List<Double> priceDifferenceList = value.stream()
                        .map(companyUnitDO -> parseNumber(companyUnitDO.getHisUnifiedSettlementPointPrice()).subtract(parseNumber(companyUnitDO.getRealUnifiedSettlementPointPrice())).doubleValue())
                        .toList();
                //每个点的平均值
                lineList.add(BigDecimal.valueOf(priceDifferenceList.stream().mapToDouble(Double::doubleValue).sum()).divide(BigDecimal.valueOf(priceDifferenceList.size()), 2, RoundingMode.HALF_UP).doubleValue());
                double[] result = new double[priceDifferenceList.size()];
                for (int i = 0; i < priceDifferenceList.size(); i++) {
                    result[i] = priceDifferenceList.get(i);
                }
                double[] boxplot = plot(result);
                //盒须图数据
                boxplotList.add(Arrays.stream(boxplot).boxed().toList());
                //异常值数据
                //结构为 'scatter': {
                //      '2024-10-1': [{time: '00:15', value: 1230}, {time: '00:15', value: 1000}, {
                //        time: '20:15',
                //        value: -120
                //      }],
                //      '2024-10-4': [{time: '01:15', value: 230}, {time: '16:00', value: -120}],
                //    }
                String[] parts = key.split(":");
                int[] targetHourMinute = new int[]{Integer.parseInt(parts[0]), Integer.parseInt(parts[1])};
                List<CompanyUnitDO> collect = companyUnitDOList.stream()
                        .filter(companyUnitDO -> {
                            LocalDateTime localDateTime = companyUnitDO.getTs().toLocalDateTime();
                            int hour = localDateTime.getHour();
                            int minute = localDateTime.getMinute();
                            return hour == targetHourMinute[0] && minute == targetHourMinute[1];
                        })
                        .toList();
                for (CompanyUnitDO c : collect) {
                    double cal = parseNumber(c.getHisUnifiedSettlementPointPrice()).subtract(parseNumber(c.getRealUnifiedSettlementPointPrice())).doubleValue();
                    if (cal > boxplot[boxplot.length - 1] || cal < boxplot[0]) {
                        Map<String, Object> outliersMap = new HashMap<>();
                        LocalDateTime localDateTime = c.getTs().toLocalDateTime();
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        outliersMap.put("time", localDateTime.format(formatter));
                        outliersMap.put("value", BigDecimal.valueOf(cal).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        if (mapListMap.get(localDateTime.format(formatter1)) == null) {
                            List<Map<String, Object>> outliersList = new ArrayList<>();
                            outliersList.add(outliersMap);
                            mapListMap.put(localDateTime.format(formatter1), outliersList);
                        } else {
                            mapListMap.get(localDateTime.format(formatter1)).add(outliersMap);
                        }
                    }
                }
            });
            Map<Integer, Float> resultMap = companyUnitDOList.stream()
                    .collect(Collectors.groupingBy(
                            CompanyUnitDO -> CompanyUnitDO.getTs().toLocalDateTime().minusMinutes(15).getHour(),
                            Collectors.mapping(CompanyUnitDO -> parseNumber(CompanyUnitDO.getHisUnifiedSettlementPointPrice()).subtract(parseNumber(CompanyUnitDO.getRealUnifiedSettlementPointPrice())).floatValue(), Collectors.toList())
                    ))
                    .entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().stream().reduce(0f, Float::sum)
                    ));
            resultMap.forEach((key, value) -> {
                Map<String, Object> map = new HashMap<>();
                map.put("time", key + 1);
                //价差平均值
                map.put("pricediff", new BigDecimal(value).divide(new BigDecimal(companyUnitDOList.size() / 96), 2, RoundingMode.HALF_UP).divide(new BigDecimal(4), 2, RoundingMode.HALF_UP).doubleValue());
                mapList.add(map);
            });
            //--------------------------峰谷平计算-----------------------------
            //当前租户该省份的所有峰谷平时段
            List<TimeSlotsDO> timeSlotsDOList = timeSlotsService.getListAll(tenantService.getTenant(SecurityFrameworkUtils.getLoginUserTenantId()).getTradingMarketCode());
            List<TimeSlotsDO> offPeak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("off-peak")).toList();
            List<TimeSlotsDO> valley = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("valley")).toList();
            List<TimeSlotsDO> peak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("peak")).toList();
            BigDecimal peakSum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : peak) {
                peakSum = peakSum.add(BigDecimal.valueOf(resultMap.get(timeSlotsDO.getStartTime().getHour())));
            }
            BigDecimal valleySum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : valley) {
                valleySum = valleySum.add(BigDecimal.valueOf(resultMap.get(timeSlotsDO.getStartTime().getHour())));
            }
            BigDecimal offPeakSum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : offPeak) {
                offPeakSum = offPeakSum.add(BigDecimal.valueOf(resultMap.get(timeSlotsDO.getStartTime().getHour())));
            }
            // 峰段计算
            double v1 = 0.0;
            if (peak.size() > 0 && companyUnitDOList.size() > 0) {
                BigDecimal denominator = BigDecimal.valueOf(peak.size() * 4L * (companyUnitDOList.size() / 96));
                if (denominator.compareTo(BigDecimal.ZERO) > 0) {
                    v1 = peakSum.divide(denominator, 2, RoundingMode.HALF_UP).doubleValue();
                }
            }

            // 平段计算
            double v2 = 0.0;
            if (offPeak.size() > 0 && companyUnitDOList.size() > 0) {
                BigDecimal denominator = BigDecimal.valueOf(offPeak.size() * 4L * (companyUnitDOList.size() / 96));
                if (denominator.compareTo(BigDecimal.ZERO) > 0) {
                    v2 = offPeakSum.divide(denominator, 2, RoundingMode.HALF_UP).doubleValue();
                }
            }

            // 谷段计算
            double v3 = 0.0;
            if (valley.size() > 0 && companyUnitDOList.size() > 0) {
                BigDecimal denominator = BigDecimal.valueOf(valley.size() * 4L * (companyUnitDOList.size() / 96));
                if (denominator.compareTo(BigDecimal.ZERO) > 0) {
                    v3 = valleySum.divide(denominator, 2, RoundingMode.HALF_UP).doubleValue();
                }
            }
//            double v1 = peakSum.divide(BigDecimal.valueOf(peak.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP).doubleValue();
//            double v2 = offPeakSum.divide(BigDecimal.valueOf(offPeak.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP).doubleValue();
//            double v3 = valleySum.divide(BigDecimal.valueOf(valley.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP).doubleValue();
            mapList.add(Map.of("time", "峰段", "pricediff", v1));
            mapList.add(Map.of("time", "谷段", "pricediff", v3));
            mapList.add(Map.of("time", "平段", "pricediff", v2));
            //------------------------------峰谷平计算结束--------------------------
            return Map.of("boxplot", boxplotList, "scatter", mapListMap, "line", lineList, "statisticsAvgData", mapList, "slots", timeSlotsDOList);
        }
        return Map.of("boxplot", "", "scatter", "", "line", "", "statisticsAvgData", "", "slots", "");
    }

    @Override
    public IPage<StartupAndDownUnitDO> getBiKaiBiTing(CompanyUnitRespVO companyUnitRespVO) {
        IPage<StartupAndDownUnitDO> result = mustOpenAndStopUnitMapper.getBiKaiBiTing(new Page<StartupAndDownUnitDO>(companyUnitRespVO.getCurrentPage(), companyUnitRespVO.getPageSize()), companyUnitRespVO);
        return result;
    }

    /**
     * 分区价格
     *
     * @param date                 时间
     * @param dataSelectTypeOption 0是日前价格 1是实时价格
     * @return
     */
    @Override
    public List<List<Object>> getPartitionPrice(Long[] date, String dataSelectTypeOption) {
        String companyId = this.getCurrentUserOrgCode();
        List<List<Object>> list = new ArrayList<>();
        if (date.length > 0) {
            date[1] = date[1] + 86400000L - 1000;
            Timestamp[] timestamps = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getPartitionPrice(companyId, timestamps[0], timestamps[timestamps.length - 1]);
            //当前租户该省份的所有峰谷平时段
            List<TimeSlotsDO> timeSlotsDOList = timeSlotsService.getListAll(tenantService.getTenant(SecurityFrameworkUtils.getLoginUserTenantId()).getTradingMarketCode());
            List<TimeSlotsDO> offPeak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("off-peak")).toList();
            List<TimeSlotsDO> valley = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("valley")).toList();
            List<TimeSlotsDO> peak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("peak")).toList();
            Map<String, Double> hdGroupByHour;
            Map<String, Double> hxGroupByHour;
            if ("0".equals(dataSelectTypeOption)) {
                hdGroupByHour = companyUnitDOList.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getHisUnifiedSettlementPointPartitionPricesHd()).doubleValue())));
                hxGroupByHour = companyUnitDOList.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getHisUnifiedSettlementPointPartitionPricesHx()).doubleValue())));
            } else {
                hdGroupByHour = companyUnitDOList.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getRealUnifiedSettlementPointPartitionPricesHd()).doubleValue())));
                hxGroupByHour = companyUnitDOList.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getRealUnifiedSettlementPointPartitionPricesHx()).doubleValue())));
            }
            //河东的峰谷平
            BigDecimal hdPeakSum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : peak) {
                hdPeakSum = hdPeakSum.add(BigDecimal.valueOf(hdGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            BigDecimal hdValleySum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : valley) {
                hdValleySum = hdValleySum.add(BigDecimal.valueOf(hdGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            BigDecimal hdOffPeakSum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : offPeak) {
                hdOffPeakSum = hdOffPeakSum.add(BigDecimal.valueOf(hdGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            //河西的峰谷平
            BigDecimal hxPeakSum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : peak) {
                hxPeakSum = hxPeakSum.add(BigDecimal.valueOf(hxGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            BigDecimal hxValleySum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : valley) {
                hxValleySum = hxValleySum.add(BigDecimal.valueOf(hxGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            BigDecimal hxOffPeakSum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : offPeak) {
                hxOffPeakSum = hxOffPeakSum.add(BigDecimal.valueOf(hxGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            List<Object> hdList = new ArrayList<>();
            List<Object> hxList = new ArrayList<>();
            hdList.add("河东");
            hdList.add(hdPeakSum.divide(BigDecimal.valueOf(peak.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            hdList.add(hdValleySum.divide(BigDecimal.valueOf(valley.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            hdList.add(hdOffPeakSum.divide(BigDecimal.valueOf(offPeak.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            hxList.add("河西");
            hxList.add(hxPeakSum.divide(BigDecimal.valueOf(peak.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            hxList.add(hxValleySum.divide(BigDecimal.valueOf(valley.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            hxList.add(hdOffPeakSum.divide(BigDecimal.valueOf(offPeak.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            if ("0".equals(dataSelectTypeOption)) {
                hdList.addAll(companyUnitDOList.stream().map(CompanyUnitDO::getHisUnifiedSettlementPointPartitionPricesHd).toList());
                hxList.addAll(companyUnitDOList.stream().map(CompanyUnitDO::getHisUnifiedSettlementPointPartitionPricesHx).toList());
            } else {
                hdList.addAll(companyUnitDOList.stream().map(CompanyUnitDO::getRealUnifiedSettlementPointPartitionPricesHd).toList());
                hxList.addAll(companyUnitDOList.stream().map(CompanyUnitDO::getRealUnifiedSettlementPointPartitionPricesHx).toList());
            }
            list.add(hdList);
            list.add(hxList);
        }
        return list;
    }

    @Override
    public List<List<Object>> getSettlementPrice(Long[] date) {
        String companyId = this.getCurrentUserOrgCode();
        List<List<Object>> list = new ArrayList<>();
        if (date.length > 0) {
            date[1] = date[1] + 86400000L - 1000;
            Timestamp[] timestamps = new Timestamp[date.length];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            List<CompanyUnitDO> companyUnitDOList = companyUnitMapper.getSettlementPrice(companyId, timestamps[0], timestamps[timestamps.length - 1]);
            //当前租户该省份的所有峰谷平时段
            List<TimeSlotsDO> timeSlotsDOList = timeSlotsService.getListAll(tenantService.getTenant(SecurityFrameworkUtils.getLoginUserTenantId()).getTradingMarketCode());
            List<TimeSlotsDO> offPeak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("off-peak")).toList();
            List<TimeSlotsDO> valley = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("valley")).toList();
            List<TimeSlotsDO> peak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("peak")).toList();
            Map<String, Double> hisGroupByHour;
            hisGroupByHour = companyUnitDOList.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getHisUnifiedSettlementPointPrice()).doubleValue())));
            Map<String, Double> realGroupByHour;
            realGroupByHour = companyUnitDOList.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getRealUnifiedSettlementPointPrice()).doubleValue())));
            //日前结算点的峰谷平
            BigDecimal hisPeakSum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : peak) {
                hisPeakSum = hisPeakSum.add(BigDecimal.valueOf(hisGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            BigDecimal hisValleySum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : valley) {
                hisValleySum = hisValleySum.add(BigDecimal.valueOf(hisGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            BigDecimal hisOffPeakSum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : offPeak) {
                hisOffPeakSum = hisOffPeakSum.add(BigDecimal.valueOf(hisGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            //-----实时结算点的峰谷平
            BigDecimal realPeakSum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : peak) {
                realPeakSum = realPeakSum.add(BigDecimal.valueOf(realGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            BigDecimal realValleySum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : valley) {
                realValleySum = realValleySum.add(BigDecimal.valueOf(realGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            BigDecimal realOffPeakSum = BigDecimal.ZERO;
            for (TimeSlotsDO timeSlotsDO : offPeak) {
                realOffPeakSum = realOffPeakSum.add(BigDecimal.valueOf(realGroupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
            }
            List<Object> hisList = new ArrayList<>();
            List<Object> realList = new ArrayList<>();
            hisList.add("日前统一结算点价格");
            hisList.add(hisPeakSum.divide(BigDecimal.valueOf(peak.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            hisList.add(hisValleySum.divide(BigDecimal.valueOf(valley.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            hisList.add(hisOffPeakSum.divide(BigDecimal.valueOf(offPeak.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            realList.add("实时统一结算点价格");
            realList.add(realPeakSum.divide(BigDecimal.valueOf(peak.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            realList.add(realValleySum.divide(BigDecimal.valueOf(valley.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            realList.add(realOffPeakSum.divide(BigDecimal.valueOf(offPeak.size() * 4L * (companyUnitDOList.size() / 96)), 2, RoundingMode.HALF_UP));
            hisList.addAll(companyUnitDOList.stream().map(CompanyUnitDO::getHisUnifiedSettlementPointPrice).toList());
            realList.addAll(companyUnitDOList.stream().map(CompanyUnitDO::getRealUnifiedSettlementPointPrice).toList());
            list.add(hisList);
            list.add(realList);
        }
        return list;
    }

    @Override
    public HashMap<String, Object> getWeatherInfo(HashMap<String, Object> resultMap, Timestamp startTs, Timestamp endTs) {
        Map<String, List<String>> weatherInfoList = new HashMap<>();
        weatherInfoList.put("days", new ArrayList<>());
        weatherInfoList.put("weather", new ArrayList<>());
        weatherInfoList.put("night", new ArrayList<>());
        weatherInfoList.put("highTemp", new ArrayList<>());
        weatherInfoList.put("lowTemp", new ArrayList<>());
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        DeptRespDTO deptRespDTO = deptApi.getDept(deptId);
        String dispatchCode = "";
        if (null != deptRespDTO) {
            dispatchCode = deptRespDTO.getDistrictCode() + "";
        }
        LambdaQueryWrapper<DistrictCodeDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DistrictCodeDO::getCode, dispatchCode);
        List<DistrictCodeDO> districtCodeDOS = districtCodeMapper.selectList(queryWrapper);
        if (!districtCodeDOS.isEmpty()) {
            List<String> cityName = new ArrayList<>();
            cityName.add(districtCodeDOS.get(0).getName());
            weatherInfoList.put("cityName", cityName);
        }
        List<HeFengDay> heFengDayList = heFengDayMapper.selectListByDayAndAreaCode(dispatchCode, startTs, endTs);
        heFengDayList.forEach(x -> {
            weatherInfoList.get("days").add(DateUtil.format(DateUtil.date(x.getTs().getTime()), "MM-dd"));
            weatherInfoList.get("weather").add(x.getTextDay());
            weatherInfoList.get("night").add(x.getTextNight());
            weatherInfoList.get("highTemp").add(x.getTempMax() + "");
            weatherInfoList.get("lowTemp").add(x.getTempMin() + "");
        });
        resultMap.put("weatherInfoList", weatherInfoList);
        return resultMap;
    }

    @Override
    public Object getPowerPlantNames(CompanyUnitRespVO companyUnitRespVO) {
        return mustOpenAndStopUnitMapper.getPowerPlantNames();
    }

    /**
     * 日前或实时节点价格查询
     *
     * @param monthDate 月份
     * @param date      日期
     * @param realORHis 日前还是实时
     * @return
     */
    @Override
    public Map<String, Object> getUnifiedSettlementPointPrice(String monthDate, Long[] date, String realORHis) {
        String companyId = this.getCurrentUserOrgCode();
        List<CompanyUnitDO> companyUnitDOList;
        Arrays.sort(date);
        if (date.length == 0) {
            monthDate = monthDate + "-01 00:15:00";
            Timestamp startDate = Timestamp.valueOf(monthDate);
            // 使用 Calendar 进行时间操作
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(startDate.getTime());
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 获取下个月 1 号的时间戳
            Timestamp endDate = new Timestamp(calendar.getTimeInMillis());
            companyUnitDOList = companyUnitMapper.getSettlementPrice(companyId, startDate, endDate);
            companyUnitMapper.getSettlementPrice(companyId, startDate, endDate);
        } else {
            for (int i = 0; i < date.length; i++) {
                date[i] = date[i] + 900000;
            }
            Timestamp[] timestamps = new Timestamp[date.length + 1];
            for (int i = 0; i < date.length; i++) {
                timestamps[i] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[i])));
            }
            timestamps[date.length] = Timestamp.valueOf(DateUtil.formatDateTime(new Date(date[date.length - 1] + 60 * 60 * 24 * 1000L - 1000)));
            companyUnitDOList = companyUnitMapper.getSettlementPrice(companyId, timestamps[0], timestamps[timestamps.length - 1]);
        }
        //提取出每15分钟的数据
        List<CompanyUnitDO> extractList = extractFifteenMinuteData(companyUnitDOList);
        //筛选  如果按照日查询可能跳着查, 在写sql的时候使用的是最早的时间和最晚的时间范围查, 需要吧中间多查出来的数据删掉
        List<CompanyUnitDO> filteredData = new ArrayList<>();
        if (date.length == 0) {
            filteredData = extractList;
        } else {
            for (Long dayTimestamp : date) {
                long dayStart = dayTimestamp;
                long dayEnd = dayStart + 24 * 60 * 60 * 1000 - 1000;
                filteredData.addAll(extractList.stream().filter(unit -> unit.getTs().getTime() >= dayStart && unit.getTs().getTime() < dayEnd).toList());
            }
        }
        //根据数据分割出每天的数据     00:15-00:00
        //先根据时间分组然后排序
        Map<String, List<CompanyUnitDO>> result = sortMapByDate(groupDataByDate(filteredData));
        Map<String, Double> groupByHour;
        if (realORHis.equals("实时")) {
            groupByHour = filteredData.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getRealUnifiedSettlementPointPrice()).doubleValue())));
        } else {
            groupByHour = filteredData.stream().collect(Collectors.groupingBy(x -> DateUtil.format(new Date(x.getTs().getTime() - 900000), "HH"), Collectors.summingDouble(item -> parseNumber(item.getHisUnifiedSettlementPointPrice()).doubleValue())));
        }
        double valueAvg = 0;
        //排序
        Map<String, Object> sortedMap = new HashMap<>();
        for (Map.Entry<String, Double> entry : groupByHour.entrySet()) {
            String key = String.valueOf(Integer.parseInt(entry.getKey()) + 1);
            sortedMap.put(key, BigDecimal.valueOf(entry.getValue()).setScale(2, RoundingMode.HALF_UP));
        }
        for (Map.Entry<String, Double> entry : groupByHour.entrySet()) {
            String key = String.valueOf(Integer.parseInt(entry.getKey()) + 1);
            double value = new BigDecimal(sortedMap.get(key).toString()).divide(BigDecimal.valueOf(4).multiply(new BigDecimal(filteredData.size()).divide(new BigDecimal(96), 2, RoundingMode.HALF_UP)), 2, RoundingMode.HALF_UP).doubleValue();
            sortedMap.put(key, value);
            valueAvg += value;
        }
        sortedMap.put("均值", BigDecimal.valueOf(valueAvg).divide(BigDecimal.valueOf(24), 2, RoundingMode.HALF_UP));
        //根据租户id获取省编码GS
        //tenantService.getTenant(SecurityFrameworkUtils.getLoginUserTenantId()).getTradingMarketCode()
        //当前租户该省份的所有峰谷平时段
        List<TimeSlotsDO> timeSlotsDOList = timeSlotsService.getListAll(tenantService.getTenant(SecurityFrameworkUtils.getLoginUserTenantId()).getTradingMarketCode());
        List<TimeSlotsDO> offPeak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("off-peak")).toList();
        List<TimeSlotsDO> valley = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("valley")).toList();
        List<TimeSlotsDO> peak = timeSlotsDOList.stream().filter(timeSlotsDO -> timeSlotsDO.getType().equals("peak")).toList();
        BigDecimal peakSum = BigDecimal.ZERO;
        for (TimeSlotsDO timeSlotsDO : peak) {
            peakSum = peakSum.add(BigDecimal.valueOf(groupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
        }
        BigDecimal valleySum = BigDecimal.ZERO;
        for (TimeSlotsDO timeSlotsDO : valley) {
            valleySum = valleySum.add(BigDecimal.valueOf(groupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
        }
        BigDecimal offPeakSum = BigDecimal.ZERO;
        for (TimeSlotsDO timeSlotsDO : offPeak) {
            offPeakSum = offPeakSum.add(BigDecimal.valueOf(groupByHour.get(timeSlotsDO.getStartTime().format(DateTimeFormatter.ofPattern("HH")))));
        }
        // 计算峰段平均值
        if (peak.size() > 0 && filteredData.size() / 96 > 0) {
            BigDecimal peakDivisor = BigDecimal.valueOf(peak.size() * 4L * (filteredData.size() / 96));
            sortedMap.put("峰段", peakSum.divide(peakDivisor, 2, RoundingMode.HALF_UP));
        } else {
            // 处理分母为零的情况，例如设置默认值或跳过
            sortedMap.put("峰段", BigDecimal.ZERO); // 或者根据业务需求设置其他值
        }

        // 计算平段平均值
        if (offPeak.size() > 0 && filteredData.size() / 96 > 0) {
            BigDecimal offPeakDivisor = BigDecimal.valueOf(offPeak.size() * 4L * (filteredData.size() / 96));
            sortedMap.put("平段", offPeakSum.divide(offPeakDivisor, 2, RoundingMode.HALF_UP));
        } else {
            // 处理分母为零的情况，例如设置默认值或跳过
            sortedMap.put("平段", BigDecimal.ZERO); // 或者根据业务需求设置其他值
        }

        // 计算谷段平均值
        if (valley.size() > 0 && filteredData.size() / 96 > 0) {
            BigDecimal valleyDivisor = BigDecimal.valueOf(valley.size() * 4L * (filteredData.size() / 96));
            sortedMap.put("谷段", valleySum.divide(valleyDivisor, 2, RoundingMode.HALF_UP));
        } else {
            // 处理分母为零的情况，例如设置默认值或跳过
            sortedMap.put("谷段", BigDecimal.ZERO); // 或者根据业务需求设置其他值
        }

//        sortedMap.put("峰段", peakSum.divide(BigDecimal.valueOf(peak.size() * 4L * (filteredData.size() / 96)), 2, RoundingMode.HALF_UP));
//        sortedMap.put("平段", offPeakSum.divide(BigDecimal.valueOf(offPeak.size() * 4L * (filteredData.size() / 96)), 2, RoundingMode.HALF_UP));
//        sortedMap.put("谷段", valleySum.divide(BigDecimal.valueOf(valley.size() * 4L * (filteredData.size() / 96)), 2, RoundingMode.HALF_UP));
        // 自定义比较器
        Comparator<String> customComparator = (key1, key2) -> {
            List<String> order = new ArrayList<>();
            for (int i = 1; i <= 24; i++) {
                order.add(String.valueOf(i));
            }
            order.add("均值");
            order.add("峰段");
            order.add("谷段");
            order.add("平段");

            int index1 = order.indexOf(key1);
            int index2 = order.indexOf(key2);

            return Integer.compare(index1, index2);
        };

        // 使用 TreeMap 进行排序
        Map<String, Object> sortedMap1 = new TreeMap<>(customComparator);
        sortedMap1.putAll(sortedMap);
        Map<String, Object> map = new HashMap<>();
        map.put("table", sortedMap1);
        map.put("echarts", result);
        map.put("slots", timeSlotsDOList);
        return map;
    }
}
