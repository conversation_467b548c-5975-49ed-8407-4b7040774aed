package cn.iocoder.yudao.module.et.dal.dataobject.tradingCalendar;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Accessors(chain = true)
@NoArgsConstructor
@Data
@TableName("jy_trading_announcement")
public class JyTradingAnnouncementDO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 交易名称
     */
    private String name;
    /**
     * 交易方式
     */
    private String type;
    /**
     * 交易状态
     */
    private String status;
    /**
     * 交易发布
     */
    private String released;
    /**
     * 申报开始时间
     */
    private LocalDateTime dateBegin;
    /**
     * 申报截止时间
     */
    private LocalDateTime dateEnd;
    private Integer thermalPower;
    private Integer greenPower;
    private Integer powerSales;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 区域公司 code
     */
    private String orgCode;
    /**
     * 数据爬取时间
     */
    private LocalDateTime gatherDate;
    /**
     * 修改时间
     */
    private LocalDateTime modificationTime;
}
