package cn.iocoder.yudao.module.et.service.init;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.et.dal.dataobject.spiderConf.SpiderConfDO;
import cn.iocoder.yudao.module.et.service.mongo.MongoService;
import cn.iocoder.yudao.module.et.service.spiderconf.SpiderConfService;
import cn.iocoder.yudao.module.et.thread.SpiderPageAnalyzeThread;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 初始化系统内存变量缓存结构
 */
@Slf4j
@Component
public class SysBeanCacheService implements InitializingBean {

    @Autowired
    private SpiderConfService spiderConfService;
    @Autowired
    private MongoService mongoService;
    @Autowired
    private ConfigurableApplicationContext ctx;

    //Map<TenantId, Map<bizCode, SpiderConf>
    public static Map<Long, Map<String, SpiderConfDO>> spiderConfMap = Maps.newHashMap();

    @Override
    public void afterPropertiesSet() throws Exception {
        long runTime = System.currentTimeMillis();
        log.info("初始化爬取页面解析类缓存结构开始……");
        this.initSpiderConfMap();
        log.info(StrUtil.format("初始化爬取页面解析类缓存结构完成. {} ms.", (System.currentTimeMillis() - runTime)));

//        this.spiderPageAnalyze();
//        log.info("爬取页面解析线程启动完成.");
    }

    /**
     * 初始化爬取页面解析类本地缓存
     */
    private void initSpiderConfMap() {
        List<SpiderConfDO> list = spiderConfService.findAllByUse();
        Map<String, SpiderConfDO> map = null;
        for(SpiderConfDO conf : list) {
            map = SysBeanCacheService.spiderConfMap.get(conf.getTenantId());
            if(map == null) {
                map = Maps.newHashMap();
                SysBeanCacheService.spiderConfMap.put(conf.getTenantId(), map);
            }
            map.put(conf.getSpiderCode(), conf);
        }
    }

    private void spiderPageAnalyze() {
        SpiderPageAnalyzeThread thread = new SpiderPageAnalyzeThread(mongoService, ctx, SysBeanCacheService.spiderConfMap);
        Thread t = new Thread(thread);
        t.start();
    }
}
