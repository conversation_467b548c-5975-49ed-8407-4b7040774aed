package cn.iocoder.yudao.module.et.dal.dataobject.tradingunit;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.sql.Timestamp;

/**
 * 交易单元 DO
 *
 * <AUTHOR>
 */
@TableName("trading_unit_daily")
@KeySequence("trading_unit_daily_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradingUnitDailyDO {

    /**
     * 时间
     */
    private Timestamp ts;

    /**
     * 场站id
     */
    private String unitId;


    /**
     * 中长期结算曲线
     */
    private Double mediumLongTermSettlementCurves;
    /**
     * 自计划
     */
    private Double selfPlan;
    /**
     * 预平衡计划
     */
    private Double prebalancePlan;
    /**
     * 可靠性计划
     */
    private Double reliabilityPlan;
    /**
     * 双边出清
     */
    private Double bilateralClearance;
    /**
     * 实时计划
     */
    private Double realPlan;
    /**
     * 实时上网scada电力
     */
    private Double realScadaPower;
    /**
     * 短期预测
     */
    private Double shortTermForecast;
    /**
     * 超短期预测
     */
    private Double ultraShortTermForecast;
    /**
     * 跨省区现货出清电力
     */
    private Double crossProvincialSpotClearingOfElectricity;
    /**
     * 实时出清依据
     */
    private Double realBasisClearance;


    public String getSelfPlan() {
        if (null == selfPlan) {
            return "--";
        } else {
            return selfPlan.toString();
        }
    }

    public String getPrebalancePlan() {
        if (null == prebalancePlan) {
            return "--";
        } else {
            return prebalancePlan.toString();
        }
    }

    public String getReliabilityPlan() {
        if (null == reliabilityPlan) {
            return "--";
        } else {
            return reliabilityPlan.toString();
        }
    }

    public String getBilateralClearance() {
        if (null == bilateralClearance) {
            return "--";
        } else {
            return bilateralClearance.toString();
        }
    }

    public String getRealPlan() {
        if (null == realPlan) {
            return "--";
        } else {
            return realPlan.toString();
        }
    }

    public String getRealScadaPower() {
        if (null == realScadaPower) {
            return "--";
        } else {
            return realScadaPower.toString();
        }
    }

    public String getShortTermForecast() {
        if (null == shortTermForecast) {
            return "--";
        } else {
            return shortTermForecast.toString();
        }
    }

    public String getUltraShortTermForecast() {
        if (null == ultraShortTermForecast) {
            return "--";
        } else {
            return ultraShortTermForecast.toString();
        }
    }

    public String getCrossProvincialSpotClearingOfElectricity() {
        if (null == crossProvincialSpotClearingOfElectricity) {
            return "--";
        } else {
            return crossProvincialSpotClearingOfElectricity.toString();
        }
    }

    public String getRealBasisClearance() {
        if (null == realBasisClearance) {
            return "--";
        } else {
            return realBasisClearance.toString();
        }
    }

    public String getMediumLongTermSettlementCurves() {
        if (null == mediumLongTermSettlementCurves) {
            return "--";
        } else {
            return mediumLongTermSettlementCurves.toString();
        }
    }


}