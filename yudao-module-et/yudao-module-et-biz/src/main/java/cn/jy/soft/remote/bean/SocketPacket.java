package cn.jy.soft.remote.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serial;
import java.util.Date;

/**
 * socket报文
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Document("socket_packet")
public class SocketPacket extends RemoteBase {

	@Serial
	private static final long serialVersionUID = -6752775754086586386L;

	private String id;
	/**
	 * 租户ID
	 */
	private Long tenantId;
	/**
	 * 数据归属用户唯一编码
	 */
	private String orgCode;
	/**
	 * 业务代码（爬取页面的唯一编码）
	 */
	private String bizCode;
	/**
	 * 业务时间
	 */
	private Date bizDate;
	/**
	 * 爬取时间
	 */
	private Date gatherTime;
	/**
	 * 数据主体
	 */
	private Object objData;
	/**
	 * jsonData
	 */
	private String data;

}
