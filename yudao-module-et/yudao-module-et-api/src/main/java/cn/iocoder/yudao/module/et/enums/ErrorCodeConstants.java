package cn.iocoder.yudao.module.et.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * ET 错误码枚举类
 */
public interface ErrorCodeConstants {


    //通用错误信息
    ErrorCode TIME_RANGE_PARAMS_LENGTH_ERROR = new ErrorCode(10000002, "时间区间入参不正确");
    ErrorCode NOT_EXIST_DATA = new ErrorCode(10000001, "未查询到数据");
    ErrorCode POWER_STATION_NOT_EXISTS = new ErrorCode(10000003, "场站信息不存在");
    ErrorCode DISTRICT_CODE_NOT_EXISTS = new ErrorCode(10000004, "行政区划不存在");
    ErrorCode DISPATCH_NODE_NOT_EXISTS = new ErrorCode(10000005, "交易单元不存在");
    ErrorCode DISTRICT_AREA_NOT_EXISTS = new ErrorCode(10000006, "省份对应算法类型不存在");
    ErrorCode TIME_SLOTS_NOT_EXISTS = new ErrorCode(10000007, "省份峰谷平时段管理不存在");
    ErrorCode SIMILAR_DAY_PREDICTION_NOT_EXISTS = new ErrorCode(10000008, "相似日表不存在");
    ErrorCode TRADING_MARKET_NOT_EXISTS = new ErrorCode(10000009, "交易不存在");
    ErrorCode STARTUP_AND_DOWN_UNIT_DETAIL_NOT_EXISTS = new ErrorCode(10000010, "必开必停机组明细信息不存在");
    ErrorCode SUBSTATION_NOT_EXISTS = new ErrorCode(10000011, "日前实时电价统计不存在");

    ErrorCode SPIDER_CONF_NOT_EXISTS = new ErrorCode(10000012, "爬取页面解析类表不存在");
    ErrorCode CONTRACTS_NOT_EXISTS = new ErrorCode(2000001, "合同不存在");
    ErrorCode CONTRACTS_EQUIPMENT_NOT_EXISTS = new ErrorCode(2000002, "合同设备不存在");
    ErrorCode CONTRACTS_MEMBER_NOT_EXISTS = new ErrorCode(2000003, "合同成员不存在");
    ErrorCode CONTRACTS_MONTH12_NOT_EXISTS = new ErrorCode(2000004, "合同月度信息不存在");
    ErrorCode CONTRACTS_POWER_CURVE_NOT_EXISTS = new ErrorCode(2000005, "合同发电曲线不存在");
    ErrorCode CONTRACTS_TIME24_NOT_EXISTS = new ErrorCode(2000006, "合同小时信息不存在");
    ErrorCode CONTRACTS_TYPE_NOT_EXISTS = new ErrorCode(2000007, "合同类型不存在");
    ErrorCode SERIES_CONTRACTS_NOT_EXISTS = new ErrorCode(2000008, "合同类型不存在");

    // ========== 表不存在相关 1-048-000-001 ==========
    ErrorCode TABLE_NOT_EXISTS = new ErrorCode(1048000001, "表不存在");
}
