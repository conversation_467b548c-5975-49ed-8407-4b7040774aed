server:
  port: 30010

--- #################### 数据库相关配置 ####################
spring:
  # 数据源配置项
  autoconfigure:
    exclude:
    #- org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration # 默认 local 环境，不开启 Quartz 的自动配置
    #- de.codecentric.boot.admin.server.config.AdminServerAutoConfiguration # 禁用 Spring Boot Admin 的 Server 的自动配置
    #- de.codecentric.boot.admin.server.ui.config.AdminServerUiAutoConfiguration # 禁用 Spring Boot Admin 的 Server UI 的自动配置
    #- de.codecentric.boot.admin.client.config.SpringBootAdminClientAutoConfiguration # 禁用 Spring Boot Admin 的 Client 的自动配置
  datasource:
    dynamic: # 多数据源配置
      hikari: # hikari 【连接池】相关的全局配置
        connection-timeout: 30000
        maximum-pool-size: 10
        idle-timeout: 600000
        pool-name: HikariCP
        is-auto-commit: true
      primary: master
      datasource:
        master:
          name: etadm-hlj
          url: jdbc:mysql://**************:3306/${spring.datasource.dynamic.datasource.master.name}?useSSL=false&allowPublicKeyRetrieval=true&useUnicode=true&characterEncoding=UTF-8&rewriteBatchedStatements=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true&useAffectedRows=true&autoReconnect=true&autoReconnectForPools=true
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: pe7FCKeWmGGY
          # 指定为HikariDataSource
          type: com.zaxxer.hikari.HikariDataSource
          # hikari连接池配置 对应 HikariConfig 配置属性类
          hikari:
            #最小空闲连接数
            minimum-idle: 5
            # 空闲连接存活最大时间，默认10分钟
            idle-timeout: 300000
            # 连接池最大连接数，默认是10
            maximum-pool-size: 500
            # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
            max-lifetime: 1800000
            # 数据库连接超时时间,默认30秒
            connection-timeout: 5000
            # 连接测试query
            connection-test-query: SELECT 1
            validation-timeout: 3000
        tdengine:
          url: ********************************************
          username: root
          password: taosdata
          driver-class-name: com.taosdata.jdbc.TSDBDriver
          # 指定为HikariDataSource
          type: com.zaxxer.hikari.HikariDataSource
          # hikari连接池配置
          hikari:
            #最小空闲连接数
            minimum-idle: 5
            # 空闲连接存活最大时间，默认10分钟
            idle-timeout: 300000
            # 连接池最大连接数，默认是10
            maximum-pool-size: 500
            # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
            max-lifetime: 1800000
            # 数据库连接超时时间,默认30秒
            connection-timeout: 5000
            # 连接测试query
            connection-test-query: SELECT 1
            validation-timeout: 3000

            # 需要和sharding.yml--->databaseName的值要一致,不然无法识别数据源
        shardingsphereDB:
          driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
          url: jdbc:shardingsphere:classpath:application-shardingsphere-prod.yaml
          # 指定为HikariDataSource
          type: com.zaxxer.hikari.HikariDataSource
          # hikari连接池配置
          hikari:
            #最小空闲连接数
            minimum-idle: 5
            # 空闲连接存活最大时间，默认10分钟
            idle-timeout: 300000
            # 连接池最大连接数，默认是10
            maximum-pool-size: 500
            # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
            max-lifetime: 1800000
            # 数据库连接超时时间,默认30秒
            connection-timeout: 5000
            # 连接测试query
            connection-test-query: SELECT 1
            validation-timeout: 3000

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: 127.0.0.1 # 地址
      port: 36379 # 端口
      database: 1 # 数据库索引
      password: redis_3pX64C # 密码，建议生产环境开启
    mongodb:
      host: **************
      port:
      database: etadm_spider_hlj
      username: root
      password: Syjy*root
      authentication-database: admin

--- #################### 定时任务相关配置 ####################

# Quartz 配置项，对应 QuartzProperties 配置类
spring:
  quartz:
    auto-startup: true # 本地开发环境，尽量不要开启 Job
    scheduler-name: schedulerName # Scheduler 名字。默认为 schedulerName
    job-store-type: jdbc # Job 存储器类型。默认为 memory 表示内存，可选 jdbc 使用数据库。
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    properties: # 添加 Quartz Scheduler 附加属性，更多可以看 http://www.quartz-scheduler.org/documentation/2.4.0-SNAPSHOT/configuration.html 文档
      org:
        quartz:
          # Scheduler 相关配置
          scheduler:
            instanceName: schedulerName
            instanceId: AUTO # 自动生成 instance ID
          # JobStore 相关配置
          jobStore:
            # JobStore 实现类。可见博客：https://blog.csdn.net/weixin_42458219/article/details/122247162
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            isClustered: true # 是集群模式
            clusterCheckinInterval: 15000 # 集群检查频率，单位：毫秒。默认为 15000，即 15 秒
            misfireThreshold: 60000 # misfire 阀值，单位：毫秒。
          # 线程池相关配置
          threadPool:
            threadCount: 25 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级
            class: org.quartz.simpl.SimpleThreadPool # 线程池类型
    jdbc: # 使用 JDBC 的 JobStore 的时候，JDBC 的配置
      initialize-schema: NEVER # 是否自动使用 SQL 初始化 Quartz 表结构。这里设置成 never ，我们手动创建表结构。


--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。
  health:
    redis:
      enabled: false

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        url: http://127.0.0.1:${server.port}/${spring.boot.admin.context-path} # 设置 Spring Boot Admin Server 地址
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Spring

# 日志文件配置
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    cn.iocoder.yudao.module.bpm.dal.mysql: ERROR
    cn.iocoder.yudao.module.infra.dal.mysql: ERROR
    cn.iocoder.yudao.module.infra.dal.mysql.logger.ApiErrorLogMapper: ERROR # 配置 ApiErrorLogMapper 的日志级别为 info，避免和 GlobalExceptionHandler 重复打印
    cn.iocoder.yudao.module.infra.dal.mysql.job.JobLogMapper: ERROR # 配置 JobLogMapper 的日志级别为 info
    cn.iocoder.yudao.module.infra.dal.mysql.file.FileConfigMapper: ERROR # 配置 FileConfigMapper 的日志级别为 info
    cn.iocoder.yudao.module.system.dal.mysql: ERROR
    cn.iocoder.yudao.module.system.dal.mysql.sms.SmsChannelMapper: ERROR # 配置 SmsChannelMapper 的日志级别为 info
    org.springframework.context.support.PostProcessorRegistrationDelegate: ERROR # TODO 芋艿：先禁用，Spring Boot 3.X 存在部分错误的 WARN 提示

debug: false

--- #################### 芋道相关配置 ####################

# 芋道配置项，设置当前项目所有自定义的配置
yudao:
  captcha:
    enable: false # 本地环境，暂时关闭图片验证码，方便登录等接口的测试；
  security:
    mock-enable: true
  access-log: # 访问日志的配置项
    enable: false
  demo: false # 关闭演示模式

--- #################### 微信公众号相关配置 ####################
wx:
  mp:
    # 公众号配置(必填)
    app-id: 2
    secret: 2
    # 存储配置，解决 AccessToken 的跨节点的共享
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wx # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台
  miniapp: # 小程序配置（必填），参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-miniapp-spring-boot-starter/README.md 文档
    appid: 1
    secret: 1
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wa # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台
