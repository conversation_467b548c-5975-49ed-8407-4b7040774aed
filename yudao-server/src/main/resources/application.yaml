spring:
  application:
    name: etadm-server

  profiles:
    active: prod

  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。

  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      enabled: true
      max-file-size: 256MB # 单个文件大小
      max-request-size: 512MB # 设置总上传的文件大小

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 Date 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

server:
  servlet:
    encoding:
      enabled: true
      charset: UTF-8 # 必须设置 UTF-8，避免 WebFlux 流式返回（AI 场景）会乱码问题
      force: true
    # 应用的访问路径
    context-path: /
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
    io-threads: 8
    # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
    worker-threads: 256
    # 是否分配的直接内存
    direct-buffers: true

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档

knife4j:
  enable: true
  setting:
    language: zh_cn


mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    banner: false # 关闭控制台的 Banner 打印
  type-aliases-package: ${yudao.info.base-package}.module.*.dal.dataobject
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成

mybatis-plus-join:
  banner: false # 是否打印 mybatis plus join banner，默认true
  sub-table-logic: true # 全局启用副表逻辑删除，默认true。关闭后关联查询不会加副表逻辑删除
  ms-cache: true # 拦截器MappedStatement缓存，默认 true
  table-alias: t # 表别名(默认 t)
  logic-del-type: on # 副表逻辑删除条件的位置，支持 WHERE、ON，默认 ON

# Spring Data Redis 配置
spring:
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度

# VO 转换（数据翻译）相关
easy-trans:
  is-enable-global: true # 启用全局翻译（拦截所有 SpringMVC ResponseBody 进行自动翻译 )。如果对于性能要求很高可关闭此配置，或通过 @IgnoreTrans 忽略某个接口

--- #################### 验证码相关配置 ####################

aj:
  captcha:
    jigsaw: classpath:images/jigsaw # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    pic-click: classpath:images/pic-click # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    cache-type: redis # 缓存 local/redis...
    cache-number: 1000 # local 缓存的阈值,达到这个值，清除缓存
    timing-clear: 180 # local定时清除过期缓存(单位秒),设置为0代表不执行
    type: blockPuzzle # 验证码类型 default两种都实例化。 blockPuzzle 滑块拼图 clickWord 文字点选
    water-mark: ET # 右下角水印文字(我的水印)，可使用 https://tool.chinaz.com/tools/unicode.aspx 中文转 Unicode，Linux 可能需要转 unicode
    interference-options: 0 # 滑动干扰项(0/1/2)
    req-frequency-limit-enable: false # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 5 # 验证失败 5 次，get接口锁定
    req-get-lock-seconds: 10 # 验证失败后，锁定时间间隔
    req-get-minute-limit: 30 # get 接口一分钟内请求数限制
    req-check-minute-limit: 60 # check 接口一分钟内请求数限制
    req-verify-minute-limit: 60 # verify 接口一分钟内请求数限制


--- #################### 芋道相关配置 ####################

yudao:
  info:
    version: 1.0.0
    base-package: cn.iocoder.yudao
  web:
    admin-ui:
      url: http://dashboard.yudao.iocoder.cn # Admin 管理后台 UI 的地址
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  security:
    permit-all_urls:
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录
      - /admin-api/et/**
      - /admin-api/api/**
  websocket:
    enable: true # websocket的开关
    path: /et/ws # 路径
    sender-type: local # 消息发送的类型，可选值为 local、redis、rocketmq、kafka、rabbitmq
    sender-rocketmq:
      topic: ${spring.application.name}-websocket # 消息发送的 RocketMQ Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 RocketMQ Consumer Group
    sender-rabbitmq:
      exchange: ${spring.application.name}-websocket-exchange # 消息发送的 RabbitMQ Exchange
      queue: ${spring.application.name}-websocket-queue # 消息发送的 RabbitMQ Queue
    sender-kafka:
      topic: ${spring.application.name}-websocket # 消息发送的 Kafka Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 Kafka Consumer Group
  swagger:
    title: 电力交易辅助决策系统
    description: 提供管理后台、用户 App 的所有功能
    version: ${yudao.info.version}
    url: ${yudao.web.admin-ui.url}
    email: <EMAIL>
  codegen:
    base-package: ${yudao.info.base-package}
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
      - /admin-api/system/tenant/get-id-by-name # 基于名字获取租户，不许带租户编号
      - /admin-api/system/tenant/get-by-website # 基于域名获取租户，不许带租户编号
      - /admin-api/system/captcha/get # 获取图片验证码，和租户无关
      - /admin-api/system/captcha/check # 校验图片验证码，和租户无关
      - /admin-api/infra/file/*/get/** # 获取图片，和租户无关
      - /admin-api/system/sms/callback/* # 短信回调接口，无法带上租户编号
      - /admin-api/api/**
    ignore-tables:
      - system_tenant
      - system_tenant_package
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - system_sms_channel
      - system_sms_template
      - system_sms_log
      - system_sensitive_word
      - system_oauth2_client
      - system_mail_account
      - system_mail_template
      - system_mail_log
      - system_notify_template
      - infra_codegen_column
      - infra_codegen_table
      - infra_config
      - infra_file_config
      - infra_file
      - infra_file_content
      - infra_job
      - infra_job_log
      - infra_job_log
      - infra_data_source_config
      - company_unit
      - trading_unit
      - jy_district_code
    ignore-caches:
      - permission_menu_ids
      - oauth_client
      - notify_template
      - mail_account
      - mail_template
      - sms_template
  sms-code: # 短信验证码相关的配置项
    expire-times: 10m
    send-frequency: 1m
    send-maximum-quantity-per-day: 10
    begin-code: 9999 # 这里配置 9999 的原因是，测试方便。
    end-code: 9999 # 这里配置 9999 的原因是，测试方便。


debug: false