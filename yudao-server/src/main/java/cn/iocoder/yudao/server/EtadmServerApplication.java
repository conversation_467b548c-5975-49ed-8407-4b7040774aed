package cn.iocoder.yudao.server;

import com.github.fppt.jedismock.RedisServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 项目的启动类
 * <p>
 * 如果你碰到启动的问题，请认真阅读 https://doc.iocoder.cn/quick-start/ 文章
 * 如果你碰到启动的问题，请认真阅读 https://doc.iocoder.cn/quick-start/ 文章
 * 如果你碰到启动的问题，请认真阅读 https://doc.iocoder.cn/quick-start/ 文章
 *
 * <AUTHOR>
 */
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${yudao.info.base-package}
@SpringBootApplication(scanBasePackages = {"${yudao.info.base-package}.server", "${yudao.info.base-package}.module"})
public class EtadmServerApplication {

    public static void main(String[] args) {
        // 启动 Redis 服务

        RedisServer redisServer = new RedisServer(36379);
        try {
            redisServer.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        SpringApplication.run(EtadmServerApplication.class, args);
//        ConfigurableApplicationContext ctx = SpringApplication.run(EtadmServerApplication.class, args);
    }
}
