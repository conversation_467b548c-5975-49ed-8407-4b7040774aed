package cn.iocoder.yudao.module.system.api.tenant;

import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 多租户的 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class TenantApiImpl implements TenantApi {

    @Resource
    private TenantService tenantService;

    @Override
    public List<Long> getTenantIdList() {
        return tenantService.getTenantIdList();
    }

    @Override
    public void validateTenant(Long id) {
        tenantService.validTenant(id);
    }

    @Override
    public String getTradingMarketCode(Long id) {
        return tenantService.getTradingMarketCode(id);
    }


}
