package cn.iocoder.yudao.module.system.api.dept.dto;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import lombok.Data;

/**
 * 部门 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class DeptRespDTO {

    /**
     * 部门编号
     */
    private Long id;
    /**
     * 部门名称
     */
    private String name;
    /**
     * 父部门编号
     */
    private Long parentId;
    /**
     * 负责人的用户编号
     */
    private Long leaderUserId;
    /**
     * 部门状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 区域公司 code
     */
    private String orgCode;
    /**
     * 是否是场站 1为是  如果是场站 则有下属的交易单元
     */
    private Integer powerStation;

    private Long tenantId;


    /**
     * 交易市场所在省份
     */
    private Integer districtCode;
}
